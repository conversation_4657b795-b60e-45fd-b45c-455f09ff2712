// BaseToast.patched.jsx - Web compatibility for react-native-toast-message
import React from 'react';

import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 4,
    margin: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    maxWidth: '90%',
    minWidth: 300,
  },
  success: {
    borderLeftColor: '#2ecc71',
    borderLeftWidth: 5,
  },
  error: {
    borderLeftColor: '#e74c3c',
    borderLeftWidth: 5,
  },
  info: {
    borderLeftColor: '#3498db',
    borderLeftWidth: 5,
  },
  warning: {
    borderLeftColor: '#f39c12',
    borderLeftWidth: 5,
  },
  text: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
});

const BaseToast = ({ text1, type = 'success', onPress }) => {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.9}>
      <View style={[styles.base, styles[type]]}>
        <Text style={styles.text}>{text1}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default BaseToast;