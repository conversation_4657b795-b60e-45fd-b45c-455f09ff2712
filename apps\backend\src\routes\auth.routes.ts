// backend/src/routes/auth.routes.ts
import express from 'express';
import { AuthController } from '../controllers/auth.controller';
import { asyncHandler } from '../utils/asyncHandler';
import { authMiddleware } from '../middleware/auth.middleware';
import { validateBody } from '../middleware/validation.middleware';
import {
  UserRegistrationSchema,
  UserLoginSchema,
  UserUpdateSchema
} from '../schemas/validation.schemas';
import { rateLimitMiddleware, rateLimits } from '../middleware/rateLimiting.middleware';

const router = express.Router();

// 🔐 Auth Routes with Enhanced Validation
router.post('/signup',
  rateLimitMiddleware(rateLimits.auth),
  validate<PERSON><PERSON>(UserRegistrationSchema),
  asyncHandler(AuthController.signup)
);

router.post('/login',
  rateLimitMiddleware(rateLimits.auth),
  validateBody(UserLoginSchema),
  asyncHandler(AuthController.login)
);

router.get('/me',
  authMiddleware,
  asyncHandler(AuthController.getProfile)
);

router.put('/me',
  authMiddleware,
  validateBody(UserUpdateSchema),
  asyncHandler(AuthController.updateProfile)
);

// 🔄 Refresh token endpoint
router.post('/refresh',
  rateLimitMiddleware(rateLimits.auth),
  asyncHandler(AuthController.refreshToken)
);

// 🚪 Logout endpoint
router.post('/logout',
  authMiddleware,
  asyncHandler(AuthController.logout)
);

// 🔑 Forgot password endpoint
router.post('/forgot-password',
  rateLimitMiddleware(rateLimits.auth),
  asyncHandler(AuthController.forgotPassword)
);

export default router;
