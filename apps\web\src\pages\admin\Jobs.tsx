// apps/web/src/pages/admin/Jobs.tsx

import React, { useEffect, useState } from 'react';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { LoadingState } from '../../components/ui/LoadingState';
import { EmptyState } from '../../components/ui/EmptyState';
import { PageContainer } from '../../components/ui/PageContainer';
import { AdminAPI, Job } from '../../api/admin.api';
import { toast } from 'react-toastify';

// Job interface is now imported from AdminAPI

const JobsPage: React.FC = () => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const response = await AdminAPI.getJobs();
      setJobs(response.jobs);
    } catch (err: any) {
      console.error('Failed to fetch jobs:', err);
      toast.error('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  const handleFreeze = async (jobId: string) => {
    try {
      const reason = prompt('Enter freeze reason:');
      if (!reason) return;
      await AdminAPI.freezePayment(jobId, reason);
      toast.success('Payment frozen');
      fetchJobs();
    } catch (error) {
      console.error('Freeze failed:', error);
      toast.error('Freeze failed');
    }
  };

  const handleUnfreeze = async (jobId: string) => {
    try {
      await AdminAPI.unfreezePayment(jobId);
      toast.success('Payment unfrozen');
      fetchJobs();
    } catch (error) {
      console.error('Unfreeze failed:', error);
      toast.error('Unfreeze failed');
    }
  };

  const handleManualRelease = async (jobId: string) => {
    try {
      await AdminAPI.releasePaymentManually(jobId);
      toast.success('Payment released manually');
      fetchJobs();
    } catch (error) {
      console.error('Manual release failed:', error);
      toast.error('Manual release failed');
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  return (
    <PageContainer>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Job Management</h1>
            <p className="text-gray-600 mt-1">Manage jobs, payments, and releases</p>
          </div>
        </div>

        {loading ? (
          <LoadingState message="Loading jobs..." />
        ) : jobs.length === 0 ? (
          <EmptyState
            title="No jobs found"
            message="There are no jobs to display at the moment."
            actionTitle="Refresh"
            onAction={fetchJobs}
          />
        ) : (
        <div className="space-y-4">
          {jobs.map((job) => (
            <div key={job.id} className="bg-white border border-gray-200 rounded-2xl p-4 md:p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex flex-col lg:flex-row lg:items-start gap-4">
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-gray-900 text-base md:text-lg mb-2">{job.issue}</h3>
                  <div className="space-y-1 text-sm text-gray-600">
                    <p className="flex items-center gap-2">
                      <span className="font-medium">Scheduled:</span>
                      <span>{new Date(job.scheduledAt).toLocaleString()}</span>
                    </p>
                    <p className="flex items-center gap-2">
                      <span className="font-medium">Customer:</span>
                      <span>{job.homeowner.fullName}</span>
                    </p>
                    {job.technician && (
                      <p className="flex items-center gap-2">
                        <span className="font-medium">Technician:</span>
                        <span>{job.technician.user.fullName}</span>
                      </p>
                    )}
                  </div>
                  <div className="mt-3 flex flex-wrap gap-2">
                    <Badge className="text-xs">{job.status}</Badge>
                    {job.payment?.isReleased && (
                      <Badge className="bg-green-500 text-white text-xs">Released</Badge>
                    )}
                    {job.payment?.isFrozen && (
                      <Badge className="bg-red-500 text-white text-xs">Frozen</Badge>
                    )}
                  </div>
                </div>

                <div className="flex flex-row lg:flex-col gap-2 lg:w-48">
                  {!job.payment?.isReleased && (
                    <>
                      {!job.payment?.isFrozen ? (
                        <Button
                          onClick={() => handleFreeze(job.id)}
                          className="flex-1 lg:w-full text-sm"
                          size="sm"
                        >
                          Freeze
                        </Button>
                      ) : (
                        <Button
                          onClick={() => handleUnfreeze(job.id)}
                          variant="outline"
                          className="flex-1 lg:w-full text-sm"
                          size="sm"
                        >
                          Unfreeze
                        </Button>
                      )}
                      <Button
                        onClick={() => handleManualRelease(job.id)}
                        variant="secondary"
                        className="flex-1 lg:w-full text-sm"
                        size="sm"
                      >
                        Manual Release
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
        )}
      </div>
    </PageContainer>
  );
};

export default JobsPage;
