// Toast.patched.jsx - Web compatibility for react-native-toast-message
import React, { useEffect, useRef, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Animated } from 'react-native';

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10000,
    elevation: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  toast: {
    flexDirection: 'row',
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 4,
    margin: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    maxWidth: '90%',
    minWidth: 300,
  },
  success: {
    borderLeftColor: '#2ecc71',
    borderLeftWidth: 5,
  },
  error: {
    borderLeftColor: '#e74c3c',
    borderLeftWidth: 5,
  },
  info: {
    borderLeftColor: '#3498db',
    borderLeftWidth: 5,
  },
  warning: {
    borderLeftColor: '#f39c12',
    borderLeftWidth: 5,
  },
  text: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
});

const Toast = ({ config = {}, ...rest }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [type, setType] = useState('success');
  const [position, setPosition] = useState('top');
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const timer = useRef(null);

  const hide = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: false,
    }).start(() => {
      setIsVisible(false);
    });
  };

  const show = (options) => {
    if (timer.current) {
      clearTimeout(timer.current);
    }

    setMessage(options.text1 || '');
    setType(options.type || 'success');
    setPosition(options.position || 'top');
    setIsVisible(true);

    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: false,
    }).start();

    timer.current = setTimeout(hide, options.visibilityTime || 3000);
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.Toast = { show };
    }
    return () => {
      if (typeof window !== 'undefined') {
        delete window.Toast;
      }
    };
  }, []);

  if (!isVisible) {
    return null;
  }

  return React.createElement(
    Animated.View,
    {
      style: [
        styles.container,
        { opacity: fadeAnim },
        position === 'bottom' ? { bottom: 0, top: 'auto' } : { top: 0 },
      ]
    },
    React.createElement(
      TouchableOpacity,
      { onPress: hide, activeOpacity: 0.9 },
      React.createElement(
        View,
        { style: [styles.toast, styles[type]] },
        React.createElement(Text, { style: styles.text }, message)
      )
    )
  );
};

Toast.show = (options) => {
  if (typeof window !== 'undefined' && window.Toast) {
    window.Toast.show(options);
  }
};

Toast.hide = () => {
  if (typeof window !== 'undefined' && window.Toast) {
    window.Toast.hide();
  }
};

Toast.setRef = () => {};

export default Toast;