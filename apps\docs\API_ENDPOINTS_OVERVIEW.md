# CTRON Home API Endpoints Overview

**Base URL**: `http://localhost:3001/api`  
**Authentication**: JWT Bearer Token  
**Content-Type**: `application/json`

## Authentication Endpoints (`/api/auth`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/login` | User login | No |
| POST | `/register` | User registration | No |
| POST | `/refresh` | Refresh JWT token | No |
| POST | `/logout` | User logout | Yes |
| POST | `/forgot-password` | Request password reset | No |
| POST | `/reset-password` | Reset password with token | No |

## User Management (`/api/users`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/profile` | Get user profile | Yes |
| PUT | `/profile` | Update user profile | Yes |
| DELETE | `/profile` | Delete user account | Yes |
| GET | `/` | List users (Admin only) | Yes |

## Technician Management (`/api/technicians`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | List technicians | Yes |
| GET | `/:id` | Get technician details | Yes |
| PUT | `/:id` | Update technician profile | Yes |
| POST | `/:id/approve` | Approve technician (Admin) | Yes |
| POST | `/:id/reject` | Reject technician (Admin) | Yes |
| PUT | `/:id/availability` | Update availability | Yes |

## Job Management (`/api/jobs`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | List jobs | Yes |
| POST | `/` | Create new job | Yes |
| GET | `/:id` | Get job details | Yes |
| PUT | `/:id` | Update job | Yes |
| DELETE | `/:id` | Delete job | Yes |
| POST | `/:id/accept` | Accept job (Technician) | Yes |
| POST | `/:id/complete` | Mark job complete | Yes |
| POST | `/:id/cancel` | Cancel job | Yes |

## Payment Management (`/api/payments`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/create-intent` | Create payment intent | Yes |
| POST | `/confirm` | Confirm payment | Yes |
| GET | `/:id` | Get payment details | Yes |
| POST | `/:id/release` | Release payment (Admin) | Yes |
| POST | `/:id/freeze` | Freeze payment (Admin) | Yes |

## Review System (`/api/reviews`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | List reviews | Yes |
| POST | `/` | Create review | Yes |
| GET | `/:id` | Get review details | Yes |
| PUT | `/:id` | Update review | Yes |
| DELETE | `/:id` | Delete review | Yes |

## Chat System (`/api/chat`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/job/:jobId` | Get or create chat for job | Yes |
| POST | `/:chatId/messages` | Send message | Yes |
| GET | `/:chatId/messages` | Get message history | Yes |
| PUT | `/messages/:messageId/read` | Mark message as read | Yes |
| GET | `/user/:userId` | Get user's active chats | Yes |

## Notifications (`/api/notifications`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/register-token` | Register push token | Yes |
| DELETE | `/unregister-token` | Unregister push token | Yes |
| POST | `/send` | Send notification (Admin) | Yes |
| GET | `/` | Get notification history | Yes |
| PATCH | `/:id/read` | Mark notification as read | Yes |
| PATCH | `/read-all` | Mark all notifications as read | Yes |
| GET | `/unread-count` | Get unread count | Yes |

## AI Assistant (`/api/ai`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/query` | Send query to GPT assistant | Yes |
| GET | `/templates` | Get query templates by role | Yes |
| GET | `/history` | Get query history | Yes |

## Admin Dashboard (`/api/dashboard`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/stats` | Get dashboard statistics | Yes (Admin) |
| GET | `/revenue` | Get revenue analytics | Yes (Admin) |
| GET | `/jobs/recent` | Get recent jobs | Yes (Admin) |
| GET | `/technicians/pending` | Get pending technicians | Yes (Admin) |

## Settings Management (`/api/settings`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | Get system settings | Yes (Admin) |
| PUT | `/` | Update system settings | Yes (Admin) |
| GET | `/grace-period` | Get grace period setting | Yes (Admin) |
| PUT | `/grace-period` | Update grace period | Yes (Admin) |

## File Upload (`/api/upload`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/image` | Upload image to S3 | Yes |
| POST | `/document` | Upload document to S3 | Yes |
| DELETE | `/:key` | Delete file from S3 | Yes |

## Webhooks (`/api/webhook`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/stripe` | Stripe webhook handler | No (Webhook) |

## Health Check

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | API status | No |
| GET | `/api/health` | Health check | No |

## Authentication

All protected endpoints require a JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Role-Based Access Control

- **HOMEOWNER**: Can create jobs, make payments, leave reviews
- **TECHNICIAN**: Can view/accept jobs, update job status, chat with homeowners
- **ADMIN**: Full access to all endpoints, can manage users and system settings

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error type",
  "message": "Human readable error message",
  "details": "Additional error details (optional)"
}
```

## Rate Limiting

- **Auth endpoints**: 5 requests per minute
- **Chat endpoints**: 100 requests per minute
- **General API**: 1000 requests per hour

## Real-time Features (Socket.IO)

**Connection URL**: `http://localhost:3001`

### Events

#### Job Events
- `job:created` - New job created
- `job:updated` - Job status updated
- `job:assigned` - Job assigned to technician

#### Chat Events
- `chat:message` - New chat message
- `chat:typing` - User typing indicator
- `chat:read` - Message read status

#### Notification Events
- `notification:new` - New notification
- `notification:read` - Notification read

## Notes

- All timestamps are in ISO 8601 format
- File uploads support JPEG, PNG, and WebP formats
- Maximum file size: 10MB
- API responses include request IDs for debugging
- Comprehensive logging available in development mode

---

**Last Updated**: January 16, 2025  
**API Version**: 1.0.0