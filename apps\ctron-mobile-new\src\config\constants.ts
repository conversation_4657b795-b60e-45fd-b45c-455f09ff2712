/**
 * CTRON Home - Application Constants
 * Centralized configuration for timeouts, limits, and other hardcoded values
 */

// API Configuration
export const API_CONSTANTS = {
  // Timeouts (in milliseconds)
  REQUEST_TIMEOUT: 10000, // 10 seconds
  SOCKET_TIMEOUT: 5000,   // 5 seconds
  AUTH_TIMEOUT: 10000,    // 10 seconds for auth operations
  
  // Retry Configuration
  RECONNECTION_DELAY: 1000,     // 1 second
  RECONNECTION_DELAY_MAX: 5000, // 5 seconds
  RECONNECTION_ATTEMPTS: 5,
  
  // Rate Limiting
  MAX_REQUESTS_PER_MINUTE: 60,
  RATE_LIMIT_WINDOW: 60000, // 1 minute
} as const;

// UI Constants
export const UI_CONSTANTS = {
  // Animation Durations (in milliseconds)
  ANIMATION_DURATION_SHORT: 200,
  ANIMATION_DURATION_MEDIUM: 500,
  ANIMATION_DURATION_LONG: 1000,
  
  // Toast/Notification Durations
  TOAST_DURATION_SHORT: 3000,  // 3 seconds
  TOAST_DURATION_MEDIUM: 4000, // 4 seconds
  TOAST_DURATION_LONG: 5000,   // 5 seconds
  
  // Delays
  SPLASH_SCREEN_DELAY: 3000,   // 3 seconds
  DEBOUNCE_DELAY: 300,         // 300ms for search/input debouncing
  ACCESSIBILITY_DELAY: 500,    // 500ms for accessibility actions
  
  // Z-Index Values
  Z_INDEX_MODAL: 1000,
  Z_INDEX_TOAST: 10000,
  Z_INDEX_OVERLAY: 9999,
} as const;

// Validation Constants
export const VALIDATION_CONSTANTS = {
  // Text Length Limits
  MAX_DESCRIPTION_LENGTH: 2000,
  MAX_MESSAGE_LENGTH: 2000,
  MAX_JOB_DESCRIPTION_LENGTH: 1000,
  MAX_ADMIN_NOTES_LENGTH: 1000,
  MAX_CHAT_MESSAGE_LENGTH: 1000,
  MAX_QUERY_LENGTH: 1000,
  
  // Numeric Limits
  MAX_PAYMENT_AMOUNT: 10000,    // £10,000
  MAX_FILE_SIZE: 10485760,      // 10MB in bytes
  MAX_PAGINATION_LIMIT: 100,
  DEFAULT_PAGINATION_LIMIT: 20,
  
  // Minimum Values
  MIN_DESCRIPTION_LENGTH: 1,
  MIN_PASSWORD_LENGTH: 8,
  MIN_QUERY_LENGTH: 1,
} as const;

// Location Constants
export const LOCATION_CONSTANTS = {
  // Tracking Configuration
  TRACKING_INTERVAL: 30000,        // 30 seconds
  LOCATION_TIMEOUT: 10000,         // 10 seconds
  LOCATION_MAXIMUM_AGE: 10000,     // 10 seconds
  
  // Distance Calculations
  EARTH_RADIUS_KM: 6371,           // Earth's radius in kilometers
  NEARBY_JOBS_RADIUS_KM: 50,       // 50km radius for nearby jobs
  
  // Accuracy Thresholds
  HIGH_ACCURACY_THRESHOLD: 100,     // meters
  MEDIUM_ACCURACY_THRESHOLD: 500,   // meters
} as const;

// Time Constants
export const TIME_CONSTANTS = {
  // Milliseconds
  ONE_SECOND: 1000,
  ONE_MINUTE: 60 * 1000,
  ONE_HOUR: 60 * 60 * 1000,
  ONE_DAY: 24 * 60 * 60 * 1000,
  ONE_WEEK: 7 * 24 * 60 * 60 * 1000,
  ONE_MONTH: 30 * 24 * 60 * 60 * 1000,
  
  // Token Expiry
  TOKEN_CLEANUP_THRESHOLD: 30 * 24 * 60 * 60 * 1000, // 30 days
  TOKEN_EXPIRY_DURATION: 7 * 24 * 60 * 60 * 1000,    // 7 days
  
  // Job Scheduling
  URGENT_JOB_THRESHOLD: 24 * 60 * 60 * 1000,         // 24 hours
  AUTO_RELEASE_THRESHOLD: 24 * 60 * 60 * 1000,       // 24 hours
} as const;

// Color Constants (for fallbacks when theme is not available)
export const COLOR_CONSTANTS = {
  // Primary Colors
  PRIMARY_BLUE: '#6366F1',
  PRIMARY_PURPLE: '#9273EC',
  
  // Status Colors
  SUCCESS_GREEN: '#059669',
  WARNING_ORANGE: '#FF9500',
  ERROR_RED: '#EF4444',
  INFO_BLUE: '#2196F3',
  
  // Neutral Colors
  BLACK: '#000000',
  WHITE: '#FFFFFF',
  GRAY_500: '#6B7280',
  GRAY_700: '#374151',
  
  // Background Colors
  BACKGROUND_DARK: '#0F1419',
  BACKGROUND_SECONDARY: '#16213E',
} as const;

// File Upload Constants
export const UPLOAD_CONSTANTS = {
  // File Size Limits
  MAX_FILE_SIZE: 10485760,         // 10MB
  MAX_IMAGE_SIZE: 5242880,         // 5MB
  
  // Allowed File Types
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'text/plain'],
  
  // Upload Timeouts
  UPLOAD_TIMEOUT: 30000,           // 30 seconds
  UPLOAD_RETRY_ATTEMPTS: 3,
} as const;

// Development Constants
export const DEV_CONSTANTS = {
  // Default Ports
  DEFAULT_BACKEND_PORT: 3001,
  DEFAULT_WEB_PORT: 5173,
  DEFAULT_METRO_PORT: 8081,
  
  // Development URLs
  DEFAULT_LOCALHOST: 'localhost',
  DEFAULT_DEV_HOST: '0.0.0.0',
} as const;

// Export all constants as a single object for convenience
export const CONSTANTS = {
  API: API_CONSTANTS,
  UI: UI_CONSTANTS,
  VALIDATION: VALIDATION_CONSTANTS,
  LOCATION: LOCATION_CONSTANTS,
  TIME: TIME_CONSTANTS,
  COLOR: COLOR_CONSTANTS,
  UPLOAD: UPLOAD_CONSTANTS,
  DEV: DEV_CONSTANTS,
} as const;

// Type exports for TypeScript
export type ApiConstants = typeof API_CONSTANTS;
export type UiConstants = typeof UI_CONSTANTS;
export type ValidationConstants = typeof VALIDATION_CONSTANTS;
export type LocationConstants = typeof LOCATION_CONSTANTS;
export type TimeConstants = typeof TIME_CONSTANTS;
export type ColorConstants = typeof COLOR_CONSTANTS;
export type UploadConstants = typeof UPLOAD_CONSTANTS;
export type DevConstants = typeof DEV_CONSTANTS;
export type AllConstants = typeof CONSTANTS;