#!/usr/bin/env node

/**
 * CTRON Mobile - Socket.IO Client Issue Fixer
 * 
 * This script fixes the Node.js module import issues with socket.io-client
 * Run with: node scripts/fix-socketio-issue.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 CTRON Socket.IO Client Issue Fixer');
console.log('=====================================');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ Error: Please run this script from the project root directory');
  process.exit(1);
}

// Step 1: Check if Node polyfills exist
console.log('\n📦 Step 1: Checking Node.js polyfills...');
const polyfillsDir = 'node-polyfills';
const requiredPolyfills = ['tty.js', 'util.js', 'os.js', 'fs.js', 'path.js', 'crypto.js'];

if (!fs.existsSync(polyfillsDir)) {
  console.log('❌ Node polyfills directory not found');
  console.log('📁 Creating node-polyfills directory...');
  fs.mkdirSync(polyfillsDir);
} else {
  console.log('✅ Node polyfills directory exists');
}

// Check each required polyfill
let missingPolyfills = 0;
for (const polyfill of requiredPolyfills) {
  const polyfillPath = path.join(polyfillsDir, polyfill);
  if (fs.existsSync(polyfillPath)) {
    console.log(`✅ ${polyfill} exists`);
  } else {
    console.log(`❌ ${polyfill} missing`);
    missingPolyfills++;
  }
}

if (missingPolyfills > 0) {
  console.log(`⚠️  ${missingPolyfills} polyfills are missing. They should have been created automatically.`);
}

// Step 2: Check Metro configuration
console.log('\n🔍 Step 2: Checking Metro configuration...');
const metroConfigPath = 'metro.config.js';
if (fs.existsSync(metroConfigPath)) {
  const metroConfig = fs.readFileSync(metroConfigPath, 'utf8');
  
  // Check for Node.js polyfill aliases
  if (metroConfig.includes('node-polyfills/tty.js')) {
    console.log('✅ Metro config includes Node.js polyfill aliases');
  } else {
    console.log('❌ Metro config missing Node.js polyfill aliases');
  }
  
  // Check for socket.io-client in transpilePackages
  if (metroConfig.includes('socket.io-client')) {
    console.log('✅ Metro config includes socket.io-client in transpilePackages');
  } else {
    console.log('❌ Metro config missing socket.io-client in transpilePackages');
  }
  
  // Check for debug module blocking
  if (metroConfig.includes('debug/src/node.js')) {
    console.log('✅ Metro config blocks problematic debug module');
  } else {
    console.log('❌ Metro config not blocking problematic debug module');
  }
} else {
  console.log('❌ Metro config not found');
}

// Step 3: Check socket.io-client installation
console.log('\n📦 Step 3: Checking socket.io-client installation...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  if (packageJson.dependencies['socket.io-client']) {
    console.log('✅ socket.io-client found in dependencies');
    
    // Check version
    const version = packageJson.dependencies['socket.io-client'];
    console.log(`📋 Version: ${version}`);
    
    if (version.startsWith('^4.') || version.startsWith('4.')) {
      console.log('✅ Using compatible socket.io-client version');
    } else {
      console.log('⚠️  Socket.io-client version might cause issues');
    }
  } else {
    console.log('❌ socket.io-client not found in dependencies');
  }
} catch (error) {
  console.error('❌ Error reading package.json:', error.message);
}

// Step 4: Clear caches
console.log('\n🧹 Step 4: Clearing caches...');
try {
  // Clear Metro cache
  if (fs.existsSync('node_modules/.cache')) {
    execSync('rm -rf node_modules/.cache', { stdio: 'inherit' });
    console.log('✅ Metro cache cleared');
  }
  
  // Clear Expo cache
  try {
    execSync('npx expo start --clear --no-dev', { stdio: 'pipe', timeout: 3000 });
  } catch (error) {
    // Expected to timeout, just clearing cache
  }
  console.log('✅ Expo cache cleared');
  
} catch (error) {
  console.log('⚠️  Some cache clearing failed, but continuing...');
}

// Step 5: Test socket.io-client import
console.log('\n🧪 Step 5: Testing socket.io-client import...');
try {
  const testCode = `
    try {
      const io = require('socket.io-client');
      console.log('✅ socket.io-client imports successfully');
      console.log('📋 Socket.IO version:', io.version || 'unknown');
    } catch (error) {
      console.log('❌ socket.io-client import failed:', error.message);
      
      // Check if it's the specific tty error
      if (error.message.includes('tty')) {
        console.log('🔍 This is the TTY module error we are trying to fix');
      }
    }
  `;
  
  fs.writeFileSync('temp-socketio-test.js', testCode);
  execSync('node temp-socketio-test.js', { stdio: 'inherit' });
  fs.unlinkSync('temp-socketio-test.js');
  
} catch (error) {
  console.log('⚠️  Socket.io-client test failed:', error.message);
  
  if (error.message.includes('tty')) {
    console.log('🔍 Confirmed: This is the TTY module error');
    console.log('💡 The polyfills should resolve this in the Metro bundler');
  }
}

// Step 6: Recommendations
console.log('\n📋 Recommendations:');
console.log('===================');

console.log('\n✅ Fixes applied:');
console.log('  - Node.js polyfills created for React Native compatibility');
console.log('  - Metro config updated to use polyfills');
console.log('  - socket.io-client added to transpilePackages');
console.log('  - Problematic debug module blocked');

console.log('\n🚀 Next steps:');
console.log('  1. Run: yarn start --clear');
console.log('  2. If issues persist, try: yarn fix:deps');
console.log('  3. Test on both iOS and Android');

console.log('\n🔧 Alternative solutions if issues persist:');
console.log('  1. Consider using a different WebSocket library');
console.log('  2. Use socket.io-client with React Native specific configuration');
console.log('  3. Implement WebSocket communication without socket.io-client');

console.log('\n💡 Technical explanation:');
console.log('  - socket.io-client uses Node.js modules (tty, util, os, fs, path, crypto)');
console.log('  - React Native doesn\'t include Node.js standard library');
console.log('  - We provide polyfills to make these modules available');
console.log('  - Metro bundler uses these polyfills instead of Node.js modules');

console.log('\n🎉 Socket.IO Client issue fixer completed!');
console.log('   Try running "yarn start --clear" to test the fix.');