import { StyleSheet } from '../utils/platformUtils';

// Centralized Auth Screen Styles
export const authStyles = StyleSheet.create({
  // Container Styles
  container: {
    flex: 1,
    backgroundColor: '#6366F1',
  },
  
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    background: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
  },
  
  gradientOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
  },
  
  keyboardContainer: {
    flex: 1,
  },
  
  scrollView: {
    flexGrow: 1,
  },
  
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  
  // Header Styles
  headerSection: {
    alignItems: 'center',
    marginBottom: 16,
    paddingTop: 40,
  },
  
  logoContainer: {
    width: 60,
    height: 60,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  
  logoImage: {
    width: 40,
    height: 40,
  },
  
  brandName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    letterSpacing: 1.5,
    marginBottom: 6,
  },
  
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 6,
    textAlign: 'center',
  },
  
  subtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 0,
  },
  
  // Form Styles
  formCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 12,
    marginHorizontal: 0,
  },
  
  formContainer: {
    paddingTop: 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  
  formContent: {
    gap: 20,
  },
  
  inputContainer: {
    marginBottom: 16,
  },
  
  // Button Styles
  primaryButton: {
    marginTop: 24,
    backgroundColor: '#6366F1',
    borderRadius: 16,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  
  primaryButtonDisabled: {
    backgroundColor: '#D1D5DB',
    shadowOpacity: 0,
    elevation: 0,
  },
  
  primaryButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  
  // Footer Styles
  footerSection: {
    alignItems: 'center',
    marginTop: 32,
  },
  
  secondaryButton: {
    paddingHorizontal: 24,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 12,
    padding: 12,
    marginTop: 16,
    minWidth: 200,
  },
  
  secondaryButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '500',
  },
  
  // Role Selection Styles (for Signup)
  roleSection: {
    marginBottom: 20,
  },
  
  roleLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },

  // Revolutionary Design Styles - Complete UI Overhaul
  revolutionaryContainer: {
    flex: 1,
    backgroundColor: '#FF6B6B',
  },

  revolutionaryGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },

  floatingShapes: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },

  shape1: {
    position: 'absolute',
    top: 100,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },

  shape2: {
    position: 'absolute',
    top: 200,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },

  shape3: {
    position: 'absolute',
    bottom: 150,
    right: 50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
  },

  revolutionaryKeyboard: {
    flex: 1,
  },

  revolutionaryScroll: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingVertical: 40,
  },

  revolutionaryHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },

  revolutionaryLogoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  revolutionaryLogo: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },

  revolutionaryLogoIcon: {
    fontSize: 24,
  },

  revolutionaryBrandSection: {
    flex: 1,
  },

  revolutionaryBrandName: {
    fontSize: 22,
    fontWeight: '800',
    color: '#FFFFFF',
    letterSpacing: 2,
  },

  revolutionaryBrandTagline: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },

  revolutionaryWelcome: {
    fontSize: 32,
    fontWeight: '900',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },

  revolutionarySubtext: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontWeight: '500',
  },

  revolutionaryLoginCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 30,
    padding: 25,
    marginBottom: 30,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },

  revolutionaryInputGroup: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },

  revolutionaryInputIcon: {
    width: 50,
    height: 50,
    borderRadius: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
    marginTop: 25,
  },

  revolutionaryInputIconText: {
    fontSize: 20,
  },

  revolutionaryInputContainer: {
    flex: 1,
  },

  revolutionaryInputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  revolutionaryInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingHorizontal: 20,
    paddingVertical: 15,
    fontSize: 16,
    color: '#FFFFFF',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },

  revolutionaryInputError: {
    borderColor: '#FF4757',
    borderWidth: 2,
  },

  revolutionaryPasswordWrapper: {
    position: 'relative',
  },

  revolutionaryPasswordInput: {
    paddingRight: 60,
  },

  revolutionaryPasswordToggle: {
    position: 'absolute',
    right: 15,
    top: 15,
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },

  revolutionaryPasswordToggleIcon: {
    fontSize: 18,
  },

  revolutionaryErrorText: {
    color: '#FF4757',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 5,
    marginLeft: 65,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  revolutionaryRememberMe: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 25,
    marginLeft: 65,
  },

  revolutionaryToggle: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginRight: 12,
    justifyContent: 'center',
  },

  revolutionaryToggleActive: {
    backgroundColor: '#4ECDC4',
  },

  revolutionaryToggleThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },

  revolutionaryRememberText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },

  revolutionaryLoginButton: {
    borderRadius: 20,
    marginBottom: 25,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 10,
  },

  revolutionaryLoginButtonDisabled: {
    opacity: 0.6,
  },

  revolutionaryButtonGradient: {
    paddingVertical: 18,
    paddingHorizontal: 30,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  revolutionaryButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  revolutionaryLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  revolutionaryLoadingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },

  revolutionaryDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 25,
  },

  revolutionaryDividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },

  revolutionaryDividerText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginHorizontal: 15,
    fontWeight: '500',
  },

  revolutionarySocialContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },

  revolutionarySocialButton: {
    flex: 1,
    marginHorizontal: 5,
    borderRadius: 15,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },

  revolutionarySocialGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 15,
  },

  revolutionarySocialIcon: {
    fontSize: 16,
    marginRight: 8,
  },

  revolutionarySocialText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },

  revolutionaryErrorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 71, 87, 0.2)',
    borderRadius: 12,
    padding: 15,
    marginTop: 10,
    borderWidth: 1,
    borderColor: '#FF4757',
  },

  revolutionaryErrorIcon: {
    fontSize: 18,
    marginRight: 10,
  },

  revolutionaryGeneralError: {
    flex: 1,
    fontSize: 14,
    color: '#FF4757',
    fontWeight: '600',
  },

  revolutionaryFooter: {
    alignItems: 'center',
  },

  revolutionaryFooterText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 15,
    fontWeight: '500',
  },

  revolutionarySignupButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },

  revolutionarySignupText: {
     fontSize: 16,
     fontWeight: '700',
     color: '#FFFFFF',
     textAlign: 'center',
   },

   // Ultra Modern Design Styles - Dark Theme with Neon Accents
   ultraModernContainer: {
     flex: 1,
     backgroundColor: '#0A0A0A',
   },

   ultraModernGradient: {
     position: 'absolute',
     left: 0,
     right: 0,
     top: 0,
     bottom: 0,
   },

   neonEffects: {
     position: 'absolute',
     width: '100%',
     height: '100%',
   },

   neonCircle1: {
     position: 'absolute',
     top: 80,
     right: 40,
     width: 100,
     height: 100,
     borderRadius: 50,
     backgroundColor: 'rgba(0, 245, 255, 0.1)',
     borderWidth: 2,
     borderColor: 'rgba(0, 245, 255, 0.3)',
   },

   neonCircle2: {
     position: 'absolute',
     bottom: 120,
     left: 30,
     width: 60,
     height: 60,
     borderRadius: 30,
     backgroundColor: 'rgba(255, 0, 128, 0.1)',
     borderWidth: 1,
     borderColor: 'rgba(255, 0, 128, 0.3)',
   },

   neonLine1: {
     position: 'absolute',
     top: 200,
     left: 0,
     right: 0,
     height: 1,
     backgroundColor: 'rgba(0, 245, 255, 0.2)',
   },

   neonLine2: {
     position: 'absolute',
     bottom: 200,
     left: 0,
     right: 0,
     height: 1,
     backgroundColor: 'rgba(255, 0, 128, 0.2)',
   },

   ultraModernKeyboard: {
     flex: 1,
   },

   ultraModernScroll: {
     flexGrow: 1,
     paddingHorizontal: 24,
     paddingVertical: 40,
   },

   ultraModernHeader: {
     alignItems: 'center',
     marginBottom: 40,
   },

   ultraModernLogoContainer: {
     alignItems: 'center',
     marginBottom: 30,
   },

   ultraModernLogo: {
     width: 80,
     height: 80,
     borderRadius: 20,
     alignItems: 'center',
     justifyContent: 'center',
     marginBottom: 16,
     shadowColor: '#00F5FF',
     shadowOffset: { width: 0, height: 0 },
     shadowOpacity: 0.5,
     shadowRadius: 20,
     elevation: 20,
   },

   ultraModernLogoText: {
     fontSize: 36,
     color: '#FFFFFF',
   },

   ultraModernBrandName: {
     fontSize: 28,
     fontWeight: '900',
     color: '#FFFFFF',
     letterSpacing: 4,
     marginBottom: 4,
   },

   ultraModernBrandSubtitle: {
     fontSize: 12,
     color: '#666',
     fontWeight: '500',
     letterSpacing: 1,
   },

   ultraModernWelcome: {
     fontSize: 36,
     fontWeight: '800',
     color: '#FFFFFF',
     textAlign: 'center',
     marginBottom: 8,
   },

   ultraModernSubtext: {
     fontSize: 16,
     color: '#999',
     textAlign: 'center',
     fontWeight: '400',
   },

   ultraModernPanel: {
     backgroundColor: 'rgba(26, 26, 46, 0.8)',
     borderRadius: 24,
     padding: 32,
     marginBottom: 32,
     borderWidth: 1,
     borderColor: 'rgba(0, 245, 255, 0.2)',
     shadowColor: '#000000',
     shadowOffset: { width: 0, height: 20 },
     shadowOpacity: 0.5,
     shadowRadius: 30,
     elevation: 20,
   },

   ultraModernInputSection: {
     marginBottom: 28,
   },

   ultraModernInputHeader: {
     flexDirection: 'row',
     alignItems: 'center',
     marginBottom: 12,
   },

   ultraModernInputIcon: {
     width: 32,
     height: 32,
     borderRadius: 8,
     alignItems: 'center',
     justifyContent: 'center',
     marginRight: 12,
   },

   ultraModernIconText: {
     fontSize: 16,
     fontWeight: '700',
     color: '#FFFFFF',
   },

   ultraModernInputTitle: {
     fontSize: 16,
     fontWeight: '600',
     color: '#FFFFFF',
   },

   ultraModernInputWrapper: {
     position: 'relative',
   },

   ultraModernInput: {
     backgroundColor: 'transparent',
     borderWidth: 0,
     paddingVertical: 16,
     paddingHorizontal: 0,
     fontSize: 16,
     color: '#FFFFFF',
     fontWeight: '500',
   },

   ultraModernInputError: {
     color: '#FF4081',
   },

   ultraModernInputUnderline: {
     height: 2,
     backgroundColor: 'rgba(0, 245, 255, 0.3)',
     marginTop: 4,
   },

   ultraModernPasswordContainer: {
     flexDirection: 'row',
     alignItems: 'center',
   },

   ultraModernPasswordField: {
     flex: 1,
   },

   ultraModernPasswordToggle: {
     marginLeft: 12,
   },

   ultraModernToggleGradient: {
     width: 32,
     height: 32,
     borderRadius: 16,
     alignItems: 'center',
     justifyContent: 'center',
   },

   ultraModernToggleIcon: {
     fontSize: 16,
     color: '#FFFFFF',
   },

   ultraModernErrorText: {
     color: '#FF4081',
     fontSize: 12,
     fontWeight: '500',
     marginTop: 8,
   },

   ultraModernRememberSection: {
     flexDirection: 'row',
     alignItems: 'center',
     marginBottom: 32,
   },

   ultraModernSwitch: {
     marginRight: 12,
   },

   ultraModernSwitchTrack: {
     width: 48,
     height: 28,
     borderRadius: 14,
     justifyContent: 'center',
     paddingHorizontal: 2,
   },

   ultraModernSwitchThumb: {
     width: 24,
     height: 24,
     borderRadius: 12,
     backgroundColor: '#FFFFFF',
     shadowColor: '#000000',
     shadowOffset: { width: 0, height: 2 },
     shadowOpacity: 0.3,
     shadowRadius: 4,
     elevation: 4,
   },

   ultraModernRememberText: {
     fontSize: 14,
     color: '#FFFFFF',
     fontWeight: '500',
   },

   ultraModernLoginButton: {
     borderRadius: 16,
     marginBottom: 32,
     shadowColor: '#00F5FF',
     shadowOffset: { width: 0, height: 8 },
     shadowOpacity: 0.3,
     shadowRadius: 20,
     elevation: 15,
   },

   ultraModernButtonDisabled: {
     opacity: 0.5,
   },

   ultraModernButtonGradient: {
     paddingVertical: 20,
     paddingHorizontal: 32,
     borderRadius: 16,
     alignItems: 'center',
     justifyContent: 'center',
   },

   ultraModernButtonContent: {
     flexDirection: 'row',
     alignItems: 'center',
   },

   ultraModernButtonText: {
     fontSize: 16,
     fontWeight: '700',
     color: '#FFFFFF',
     letterSpacing: 1,
   },

   ultraModernButtonIcon: {
     fontSize: 18,
     color: '#FFFFFF',
     marginLeft: 8,
   },

   ultraModernLoadingContainer: {
     flexDirection: 'row',
     alignItems: 'center',
   },

   ultraModernLoadingText: {
     fontSize: 16,
     fontWeight: '600',
     color: '#FFFFFF',
   },

   ultraModernDivider: {
     flexDirection: 'row',
     alignItems: 'center',
     marginBottom: 32,
   },

   ultraModernDividerLine: {
     flex: 1,
     height: 1,
     backgroundColor: 'rgba(255, 255, 255, 0.1)',
   },

   ultraModernDividerCenter: {
     paddingHorizontal: 16,
     paddingVertical: 8,
     borderRadius: 12,
     marginHorizontal: 16,
   },

   ultraModernDividerText: {
     fontSize: 12,
     fontWeight: '600',
     color: '#FFFFFF',
     letterSpacing: 1,
   },

   ultraModernSocialSection: {
     flexDirection: 'row',
     justifyContent: 'space-between',
     marginBottom: 24,
   },

   ultraModernSocialButton: {
     flex: 1,
     marginHorizontal: 6,
     borderRadius: 12,
     shadowColor: '#000000',
     shadowOffset: { width: 0, height: 4 },
     shadowOpacity: 0.3,
     shadowRadius: 8,
     elevation: 8,
   },

   ultraModernSocialGradient: {
     flexDirection: 'row',
     alignItems: 'center',
     justifyContent: 'center',
     paddingVertical: 16,
     paddingHorizontal: 20,
     borderRadius: 12,
     borderWidth: 1,
     borderColor: 'rgba(0, 245, 255, 0.2)',
   },

   ultraModernSocialIconBg: {
     width: 24,
     height: 24,
     borderRadius: 12,
     alignItems: 'center',
     justifyContent: 'center',
     marginRight: 8,
   },

   ultraModernSocialIcon: {
     fontSize: 12,
     fontWeight: '700',
     color: '#FFFFFF',
   },

   ultraModernSocialText: {
     fontSize: 14,
     fontWeight: '600',
     color: '#FFFFFF',
   },

   ultraModernErrorContainer: {
     flexDirection: 'row',
     alignItems: 'center',
     backgroundColor: 'rgba(255, 0, 128, 0.1)',
     borderRadius: 12,
     padding: 16,
     marginTop: 16,
     borderWidth: 1,
     borderColor: 'rgba(255, 0, 128, 0.3)',
   },

   ultraModernErrorIcon: {
     width: 24,
     height: 24,
     borderRadius: 12,
     alignItems: 'center',
     justifyContent: 'center',
     marginRight: 12,
   },

   ultraModernErrorIconText: {
     fontSize: 14,
     fontWeight: '700',
     color: '#FFFFFF',
   },

   ultraModernErrorMessage: {
     flex: 1,
     fontSize: 14,
     color: '#FF4081',
     fontWeight: '500',
   },

   ultraModernFooter: {
     alignItems: 'center',
   },

   ultraModernFooterText: {
     fontSize: 16,
     color: '#999',
     marginBottom: 16,
     fontWeight: '500',
   },

   ultraModernSignupButton: {
     borderRadius: 16,
     shadowColor: '#000000',
     shadowOffset: { width: 0, height: 4 },
     shadowOpacity: 0.3,
     shadowRadius: 8,
     elevation: 8,
   },

   ultraModernSignupGradient: {
     flexDirection: 'row',
     alignItems: 'center',
     justifyContent: 'center',
     paddingVertical: 16,
     paddingHorizontal: 32,
     borderRadius: 16,
     borderWidth: 1,
     borderColor: 'rgba(0, 245, 255, 0.3)',
   },

   ultraModernSignupText: {
     fontSize: 16,
     fontWeight: '600',
     color: '#FFFFFF',
     marginRight: 8,
   },

   ultraModernSignupAccent: {
     width: 24,
     height: 24,
     borderRadius: 12,
     alignItems: 'center',
     justifyContent: 'center',
   },

   ultraModernSignupArrow: {
     fontSize: 16,
     fontWeight: '700',
     color: '#FFFFFF',
   },

   // Legacy Modern Design Styles (keeping for compatibility)
   modernLogoContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },

  logoGlow: {
    width: 80,
    height: 80,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FFFFFF',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 8,
  },

  logoIcon: {
    fontSize: 36,
    color: '#FFFFFF',
  },

  modernBrandName: {
    fontSize: 32,
    fontWeight: '800',
    color: '#FFFFFF',
    letterSpacing: 2,
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },

  modernTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },

  modernSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },

  modernFormCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 28,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.2,
    shadowRadius: 32,
    elevation: 16,
    marginHorizontal: 0,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },

  modernFormContainer: {
    paddingTop: 32,
    paddingBottom: 32,
    paddingHorizontal: 28,
  },

  modernPrimaryButton: {
    marginTop: 24,
    backgroundColor: '#6366F1',
    borderRadius: 20,
    paddingVertical: 18,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },

  modernPrimaryButtonText: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '700',
    letterSpacing: 0.5,
    textAlign: 'center',
  },

  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },

  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E5E7EB',
  },

  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: '#9CA3AF',
    fontWeight: '500',
  },

  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 16,
  },

  socialButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    gap: 8,
  },

  socialButtonIcon: {
    fontSize: 18,
  },

  socialButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },

  modernFooterSection: {
    alignItems: 'center',
    marginTop: 24,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },

  footerText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },

  modernSecondaryButton: {
    paddingHorizontal: 4,
    paddingVertical: 4,
  },

  modernSecondaryButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '700',
    textDecorationLine: 'underline',
  },
});

// Brand Constants
export const brandConstants = {
  name: 'CTRON',
  colors: {
    primary: '#6366F1',
    primaryDark: '#4F46E5',
    secondary: '#8B5CF6',
    white: '#FFFFFF',
    gray: '#6B7280',
    lightGray: '#F3F4F6',
  },
};

// Common Text Styles
export const textStyles = {
  login: {
    title: 'Log In',
    subtitle: 'Let\'s get to work',
    buttonText: 'Login',
    footerText: 'Don\'t have account? Sign Up',
  },
  signup: {
    title: 'Create Account',
    subtitle: 'Join CTRON today',
    buttonText: 'Create Account',
    footerText: 'Already have account? Log In',
  },
};