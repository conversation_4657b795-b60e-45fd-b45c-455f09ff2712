// backend/src/server.ts

import express, { Request, Response, NextFunction } from 'express';
import http from 'http';
import cors from 'cors';
import dotenv from 'dotenv';
import { raw, json, urlencoded } from 'body-parser';
import helmet from 'helmet';

import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import jobRoutes from './routes/job.routes';
import technicianRoutes from './routes/technician.routes';
import paymentRoutes from './routes/payment.routes';
import reviewRoutes from './routes/review.routes';
import webhookRoutes from './routes/webhook.routes';
import dashboardRoutes from './routes/dashboard.routes';
import settingsRoutes from './routes/settings.routes';
import chatRoutes from './routes/chat.routes';
import notificationRoutes from './routes/notifications.routes';
import aiRoutes from './routes/ai.routes';
import uploadRoutes from './routes/upload.routes';

import { initSocket } from './config/socket';
import { errorMiddleware } from './middleware/error.middleware';
import { env } from './config/env';
import { corsOptions, helmetOptions } from './config/security';
import { rateLimitMiddleware, rateLimits } from './middleware/rateLimiting.middleware';
import { addRequestId } from './utils/apiResponse';

dotenv.config();

const app = express();
const server = http.createServer(app);

// Request ID middleware (must be first)
app.use(addRequestId);

// Security middleware
app.use(helmet(helmetOptions));
app.use(cors(corsOptions));

// Rate limiting
app.use('/api/auth', rateLimitMiddleware(rateLimits.auth));
app.use('/api/chats', rateLimitMiddleware(rateLimits.chat));
app.use('/api', rateLimitMiddleware(rateLimits.api));

// Initialize Socket.IO
initSocket(server);

// Stripe webhook must use raw parser
app.use('/api/webhook', express.raw({ type: 'application/json' }));

// Body parsing
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/jobs', jobRoutes);
app.use('/api/technicians', technicianRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/webhook', webhookRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/chats', chatRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/upload', uploadRoutes);

// Health check endpoints
app.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'CTRON Home Backend API',
    version: process.env.npm_package_version || env.APP_VERSION || '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/health', (req: Request, res: Response) => {
  res.json({
    status: 'OK',
    message: 'Health check passed',
    environment: env.NODE_ENV,
    database: 'connected'
  });
});

// 404 handler
app.use((req: Request, res: Response, next: NextFunction) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Global error handler
app.use(errorMiddleware);

// ✅ Register the auto-release scheduler (only in non-test environments)
if (process.env.NODE_ENV !== 'test') {
  import('./schedulers/autoReleaseScheduler');
}

// Export app and server for index.ts
export { app, server };
