// CTRON Home - Location Service
// Comprehensive GPS tracking and navigation management

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Linking from 'expo-linking';
import * as Location from 'expo-location';
import { jwtDecode } from 'jwt-decode';

import { LOCATION_CONSTANTS } from '../config/constants';
import type { UserPayload } from '../context/AuthContext';
import { getAuthToken } from '../utils/auth.utils';
import { debugLogger } from '../utils/debugLogger';
import { Platform, Alert } from '../utils/platformUtils';

import { realtimeService } from './realtime.service';




// Type definitions for external modules
interface TaskManagerModule {
  defineTask: (taskName: string, task: (params: { data: unknown; error: unknown }) => Promise<void>) => void;
  isTaskRegisteredAsync: (taskName: string) => Promise<boolean>;
}

interface BackgroundTaskModule {
  registerTaskAsync: (taskName: string, options: { minimumInterval: number }) => Promise<void>;
  unregisterTaskAsync: (taskName: string) => Promise<void>;
}

interface LocationUpdateData {
  locations: Location.LocationObject[];
}

let TaskManager: TaskManagerModule | null = null;
let BackgroundTask: BackgroundTaskModule | null = null;

try {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  TaskManager = require('expo-task-manager');
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  BackgroundTask = require('expo-background-task');
} catch (e) {
  debugLogger.error('Failed to load expo-task-manager or expo-background-task:', e);
}

export const requestForegroundPermissionsAsync = Location.requestForegroundPermissionsAsync;
export const getCurrentPositionAsync = Location.getCurrentPositionAsync;
export const watchPositionAsync = Location.watchPositionAsync;

/**
 * Interface for location data
 */
interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  speed?: number;
  heading?: number;
  timestamp: number;
}

/**
 * Interface for location tracking options
 */
interface TrackingOptions {
  accuracy: Location.Accuracy;
  timeInterval: number;
  distanceInterval: number;
  enableBackground: boolean;
  jobId?: string;
}

/**
 * Interface for navigation destination
 */
interface NavigationDestination {
  latitude: number;
  longitude: number;
  address: string;
  name?: string;
}

/**
 * Background location task name
 */
const BACKGROUND_LOCATION_TASK = 'background-location-task';

/**
 * Location service for GPS tracking and navigation
 */
class LocationService {
  private isTracking: boolean = false;
  private currentJobId?: string;
  private trackingOptions: TrackingOptions = {
    accuracy: Location.Accuracy.High,
    timeInterval: LOCATION_CONSTANTS.TRACKING_INTERVAL,
    distanceInterval: 50, // 50 meters
    enableBackground: false,
  };
  private locationSubscription?: Location.LocationSubscription;

  /**
   * Initialize location service
   */
  public async initialize(): Promise<void> {
    try {
      // Define background location task
      if (TaskManager) {
        TaskManager.defineTask(BACKGROUND_LOCATION_TASK, async ({ data, error }) => {
          if (error) {
            debugLogger.error('Background location task error:', error);
            return;
          }

          if (data) {
            const locationData = data as LocationUpdateData;
            if (locationData.locations && locationData.locations.length > 0) {
              await this.handleLocationUpdate(locationData.locations[0]);
            }
          }
        });
      }

      debugLogger.info('📍 Location service initialized');
    } catch (error) {
      debugLogger.error('Failed to initialize location service:', error);
      throw error;
    }
  }

  /**
   * Request location permissions
   */
  public async requestPermissions(): Promise<boolean> {
    try {
      // Request foreground permissions
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();

      if (foregroundStatus !== 'granted') {
        Alert.alert(
          'Location Permission Required',
          'Location access is required for job tracking and navigation features.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => Linking.openSettings() },
          ]
        );
        return false;
      }

      // Request background permissions if needed
      if (this.trackingOptions.enableBackground) {
        const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();

        if (backgroundStatus !== 'granted') {
          Alert.alert(
            'Background Location Permission',
            'Background location access allows continuous tracking during active jobs.',
            [
              { text: 'Skip', style: 'cancel' },
              { text: 'Settings', onPress: () => Linking.openSettings() },
            ]
          );
        }
      }

      return true;
    } catch (error) {
      debugLogger.error('Failed to request location permissions:', error);
      return false;
    }
  }

  /**
   * Get current location
   */
  public async getCurrentLocation(): Promise<LocationData | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        // maximumAge: LOCATION_CONSTANTS.LOCATION_MAXIMUM_AGE, // not supported in this version
      });

      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || 0,
        altitude: location.coords.altitude || undefined,
        speed: location.coords.speed || undefined,
        heading: location.coords.heading || undefined,
        timestamp: location.timestamp,
      };
    } catch (error) {
      debugLogger.error('Failed to get current location:', error);
      return null;
    }
  }

  /**
   * Start location tracking
   */
  public async startTracking(options?: Partial<TrackingOptions>): Promise<boolean> {
    try {
      if (this.isTracking) {
        debugLogger.info('📍 Location tracking already active');
        return true;
      }

      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return false;
      }

      // Update tracking options
      this.trackingOptions = { ...this.trackingOptions, ...options };

      // Start foreground tracking
      this.locationSubscription = await Location.watchPositionAsync(
        {
          accuracy: this.trackingOptions.accuracy,
          timeInterval: this.trackingOptions.timeInterval,
          distanceInterval: this.trackingOptions.distanceInterval,
        },
        (location) => {
          this.handleLocationUpdate(location);
        }
      );

      // Start background tracking if enabled
      if (this.trackingOptions.enableBackground) {
        await this.startBackgroundTracking();
      }

      this.isTracking = true;
      this.currentJobId = options?.jobId;

      debugLogger.info('📍 Location tracking started');
      return true;
    } catch (error) {
      debugLogger.error('Failed to start location tracking:', error);
      return false;
    }
  }

  /**
   * Stop location tracking
   */
  public async stopTracking(): Promise<void> {
    try {
      if (!this.isTracking) {
        return;
      }

      // Stop foreground tracking
      if (this.locationSubscription) {
        this.locationSubscription.remove();
        this.locationSubscription = undefined;
      }

      // Stop background tracking
      await this.stopBackgroundTracking();

      this.isTracking = false;
      this.currentJobId = undefined;

      debugLogger.info('📍 Location tracking stopped');
    } catch (error) {
      debugLogger.error('Failed to stop location tracking:', error);
    }
  }

  /**
   * Start background location tracking
   */
  private async startBackgroundTracking(): Promise<void> {
    try {
      if (!TaskManager || !BackgroundTask) {
        debugLogger.warn('TaskManager or BackgroundTask not available');
        return;
      }

      const isRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_LOCATION_TASK);

      if (!isRegistered) {
        // Register the task with expo-background-task
        await BackgroundTask.registerTaskAsync(BACKGROUND_LOCATION_TASK, {
          minimumInterval: this.trackingOptions.timeInterval / 1000, // Convert ms to seconds
        });
        
        // Start location updates with expo-location
        await Location.startLocationUpdatesAsync(BACKGROUND_LOCATION_TASK, {
          accuracy: this.trackingOptions.accuracy,
          timeInterval: this.trackingOptions.timeInterval,
          distanceInterval: this.trackingOptions.distanceInterval,
          foregroundService: {
            notificationTitle: 'CTRON Home - Job Tracking',
            notificationBody: 'Tracking your location for active job',
            notificationColor: '#007AFF',
          },
        });
      }

      debugLogger.info('📍 Background location tracking started');
    } catch (error) {
      debugLogger.error('Failed to start background tracking:', error);
    }
  }

  /**
   * Stop background location tracking
   */
  private async stopBackgroundTracking(): Promise<void> {
    try {
      if (!TaskManager || !BackgroundTask) {
        debugLogger.warn('TaskManager or BackgroundTask not available');
        return;
      }

      const isRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_LOCATION_TASK);

      if (isRegistered) {
        // Stop location updates
        await Location.stopLocationUpdatesAsync(BACKGROUND_LOCATION_TASK);
        
        // Unregister the background task
        await BackgroundTask.unregisterTaskAsync(BACKGROUND_LOCATION_TASK);
      }

      debugLogger.info('📍 Background location tracking stopped');
    } catch (error) {
      debugLogger.error('Failed to stop background tracking:', error);
    }
  }

  /**
   * Handle location update
   */
  private async handleLocationUpdate(location: Location.LocationObject): Promise<void> {
    try {
      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || 0,
        altitude: location.coords.altitude || undefined,
        speed: location.coords.speed || undefined,
        heading: location.coords.heading || undefined,
        timestamp: location.timestamp,
      };

      // Store location locally
      await this.storeLocation(locationData);

      // Send to real-time service if tracking for a job
      if (this.currentJobId) {
        try {
          const token = await getAuthToken();
          let technicianId = 'unknown';
          
          if (token) {
            const decoded = jwtDecode<UserPayload>(token);
            if (decoded.role === 'TECHNICIAN' && decoded.technicianProfile?.id) {
              technicianId = decoded.technicianProfile.id;
            } else {
              technicianId = decoded.userId;
            }
          }
          
          realtimeService.sendLocationUpdate({
            technicianId,
            jobId: this.currentJobId,
            latitude: locationData.latitude,
            longitude: locationData.longitude,
            accuracy: locationData.accuracy,
            timestamp: new Date(locationData.timestamp).toISOString(),
            speed: locationData.speed,
            heading: locationData.heading,
          });
        } catch (error) {
          debugLogger.error('Failed to get technician ID for location update:', error);
        }
      }

      debugLogger.info('📍 Location updated:', locationData);
    } catch (error) {
      debugLogger.error('Failed to handle location update:', error);
    }
  }

  /**
   * Store location data locally
   */
  private async storeLocation(location: LocationData): Promise<void> {
    try {
      const key = 'location_history';
      const existingData = await AsyncStorage.getItem(key);
      const locations = existingData ? JSON.parse(existingData) : [];

      locations.push(location);

      // Keep only last 100 locations
      if (locations.length > 100) {
        locations.splice(0, locations.length - 100);
      }

      await AsyncStorage.setItem(key, JSON.stringify(locations));
    } catch (error) {
      debugLogger.error('Failed to store location:', error);
    }
  }

  /**
   * Calculate distance between two points
   */
  public calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = LOCATION_CONSTANTS.EARTH_RADIUS_KM;
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in kilometers
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Open navigation to destination
   */
  public async navigateToDestination(destination: NavigationDestination): Promise<void> {
    try {
      const { latitude, longitude, address } = destination;

      // Try to open in Google Maps first
      const googleMapsUrl = Platform.OS === 'ios'
        ? `comgooglemaps://?daddr=${latitude},${longitude}&directionsmode=driving`
        : `google.navigation:q=${latitude},${longitude}&mode=d`;

      const canOpenGoogleMaps = await Linking.canOpenURL(googleMapsUrl!);

      if (canOpenGoogleMaps) {
        await Linking.openURL(googleMapsUrl!);
      } else {
        // Fallback to Apple Maps or default maps
        const fallbackUrl = Platform.OS === 'ios'
          ? `maps://app?daddr=${latitude},${longitude}`
          : `geo:${latitude},${longitude}?q=${latitude},${longitude}(${encodeURIComponent(address)})`;

        await Linking.openURL(fallbackUrl!);
      }

      debugLogger.info('🗺️ Navigation opened to:', destination);
    } catch (error) {
      debugLogger.error('Failed to open navigation:', error);
      Alert.alert('Navigation Error', 'Unable to open navigation app. Please check your location settings.');
    }
  }

  /**
   * Get location history
   */
  public async getLocationHistory(): Promise<LocationData[]> {
    try {
      const data = await AsyncStorage.getItem('location_history');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      debugLogger.error('Failed to get location history:', error);
      return [];
    }
  }

  /**
   * Clear location history
   */
  public async clearLocationHistory(): Promise<void> {
    try {
      await AsyncStorage.removeItem('location_history');
      debugLogger.info('📍 Location history cleared');
    } catch (error) {
      debugLogger.error('Failed to clear location history:', error);
    }
  }

  /**
   * Check if currently tracking
   */
  public isCurrentlyTracking(): boolean {
    return this.isTracking;
  }

  /**
   * Get current job ID being tracked
   */
  public getCurrentJobId(): string | undefined {
    return this.currentJobId;
  }
}

// Export singleton instance
export const locationService = new LocationService();
export default locationService;
