// CTRON Home - Error State Component
// Consistent error displays across the app

import React from 'react';
import { useTheme } from '../../context/ThemeContext';
import { Button } from './Button';
import { View, Text, StyleSheet } from '../../utils/platformUtils';

interface ErrorStateProps {
  title?: string;
  message: string;
  actionTitle?: string;
  onAction?: () => void;
  style?: any;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'Something went wrong',
  message,
  actionTitle = 'Try Again',
  onAction,
  style,
}) => {
  const { colors, spacing, typography } = useTheme();

  return (
    <View style={[styles.container, style]}>
      <Text style={[styles.icon, { fontSize: 48, marginBottom: spacing.lg }]}>
        ⚠️
      </Text>
      
      <Text style={[styles.title, {
        fontSize: typography.fontSize.xl,
        fontWeight: typography.fontWeight.bold,
        color: colors.error.main,
        marginBottom: spacing.sm,
      }]}>
        {title}
      </Text>
      
      <Text style={[styles.message, {
        fontSize: typography.fontSize.base,
        color: colors.text.secondary,
        marginBottom: spacing.lg,
      }]}>
        {message}
      </Text>
      
      {onAction && (
        <Button
          title={actionTitle}
          onPress={onAction}
          variant="secondary"
          style={styles.actionButton}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  icon: {
    textAlign: 'center',
  },
  title: {
    textAlign: 'center',
  },
  message: {
    textAlign: 'center',
    lineHeight: 24,
  },
  actionButton: {
    minWidth: 120,
  },
});