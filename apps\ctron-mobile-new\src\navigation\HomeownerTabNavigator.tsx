// CTRON Home - Homeowner Tab Navigator
// Bottom tab navigation for homeowner screens

import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import { useTheme } from '../context/ThemeContext';
// Import screens
import ChatListScreen from '../screens/Chat/ChatListScreen';
import ModernBookJobScreen from '../screens/Homeowner/ModernBookJobScreen';
import ModernHomeScreen from '../screens/Homeowner/ModernHomeScreen';
import ModernMyJobsScreen from '../screens/Homeowner/ModernMyJobsScreen';
import { View, Text, StyleSheet } from '../utils/platformUtils';



const Tab = createBottomTabNavigator();

export const HomeownerTabNavigator = () => {
  const { colors, spacing } = useTheme();

  const styles = StyleSheet.create({
    tabBar: {
      backgroundColor: colors.background.primary,
      borderTopWidth: 1,
      borderTopColor: colors.border.light,
      paddingTop: spacing.sm,
      paddingBottom: spacing.sm,
      height: 80,
      elevation: 8,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: -2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 8,
    },

    tabBarItem: {
      paddingVertical: spacing.xs,
    },

    tabBarLabel: {
      fontSize: 12,
      fontWeight: '500',
      marginTop: spacing.xs,
    },

    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.xs,
    },

    iconContainerActive: {
      backgroundColor: `${colors.primary[900]}15`, // 15% opacity
    },

    icon: {
      fontSize: 20,
      color: colors.text.secondary,
    },

    iconActive: {
      color: colors.primary.main,
    },
  });

  // Icon components with access to styles
  const HomeIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>🏠</Text>
    </View>
  );

  const JobsIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>📋</Text>
    </View>
  );

  const BookIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>➕</Text>
    </View>
  );

  const ChatIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>💬</Text>
    </View>
  );

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: colors.primary[900],
        tabBarInactiveTintColor: colors.text.tertiary,
        tabBarLabelStyle: styles.tabBarLabel,
        tabBarItemStyle: styles.tabBarItem,
      }}
    >
      <Tab.Screen
        name="Home"
        component={ModernHomeScreen}
        options={{
          tabBarLabel: 'Home',
          tabBarIcon: ({ focused }) => <HomeIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="MyJobs"
        component={ModernMyJobsScreen}
        options={{
          tabBarLabel: 'My Jobs',
          tabBarIcon: ({ focused }) => <JobsIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="BookJob"
        component={ModernBookJobScreen}
        options={{
          tabBarLabel: 'Book',
          tabBarIcon: ({ focused }) => <BookIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="Messages"
        component={ChatListScreen}
        options={{
          tabBarLabel: 'Messages',
          tabBarIcon: ({ focused }) => <ChatIcon focused={focused} />,
        }}
      />
    </Tab.Navigator>
  );
};

export default HomeownerTabNavigator;

