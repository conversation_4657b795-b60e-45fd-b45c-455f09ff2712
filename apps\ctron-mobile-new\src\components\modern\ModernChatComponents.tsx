// CTRON Home - Modern Chat Components with NativeBase
// Professional chat interface components for technician communication

import { MaterialIcons } from '@expo/vector-icons';
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Avatar,
  Badge,
  Icon,
  Pressable,
  ScrollView,
  useTheme,
} from 'native-base';
import React, { useState, useRef, useEffect } from 'react';

import { Animated, Dimensions } from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

// Message Bubble Component
interface MessageBubbleProps {
  message: {
    id: string;
    content: string;
    senderId: string;
    senderName: string;
    timestamp: string;
    isRead: boolean;
    attachments?: string[];
  };
  isOwnMessage: boolean;
  showAvatar?: boolean;
  showTimestamp?: boolean;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwnMessage,
  showAvatar = true,
  showTimestamp = true,
}) => {
  const theme = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Animated.View style={{ opacity: fadeAnim }}>
      <HStack
        space={2}
        alignItems="flex-end"
        justifyContent={isOwnMessage ? 'flex-end' : 'flex-start'}
        mb={2}
        px={4}
      >
        {!isOwnMessage && showAvatar && (
          <Avatar size="sm" bg="primary.500">
            {message.senderName.charAt(0)}
          </Avatar>
        )}
        
        <VStack
          space={1}
          alignItems={isOwnMessage ? 'flex-end' : 'flex-start'}
          maxW={screenWidth * 0.75}
        >
          {!isOwnMessage && (
            <Text fontSize="xs" color="gray.500" ml={2}>
              {message.senderName}
            </Text>
          )}
          
          <Box
            bg={isOwnMessage ? 'primary.500' : 'white'}
            rounded="2xl"
            roundedBottomLeft={isOwnMessage ? '2xl' : 'md'}
            roundedBottomRight={isOwnMessage ? 'md' : '2xl'}
            px={4}
            py={3}
            shadow={1}
            borderWidth={isOwnMessage ? 0 : 1}
            borderColor="gray.100"
          >
            <Text
              fontSize="md"
              color={isOwnMessage ? 'white' : 'gray.800'}
              lineHeight="md"
            >
              {message.content}
            </Text>
          </Box>
          
          {showTimestamp && (
            <HStack space={1} alignItems="center">
              <Text fontSize="xs" color="gray.400">
                {formatTime(message.timestamp)}
              </Text>
              {isOwnMessage && (
                <Icon
                  as={MaterialIcons}
                  name={message.isRead ? 'done-all' : 'done'}
                  size="xs"
                  color={message.isRead ? 'primary.500' : 'gray.400'}
                />
              )}
            </HStack>
          )}
        </VStack>
        
        {isOwnMessage && showAvatar && (
          <Avatar size="sm" bg="success.500">
            You
          </Avatar>
        )}
      </HStack>
    </Animated.View>
  );
};

// Chat Input Component
interface ChatInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  onAttach?: () => void;
  placeholder?: string;
  isLoading?: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChangeText,
  onSend,
  onAttach,
  placeholder = 'Type a message...',
  isLoading = false,
}) => {
  const theme = useTheme();

  return (
    <Box bg="white" px={4} py={3} shadow={2}>
      <HStack space={3} alignItems="flex-end">
        {onAttach && (
          <Pressable onPress={onAttach}>
            <Box
              w={10}
              h={10}
              bg="gray.100"
              rounded="full"
              justifyContent="center"
              alignItems="center"
            >
              <Icon
                as={MaterialIcons}
                name="attach-file"
                size="md"
                color="gray.600"
              />
            </Box>
          </Pressable>
        )}
        
        <Input
          flex={1}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          multiline
          maxHeight={20}
          bg="gray.50"
          borderColor="gray.200"
          borderWidth={1}
          rounded="2xl"
          px={4}
          py={3}
          fontSize="md"
          _focus={{
            bg: 'white',
            borderColor: 'primary.500',
          }}
        />
        
        <Pressable
          onPress={onSend}
          disabled={!value.trim() || isLoading}
        >
          <Box
            w={10}
            h={10}
            bg={value.trim() ? 'primary.500' : 'gray.300'}
            rounded="full"
            justifyContent="center"
            alignItems="center"
          >
            <Icon
              as={MaterialIcons}
              name="send"
              size="md"
              color="white"
            />
          </Box>
        </Pressable>
      </HStack>
    </Box>
  );
};

// Chat Header Component
interface ChatHeaderProps {
  recipientName: string;
  recipientAvatar?: string;
  isOnline?: boolean;
  lastSeen?: string;
  onBack: () => void;
  onCall?: () => void;
  onVideoCall?: () => void;
  onInfo?: () => void;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  recipientName,
  recipientAvatar,
  isOnline = false,
  lastSeen,
  onBack,
  onCall,
  onVideoCall,
  onInfo,
}) => {
  return (
    <Box bg="white" shadow={1} safeAreaTop>
      <HStack
        justifyContent="space-between"
        alignItems="center"
        px={4}
        py={3}
      >
        <HStack space={3} alignItems="center" flex={1}>
          <Pressable onPress={onBack}>
            <Icon
              as={MaterialIcons}
              name="arrow-back"
              size="md"
              color="gray.700"
            />
          </Pressable>
          
          <Avatar
            size="md"
            bg="primary.500"
            source={recipientAvatar ? { uri: recipientAvatar } : undefined}
          >
            {recipientName.charAt(0)}
          </Avatar>
          
          <VStack flex={1}>
            <Text fontSize="lg" fontWeight="semibold" color="gray.800">
              {recipientName}
            </Text>
            <HStack space={1} alignItems="center">
              {isOnline && (
                <Box w={2} h={2} bg="success.500" rounded="full" />
              )}
              <Text fontSize="sm" color="gray.500">
                {isOnline ? 'Online' : lastSeen ? `Last seen ${lastSeen}` : 'Offline'}
              </Text>
            </HStack>
          </VStack>
        </HStack>
        
        <HStack space={2}>
          {onCall && (
            <Pressable onPress={onCall}>
              <Box
                w={10}
                h={10}
                bg="gray.100"
                rounded="full"
                justifyContent="center"
                alignItems="center"
              >
                <Icon
                  as={MaterialIcons}
                  name="call"
                  size="md"
                  color="gray.600"
                />
              </Box>
            </Pressable>
          )}
          
          {onVideoCall && (
            <Pressable onPress={onVideoCall}>
              <Box
                w={10}
                h={10}
                bg="gray.100"
                rounded="full"
                justifyContent="center"
                alignItems="center"
              >
                <Icon
                  as={MaterialIcons}
                  name="videocam"
                  size="md"
                  color="gray.600"
                />
              </Box>
            </Pressable>
          )}
          
          {onInfo && (
            <Pressable onPress={onInfo}>
              <Icon
                as={MaterialIcons}
                name="more-vert"
                size="md"
                color="gray.600"
              />
            </Pressable>
          )}
        </HStack>
      </HStack>
    </Box>
  );
};

// Chat List Item Component
interface ChatListItemProps {
  chat: {
    id: string;
    recipientName: string;
    recipientAvatar?: string;
    lastMessage: string;
    lastMessageTime: string;
    unreadCount: number;
    isOnline: boolean;
    jobTitle?: string;
  };
  onPress: () => void;
}

export const ChatListItem: React.FC<ChatListItemProps> = ({
  chat,
  onPress,
}) => {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <Pressable onPress={onPress}>
      <Box
        bg="white"
        px={4}
        py={3}
        borderBottomWidth={1}
        borderBottomColor="gray.100"
        _pressed={{
          bg: 'gray.50',
        }}
      >
        <HStack space={3} alignItems="center">
          <Box position="relative">
            <Avatar
              size="md"
              bg="primary.500"
              source={chat.recipientAvatar ? { uri: chat.recipientAvatar } : undefined}
            >
              {chat.recipientName.charAt(0)}
            </Avatar>
            {chat.isOnline && (
              <Box
                position="absolute"
                bottom={0}
                right={0}
                w={3}
                h={3}
                bg="success.500"
                rounded="full"
                borderWidth={2}
                borderColor="white"
              />
            )}
          </Box>
          
          <VStack flex={1} space={1}>
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontSize="md" fontWeight="semibold" color="gray.800" flex={1}>
                {chat.recipientName}
              </Text>
              <Text fontSize="xs" color="gray.500">
                {formatTime(chat.lastMessageTime)}
              </Text>
            </HStack>
            
            {chat.jobTitle && (
              <Text fontSize="xs" color="primary.600" fontWeight="medium">
                Job: {chat.jobTitle}
              </Text>
            )}
            
            <HStack justifyContent="space-between" alignItems="center">
              <Text
                fontSize="sm"
                color="gray.600"
                flex={1}
                numberOfLines={1}
              >
                {chat.lastMessage}
              </Text>
              {chat.unreadCount > 0 && (
                <Badge
                  colorScheme="primary"
                  variant="solid"
                  rounded="full"
                  minW={5}
                  h={5}
                  _text={{
                    fontSize: 'xs',
                    fontWeight: 'bold',
                  }}
                >
                  {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                </Badge>
              )}
            </HStack>
          </VStack>
        </HStack>
      </Box>
    </Pressable>
  );
};

// Typing Indicator Component
export const TypingIndicator: React.FC<{ senderName: string }> = ({
  senderName,
}) => {
  const dot1 = useRef(new Animated.Value(0)).current;
  const dot2 = useRef(new Animated.Value(0)).current;
  const dot3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animate = () => {
      Animated.sequence([
        Animated.timing(dot1, { toValue: 1, duration: 400, useNativeDriver: true }),
        Animated.timing(dot2, { toValue: 1, duration: 400, useNativeDriver: true }),
        Animated.timing(dot3, { toValue: 1, duration: 400, useNativeDriver: true }),
        Animated.timing(dot1, { toValue: 0, duration: 400, useNativeDriver: true }),
        Animated.timing(dot2, { toValue: 0, duration: 400, useNativeDriver: true }),
        Animated.timing(dot3, { toValue: 0, duration: 400, useNativeDriver: true }),
      ]).start(() => animate());
    };
    animate();
  }, []);

  return (
    <HStack space={2} alignItems="center" px={4} py={2}>
      <Avatar size="sm" bg="primary.500">
        {senderName.charAt(0)}
      </Avatar>
      <Box
        bg="gray.100"
        rounded="2xl"
        px={4}
        py={3}
      >
        <HStack space={1} alignItems="center">
          <Animated.View
            style={{
              opacity: dot1,
              transform: [{ scale: dot1 }],
            }}
          >
            <Box w={2} h={2} bg="gray.400" rounded="full" />
          </Animated.View>
          <Animated.View
            style={{
              opacity: dot2,
              transform: [{ scale: dot2 }],
            }}
          >
            <Box w={2} h={2} bg="gray.400" rounded="full" />
          </Animated.View>
          <Animated.View
            style={{
              opacity: dot3,
              transform: [{ scale: dot3 }],
            }}
          >
            <Box w={2} h={2} bg="gray.400" rounded="full" />
          </Animated.View>
        </HStack>
      </Box>
    </HStack>
  );
};

export default {
  MessageBubble,
  ChatInput,
  ChatHeader,
  ChatListItem,
  TypingIndicator,
};