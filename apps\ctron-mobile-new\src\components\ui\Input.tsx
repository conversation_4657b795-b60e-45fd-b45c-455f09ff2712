// CTRON Home - Enhanced Input Component
// Modern input component with design system integration

import React, { useState } from 'react';

import { useTheme } from '../../context/ThemeContext';
import { View, Text, TextInput, StyleSheet } from '../../utils/platformUtils';

// Define TextInputProps locally since it's not available in React Native web
interface TextInputProps {
  value?: string;
  onChangeText?: (text: string) => void;
  placeholder?: string;
  multiline?: boolean;
  numberOfLines?: number;
  style?: Record<string, any>;
  onFocus?: () => void;
  onBlur?: () => void;
  secureTextEntry?: boolean;
  keyboardType?: string;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoCorrect?: boolean;
  autoComplete?: string;
  textContentType?: string;
  returnKeyType?: string;
}

export interface InputProps extends TextInputProps {
  label?: string;
  required?: boolean;
  error?: string;
  hint?: string;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  rightIcon?: React.ReactElement;
}

export const Input = (props: InputProps) => {
  const { label, required = false, error, hint, variant = 'default', size = 'medium', style, onFocus, onBlur, rightIcon, ...restProps } = props;
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const { colors, spacing, typography, borderRadius, shadows, sizes } = useTheme();
  const styles = getStyles(colors, spacing, typography, borderRadius, shadows, sizes);

  const inputStyles = [
    styles.input,
    styles[variant],
    styles[`size_${size}`],
    isFocused && styles.focused,
    error && styles.inputError,
    rightIcon && styles.inputWithIcon,
    style,
  ].filter(Boolean);

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      <View style={styles.inputContainer}>
        <TextInput
          style={inputStyles}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholderTextColor={colors.tertiaryLabel}
          selectionColor={colors.primary.main}
          {...restProps}
        />
        {rightIcon && (
          <View style={styles.rightIconContainer}>
            {rightIcon}
          </View>
        )}
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
      {hint && !error && <Text style={styles.hintText}>{hint}</Text>}
    </View>
  );
};

const getStyles = (colors: any, spacing: any, typography: any, borderRadius: any, shadows: any, sizes: any) => StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  },

  label: {
    ...typography.text.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },

  required: {
    color: colors.error.main,
  },

  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },

  input: {
    borderWidth: 1,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...typography.text.md,
    color: colors.text.primary,
    minHeight: sizes.touchTarget,
    ...shadows.sm,
    flex: 1,
  },

  inputWithIcon: {
    paddingRight: spacing.xl,
  },

  rightIconContainer: {
    position: 'absolute',
    right: spacing.md,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Variants
  default: {
    backgroundColor: colors.background.secondary,
    borderColor: colors.border.primary,
  },

  outlined: {
    backgroundColor: colors.background.primary,
    borderColor: colors.border.primary,
  },

  filled: {
    backgroundColor: colors.background.secondary,
    borderColor: 'transparent',
  },

  // Sizes
  size_small: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    minHeight: sizes.inputHeight.sm,
    ...typography.text.sm,
  },

  size_medium: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 44,
    ...typography.text.md,
  },

  size_large: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    minHeight: sizes.inputHeight.lg,
    ...typography.text.lg,
  },

  // States
  focused: {
    borderColor: colors.primary.main,
    borderWidth: 2,
    ...shadows.md,
  },

  inputError: {
    borderColor: colors.error.main,
    borderWidth: 2,
  },

  errorText: {
    ...typography.text.xs,
    color: colors.error.main,
    marginTop: spacing.xs,
  },

  hintText: {
    ...typography.text.xs,
    color: colors.text.tertiary,
    marginTop: spacing.xs,
  },
});

