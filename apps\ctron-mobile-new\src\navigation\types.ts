import { NativeStackScreenProps } from '@react-navigation/native-stack';

export type RootStackParamList = {
  Auth: undefined;
  HomeownerStack: undefined;
  TechnicianStack: undefined;
  AdminStack: undefined;
};

export type HomeownerStackParamList = {
  Home: undefined;
  MyJobs: undefined;
  JobDetails: { jobId: string };
  EditJob: { jobId: string };
  Payment: { jobId: string };
  BookJob: undefined;
  ChatList: undefined;
  Chat: {
    chatId: string;
    jobTitle: string;
    recipientId?: string;
  };
  Profile: undefined;
  TestNB: undefined; // Test screen for NativeBase integration
};

export type ChatScreenProps = NativeStackScreenProps<HomeownerStackParamList, 'Chat'>;
export type ChatScreenRouteProp = ChatScreenProps['route'];