// CTRON Home - Modern Book Job Screen with NativeBase
// Enhanced booking experience with professional UI components

import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  ScrollView,
  Button,
  Input,
  TextArea,
  Select,
  FormControl,
  Badge,
  Avatar,
  Icon,
  Pressable,
  Modal,
  useTheme,
  useToast,
  Progress,
  Divider,
} from 'native-base';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import DateTimePicker from '@react-native-community/datetimepicker';

import { JobAPI, JobCreatePayload } from '../../api/job.api';
import { TechnicianAPI } from '../../api/technician.api';
import type { HomeownerStackParamList } from '../../navigation/types';
import { Platform } from '../../utils/platformUtils';

type BookJobScreenNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'BookJob'>;

interface ServiceType {
  id: string;
  name: string;
  description: string;
  icon: string;
  estimatedDuration: string;
  basePrice: number;
  category: string;
}

interface Technician {
  id: string;
  fullName: string;
  specialization: string;
  rating?: number;
  distance?: number;
  completedJobs?: number;
  hourlyRate?: number;
  isAvailable: boolean;
  profileImage?: string;
  skills?: string[];
  responseTime?: string;
}

const serviceTypes: ServiceType[] = [
  {
    id: '1',
    name: 'Plumbing',
    description: 'Pipes, leaks, installations',
    icon: 'plumbing',
    estimatedDuration: '1-3 hours',
    basePrice: 80,
    category: 'Home Maintenance'
  },
  {
    id: '2',
    name: 'Electrical',
    description: 'Wiring, outlets, lighting',
    icon: 'electrical-services',
    estimatedDuration: '1-4 hours',
    basePrice: 90,
    category: 'Home Maintenance'
  },
  {
    id: '3',
    name: 'HVAC',
    description: 'Heating, cooling, ventilation',
    icon: 'ac-unit',
    estimatedDuration: '2-6 hours',
    basePrice: 120,
    category: 'Climate Control'
  },
  {
    id: '4',
    name: 'Appliance Repair',
    description: 'Washing machines, fridges, ovens',
    icon: 'kitchen',
    estimatedDuration: '1-2 hours',
    basePrice: 70,
    category: 'Appliances'
  },
  {
    id: '5',
    name: 'General Handyman',
    description: 'Various home repairs',
    icon: 'handyman',
    estimatedDuration: '1-4 hours',
    basePrice: 60,
    category: 'General'
  },
  {
    id: '6',
    name: 'Cleaning',
    description: 'Deep cleaning, maintenance',
    icon: 'cleaning-services',
    estimatedDuration: '2-4 hours',
    basePrice: 50,
    category: 'Cleaning'
  }
];

const urgencyLevels = [
  { id: 'low', label: 'Low', description: 'Within a week', color: 'gray' },
  { id: 'medium', label: 'Medium', description: 'Within 2-3 days', color: 'warning' },
  { id: 'high', label: 'High', description: 'Within 24 hours', color: 'error' },
  { id: 'urgent', label: 'Emergency', description: 'ASAP', color: 'error' }
];

const ModernBookJobScreenNB: React.FC = () => {
  const navigation = useNavigation<BookJobScreenNavigationProp>();
  const toast = useToast();
  const theme = useTheme();

  // Form state
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedService, setSelectedService] = useState<ServiceType | null>(null);
  const [issue, setIssue] = useState('');
  const [urgency, setUrgency] = useState('medium');
  const [scheduledAt, setScheduledAt] = useState<Date>(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [address, setAddress] = useState('');
  const [selectedTechnician, setSelectedTechnician] = useState<Technician | null>(null);
  const [loading, setLoading] = useState(false);
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [loadingTechnicians, setLoadingTechnicians] = useState(false);

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  const totalSteps = 4;
  const progressPercentage = (currentStep / totalSteps) * 100;

  useEffect(() => {
    if (currentStep === 3 && selectedService) {
      loadTechnicians();
    }
  }, [currentStep, selectedService]);

  const loadTechnicians = async () => {
    setLoadingTechnicians(true);
    try {
      // Mock technicians data - replace with actual API call
      const mockTechnicians: Technician[] = [
        {
          id: '1',
          fullName: 'Mike Johnson',
          specialization: 'Licensed Plumber',
          rating: 4.8,
          distance: 2.5,
          completedJobs: 127,
          hourlyRate: 85,
          isAvailable: true,
          skills: ['Emergency Repairs', 'Pipe Installation'],
          responseTime: 'Usually responds in 15 mins',
        },
        {
          id: '2',
          fullName: 'Sarah Wilson',
          specialization: 'Certified Electrician',
          rating: 4.9,
          distance: 3.2,
          completedJobs: 89,
          hourlyRate: 95,
          isAvailable: true,
          skills: ['Wiring', 'Smart Home Setup'],
          responseTime: 'Usually responds in 30 mins',
        },
      ];
      setTechnicians(mockTechnicians);
    } catch (error) {
      toast.show({
        title: 'Error',
        description: 'Failed to load technicians',
        status: 'error',
      });
    } finally {
      setLoadingTechnicians(false);
    }
  };

  const validateStep = () => {
    const newErrors: Record<string, string> = {};

    switch (currentStep) {
      case 1:
        if (!selectedService) {
          newErrors.service = 'Please select a service type';
        }
        break;
      case 2:
        if (!issue.trim()) {
          newErrors.issue = 'Please describe your issue';
        }
        if (!address.trim()) {
          newErrors.address = 'Please enter your address';
        }
        break;
      case 3:
        if (!selectedTechnician) {
          newErrors.technician = 'Please select a technician';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      if (currentStep < totalSteps) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      navigation.goBack();
    }
  };

  const handleSubmit = async () => {
    if (!selectedService || !selectedTechnician) {
      toast.show({
        title: 'Error',
        description: 'Please complete all required fields',
        status: 'error',
      });
      return;
    }

    setLoading(true);
    try {
      const jobData: JobCreatePayload = {
        issue,
        scheduledAt: scheduledAt.toISOString(),
        technicianId: selectedTechnician.id,
        serviceType: selectedService.name,
        urgency,
        address
      };

      await JobAPI.createJob(jobData);
      
      toast.show({
        title: 'Success!',
        description: 'Your job has been booked successfully',
        status: 'success',
      });
      
      navigation.navigate('Home');
    } catch (error) {
      toast.show({
        title: 'Error',
        description: 'Failed to book the job. Please try again.',
        status: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const ServiceCard = ({ service, isSelected, onPress }: {
    service: ServiceType;
    isSelected: boolean;
    onPress: () => void;
  }) => (
    <Pressable onPress={onPress} flex={1}>
      <Box
        bg={isSelected ? 'primary.50' : 'white'}
        borderWidth={2}
        borderColor={isSelected ? 'primary.500' : 'gray.200'}
        rounded="xl"
        p={4}
        shadow={isSelected ? 3 : 1}
        _pressed={{
          bg: isSelected ? 'primary.100' : 'gray.50',
        }}
      >
        <VStack space={3} alignItems="center">
          <Box
            bg={isSelected ? 'primary.500' : 'gray.100'}
            rounded="full"
            p={3}
          >
            <Icon
              as={MaterialIcons}
              name={service.icon}
              size="lg"
              color={isSelected ? 'white' : 'gray.600'}
            />
          </Box>
          <VStack space={1} alignItems="center">
            <Text
              fontSize="md"
              fontWeight="semibold"
              color={isSelected ? 'primary.700' : 'gray.700'}
              textAlign="center"
            >
              {service.name}
            </Text>
            <Text fontSize="xs" color="gray.500" textAlign="center">
              {service.description}
            </Text>
            <Text fontSize="sm" color="primary.600" fontWeight="medium">
              from £{service.basePrice}
            </Text>
          </VStack>
          {isSelected && (
            <Icon
              as={MaterialIcons}
              name="check-circle"
              size="sm"
              color="primary.500"
            />
          )}
        </VStack>
      </Box>
    </Pressable>
  );

  const TechnicianCard = ({ technician, isSelected, onPress }: {
    technician: Technician;
    isSelected: boolean;
    onPress: () => void;
  }) => (
    <Pressable onPress={onPress}>
      <Box
        bg={isSelected ? 'primary.50' : 'white'}
        borderWidth={2}
        borderColor={isSelected ? 'primary.500' : 'gray.200'}
        rounded="xl"
        p={4}
        mb={3}
        shadow={isSelected ? 3 : 1}
        _pressed={{
          bg: isSelected ? 'primary.100' : 'gray.50',
        }}
      >
        <VStack space={3}>
          <HStack space={3} alignItems="center">
            <Avatar
              size="md"
              bg="primary.500"
              source={technician.profileImage ? { uri: technician.profileImage } : undefined}
            >
              {technician.fullName.charAt(0)}
            </Avatar>
            <VStack flex={1} space={1}>
              <Text fontSize="lg" fontWeight="semibold" color="gray.800">
                {technician.fullName}
              </Text>
              <Text fontSize="sm" color="gray.600">
                {technician.specialization}
              </Text>
              <HStack space={4} alignItems="center">
                <HStack space={1} alignItems="center">
                  <Icon as={MaterialIcons} name="star" size="sm" color="yellow.400" />
                  <Text fontSize="sm" color="gray.600">
                    {technician.rating}
                  </Text>
                </HStack>
                <Text fontSize="sm" color="gray.500">
                  {technician.distance} km away
                </Text>
              </HStack>
            </VStack>
            <VStack alignItems="flex-end" space={1}>
              <Text fontSize="lg" fontWeight="bold" color="primary.600">
                £{technician.hourlyRate}/hr
              </Text>
              <Badge colorScheme="success" variant="solid" size="sm">
                Available
              </Badge>
            </VStack>
          </HStack>
          
          {technician.skills && (
            <HStack space={2} flexWrap="wrap">
              {technician.skills.slice(0, 2).map((skill, index) => (
                <Badge key={index} colorScheme="gray" variant="subtle" size="sm">
                  {skill}
                </Badge>
              ))}
            </HStack>
          )}
          
          <Text fontSize="xs" color="gray.500">
            {technician.responseTime}
          </Text>
          
          {isSelected && (
            <Box position="absolute" top={2} right={2}>
              <Icon
                as={MaterialIcons}
                name="check-circle"
                size="sm"
                color="primary.500"
              />
            </Box>
          )}
        </VStack>
      </Box>
    </Pressable>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <VStack space={6}>
            <VStack space={2}>
              <Text fontSize="2xl" fontWeight="bold" color="gray.800">
                What service do you need?
              </Text>
              <Text fontSize="md" color="gray.600">
                Choose the type of service you're looking for
              </Text>
            </VStack>
            
            <VStack space={4}>
              {serviceTypes.reduce((rows: ServiceType[][], service, index) => {
                if (index % 2 === 0) {
                  rows.push(serviceTypes.slice(index, index + 2));
                }
                return rows;
              }, []).map((row, rowIndex) => (
                <HStack key={rowIndex} space={3}>
                  {row.map((service) => (
                    <ServiceCard
                      key={service.id}
                      service={service}
                      isSelected={selectedService?.id === service.id}
                      onPress={() => setSelectedService(service)}
                    />
                  ))}
                </HStack>
              ))}
            </VStack>
            
            {errors.service && (
              <Text fontSize="sm" color="error.500">
                {errors.service}
              </Text>
            )}
          </VStack>
        );

      case 2:
        return (
          <VStack space={6}>
            <VStack space={2}>
              <Text fontSize="2xl" fontWeight="bold" color="gray.800">
                Describe your issue
              </Text>
              <Text fontSize="md" color="gray.600">
                Provide details to help us match you with the right technician
              </Text>
            </VStack>
            
            <VStack space={4}>
              <FormControl isRequired isInvalid={!!errors.issue}>
                <FormControl.Label>Issue Description</FormControl.Label>
                <TextArea
                  placeholder="Describe the problem in detail..."
                  value={issue}
                  onChangeText={setIssue}
                  numberOfLines={4}
                  autoCompleteType="off"
                />
                <FormControl.ErrorMessage>
                  {errors.issue}
                </FormControl.ErrorMessage>
              </FormControl>

              <FormControl isRequired isInvalid={!!errors.address}>
                <FormControl.Label>Address</FormControl.Label>
                <Input
                  placeholder="Enter your address"
                  value={address}
                  onChangeText={setAddress}
                  leftElement={
                    <Icon
                      as={MaterialIcons}
                      name="location-on"
                      size="sm"
                      color="gray.400"
                      ml={3}
                    />
                  }
                />
                <FormControl.ErrorMessage>
                  {errors.address}
                </FormControl.ErrorMessage>
              </FormControl>

              <FormControl>
                <FormControl.Label>Urgency Level</FormControl.Label>
                <Select
                  selectedValue={urgency}
                  onValueChange={setUrgency}
                  placeholder="Select urgency"
                >
                  {urgencyLevels.map((level) => (
                    <Select.Item
                      key={level.id}
                      label={`${level.label} - ${level.description}`}
                      value={level.id}
                    />
                  ))}
                </Select>
              </FormControl>

              <FormControl>
                <FormControl.Label>Preferred Date & Time</FormControl.Label>
                <HStack space={2}>
                  <Button
                    flex={1}
                    variant="outline"
                    leftIcon={<Icon as={MaterialIcons} name="calendar-today" size="sm" />}
                    onPress={() => setShowDatePicker(true)}
                  >
                    {scheduledAt.toLocaleDateString()}
                  </Button>
                  <Button
                    flex={1}
                    variant="outline"
                    leftIcon={<Icon as={MaterialIcons} name="access-time" size="sm" />}
                    onPress={() => setShowTimePicker(true)}
                  >
                    {scheduledAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </Button>
                </HStack>
              </FormControl>
            </VStack>
          </VStack>
        );

      case 3:
        return (
          <VStack space={6}>
            <VStack space={2}>
              <Text fontSize="2xl" fontWeight="bold" color="gray.800">
                Choose your technician
              </Text>
              <Text fontSize="md" color="gray.600">
                Select from available technicians in your area
              </Text>
            </VStack>
            
            {loadingTechnicians ? (
              <VStack space={4} alignItems="center" py={8}>
                <Text fontSize="md" color="gray.600">
                  Finding technicians...
                </Text>
              </VStack>
            ) : (
              <VStack space={3}>
                {technicians.map((technician) => (
                  <TechnicianCard
                    key={technician.id}
                    technician={technician}
                    isSelected={selectedTechnician?.id === technician.id}
                    onPress={() => setSelectedTechnician(technician)}
                  />
                ))}
              </VStack>
            )}
            
            {errors.technician && (
              <Text fontSize="sm" color="error.500">
                {errors.technician}
              </Text>
            )}
          </VStack>
        );

      case 4:
        return (
          <VStack space={6}>
            <VStack space={2}>
              <Text fontSize="2xl" fontWeight="bold" color="gray.800">
                Confirm your booking
              </Text>
              <Text fontSize="md" color="gray.600">
                Review your details before confirming
              </Text>
            </VStack>
            
            <Box bg="white" rounded="xl" shadow={2} p={4}>
              <VStack space={4}>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="gray.500">Service</Text>
                  <Text fontSize="md" fontWeight="semibold">{selectedService?.name}</Text>
                </HStack>
                
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="gray.500">Technician</Text>
                  <Text fontSize="md" fontWeight="semibold">{selectedTechnician?.fullName}</Text>
                </HStack>
                
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="gray.500">Date & Time</Text>
                  <Text fontSize="md" fontWeight="semibold">
                    {scheduledAt.toLocaleDateString()} at {scheduledAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </Text>
                </HStack>
                
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="gray.500">Address</Text>
                  <Text fontSize="md" fontWeight="semibold" flex={1} textAlign="right">
                    {address}
                  </Text>
                </HStack>
                
                <Divider />
                
                <HStack justifyContent="space-between">
                  <Text fontSize="lg" fontWeight="bold">Estimated Cost</Text>
                  <Text fontSize="xl" fontWeight="bold" color="primary.600">
                    £{selectedService?.basePrice}
                  </Text>
                </HStack>
              </VStack>
            </Box>
          </VStack>
        );

      default:
        return null;
    }
  };

  return (
    <Box flex={1} bg="gray.50" safeArea>
      {/* Header */}
      <HStack
        justifyContent="space-between"
        alignItems="center"
        px={4}
        py={3}
        bg="white"
        shadow={1}
      >
        <Button
          variant="ghost"
          leftIcon={<Icon as={MaterialIcons} name="arrow-back" size="sm" />}
          onPress={handleBack}
        >
          Back
        </Button>
        <Text fontSize="lg" fontWeight="semibold">
          Book Service
        </Text>
        <Box w={16} />
      </HStack>

      {/* Progress Bar */}
      <Box px={4} py={3} bg="white">
        <VStack space={2}>
          <Progress value={progressPercentage} colorScheme="primary" size="sm" />
          <Text fontSize="sm" color="gray.600" textAlign="center">
            Step {currentStep} of {totalSteps}
          </Text>
        </VStack>
      </Box>

      {/* Content */}
      <ScrollView flex={1} px={4} py={6}>
        {renderStepContent()}
      </ScrollView>

      {/* Bottom Actions */}
      <Box px={4} py={4} bg="white" shadow={1}>
        <Button
          colorScheme="primary"
          size="lg"
          onPress={currentStep === totalSteps ? handleSubmit : handleNext}
          isLoading={loading}
          loadingText={currentStep === totalSteps ? 'Booking...' : 'Loading...'}
        >
          {currentStep === totalSteps ? 'Confirm Booking' : 'Continue'}
        </Button>
      </Box>

      {/* Date/Time Pickers */}
      {showDatePicker && (
        <DateTimePicker
          value={scheduledAt}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={(event, selectedDate) => {
            setShowDatePicker(false);
            if (selectedDate && event.type === 'set') {
              const newDate = new Date(scheduledAt);
              newDate.setFullYear(selectedDate.getFullYear());
              newDate.setMonth(selectedDate.getMonth());
              newDate.setDate(selectedDate.getDate());
              setScheduledAt(newDate);
            }
          }}
        />
      )}

      {showTimePicker && (
        <DateTimePicker
          value={scheduledAt}
          mode="time"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={(event, selectedTime) => {
            setShowTimePicker(false);
            if (selectedTime && event.type === 'set') {
              const newDate = new Date(scheduledAt);
              newDate.setHours(selectedTime.getHours());
              newDate.setMinutes(selectedTime.getMinutes());
              setScheduledAt(newDate);
            }
          }}
        />
      )}
    </Box>
  );
};

export default ModernBookJobScreenNB;