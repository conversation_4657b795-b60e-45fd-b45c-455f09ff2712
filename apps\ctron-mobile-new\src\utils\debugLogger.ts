/**
 * Debug Logger Utility
 * 
 * A centralized logging utility for development debugging.
 * This helps maintain consistent logging across the application
 * and can be easily configured for different environments.
 */

import { isWeb } from './platformUtils';

// Log levels
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

// Configuration for the logger
const config = {
  // Enable/disable logging based on environment
  enabled: process.env.NODE_ENV !== 'production',
  // Minimum log level to display
  minLevel: LogLevel.DEBUG
};

// Color codes for different log levels
const colors = {
  [LogLevel.DEBUG]: '\x1b[36m', // Cyan
  [LogLevel.INFO]: '\x1b[32m',  // Green
  [LogLevel.WARN]: '\x1b[33m',  // Yellow
  [LogLevel.ERROR]: '\x1b[31m', // Red
  reset: '\x1b[0m'
};

/**
 * Format a log message with timestamp and level
 */
const formatMessage = (level: LogLevel, message: string, ..._args: unknown[]): string => {
  const timestamp = new Date().toISOString();
  return `[${timestamp}] [${level}] ${message}`;
};

/**
 * Log a message with the specified level
 */
const log = (level: LogLevel, message: string, ...args: unknown[]) => {
  if (!config.enabled) return;
  
  // Check if the log level is high enough to be displayed
  const levels = Object.values(LogLevel);
  if (levels.indexOf(level) < levels.indexOf(config.minLevel)) return;
  
  const formattedMessage = formatMessage(level, message, ...args);
  
  // Use different console methods based on level
  switch (level) {
    case LogLevel.ERROR:
      console.error(isWeb ? formattedMessage : `${colors[level]}${formattedMessage}${colors.reset}`, ...args);
      break;
    case LogLevel.WARN:
      console.warn(isWeb ? formattedMessage : `${colors[level]}${formattedMessage}${colors.reset}`, ...args);
      break;
    case LogLevel.INFO:
      console.info(isWeb ? formattedMessage : `${colors[level]}${formattedMessage}${colors.reset}`, ...args);
      break;
    case LogLevel.DEBUG:
    default:
      console.debug(isWeb ? formattedMessage : `${colors[level]}${formattedMessage}${colors.reset}`, ...args);
      break;
  }
};

/**
 * Debug logger utility functions
 */
export const debugLogger = {
  debug: (message: string, ...args: unknown[]) => log(LogLevel.DEBUG, message, ...args),
  info: (message: string, ...args: unknown[]) => log(LogLevel.INFO, message, ...args),
  warn: (message: string, ...args: unknown[]) => log(LogLevel.WARN, message, ...args),
  error: (message: string, ...args: unknown[]) => log(LogLevel.ERROR, message, ...args),
  // Alias for info - used in some parts of the codebase
  success: (message: string, ...args: unknown[]) => log(LogLevel.INFO, message, ...args),
  
  // Group related logs together (using info instead of console.group)
  group: (label: string) => {
    if (config.enabled) console.info(`📁 ${label}`);
  },
  groupEnd: () => {
    if (config.enabled) console.info('📁 End group');
  },
  
  // Measure time between operations (using info instead of console.time)
  time: (label: string) => {
    if (config.enabled) {
      const timestamp = Date.now();
      (globalThis as Record<string, unknown>).__debugTimers = (globalThis as Record<string, unknown>).__debugTimers || {};
      ((globalThis as Record<string, unknown>).__debugTimers as Record<string, number>)[label] = timestamp;
      console.info(`⏱️ Timer started: ${label}`);
    }
  },
  timeEnd: (label: string) => {
    if (config.enabled) {
      const timers = ((globalThis as Record<string, unknown>).__debugTimers as Record<string, number>) || {};
      const startTime = timers[label];
      if (startTime) {
        const duration = Date.now() - startTime;
        console.info(`⏱️ Timer ended: ${label} - ${duration}ms`);
        delete timers[label];
      } else {
        console.info(`⏱️ Timer not found: ${label}`);
      }
    }
  },
  
  // Configure the logger
  configure: (options: { enabled?: boolean; minLevel?: LogLevel }) => {
    if (options.enabled !== undefined) config.enabled = options.enabled;
    if (options.minLevel !== undefined) config.minLevel = options.minLevel;
  }
};