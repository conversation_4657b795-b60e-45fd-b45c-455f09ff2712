/**
 * This script fixes common errors that might occur with Expo SDK 52
 * It's run before starting the development server
 */

const fs = require('fs');
const path = require('path');

const chalk = require('chalk');

console.log(chalk.blue('Running pre-start error fixes...'));

// Function to check if a directory exists
function directoryExists(dirPath) {
  try {
    return fs.statSync(dirPath).isDirectory();
  } catch (err) {
    return false;
  }
}

// Function to create directory if it doesn't exist
function ensureDirectoryExists(dirPath) {
  if (!directoryExists(dirPath)) {
    console.log(chalk.yellow(`Creating directory: ${dirPath}`));
    fs.mkdirSync(dirPath, { recursive: true });
    return true;
  }
  return false;
}

// Create necessary directories
const directories = [
  path.join(__dirname, '..', '.metro-cache'),
  path.join(__dirname, '..', 'src', 'shims')
];

directories.forEach(dir => {
  if (ensureDirectoryExists(dir)) {
    console.log(chalk.green(`Created directory: ${dir}`));
  }
});

// Create necessary shim files
const shimFiles = [
  {
    path: path.join(__dirname, '..', 'src', 'shims', 'empty-module.js'),
    content: `// Empty module shim for web compatibility\nexport default {};
`
  },
  {
    path: path.join(__dirname, '..', 'src', 'shims', 'TurboModuleRegistry.web.js'),
    content: `// TurboModuleRegistry shim for web compatibility
export function get(name) {
  return null;
}

export function getEnforcing(name) {
  // Return an empty object that satisfies the TurboModule interface
  return {};
}
`
  },
  {
    path: path.join(__dirname, '..', 'src', 'shims', 'NativeAnimatedModule.web.js'),
    content: `// NativeAnimatedModule shim for web compatibility\nexport default null;
`
  },
  {
    path: path.join(__dirname, '..', 'src', 'shims', 'NativeAnimatedHelper.web.js'),
    content: `// NativeAnimatedHelper shim for web compatibility\nexport const API = {};
export const nativeEventEmitter = {};
`
  }
];

shimFiles.forEach(file => {
  if (!fs.existsSync(file.path)) {
    console.log(chalk.yellow(`Creating shim file: ${file.path}`));
    fs.writeFileSync(file.path, file.content);
    console.log(chalk.green(`Created shim file: ${file.path}`));
  }
});

// Check if native-modules.json exists, create if not
const nativeModulesPath = path.join(__dirname, '..', 'config');
ensureDirectoryExists(nativeModulesPath);

const nativeModulesFile = path.join(nativeModulesPath, 'native-modules.json');
if (!fs.existsSync(nativeModulesFile)) {
  console.log(chalk.yellow(`Creating native-modules.json`));
  const nativeModulesContent = {
    modules: [
      "@react-native-community/datetimepicker",
      "expo-secure-store",
      "expo-notifications",
      "expo-location",
      "expo-image-picker",
      "expo-document-picker",
      "expo-background-task",
      "expo-task-manager",
      "react-native/Libraries/TurboModule/TurboModuleRegistry",
      "react-native/Libraries/Animated/NativeAnimatedModule",
      "react-native/Libraries/Animated/NativeAnimatedHelper"
    ]
  };
  fs.writeFileSync(nativeModulesFile, JSON.stringify(nativeModulesContent, null, 2));
  console.log(chalk.green(`Created native-modules.json`));
}

console.log(chalk.green('Pre-start error fixes completed successfully!'));