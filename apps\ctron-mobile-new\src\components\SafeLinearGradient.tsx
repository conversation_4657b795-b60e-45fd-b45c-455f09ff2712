/**
 * Safe LinearGradient Component
 * 
 * This component provides a safe wrapper around expo-linear-gradient
 * to prevent bubblingEventTypes errors in React Native's new architecture.
 * It includes fallback implementations and error boundaries.
 */

import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const ReactNative = require('react-native');
const { View } = ReactNative;
import type { ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

// Define the props interface
interface SafeLinearGradientProps {
  colors: readonly [string, string, ...string[]];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  locations?: readonly [number, number, ...number[]] | null;
  useAngle?: boolean;
  angle?: number;
  angleCenter?: { x: number; y: number };
  style?: ViewStyle;
  children?: React.ReactNode;
}

// Fallback component using CSS-like gradients
const FallbackGradient: React.FC<SafeLinearGradientProps> = ({ 
  colors, 
  style, 
  children 
}) => {
  // Create a simple fallback using the first color
  const fallbackStyle: ViewStyle = {
    ...style,
    backgroundColor: Array.isArray(colors) && colors.length > 0 ? colors[0] : '#000000',
  };

  return (
    <View style={fallbackStyle}>
      {children}
    </View>
  );
};

// Main SafeLinearGradient component
const SafeLinearGradient: React.FC<SafeLinearGradientProps> = (props) => {
  try {
    // Use expo-linear-gradient which is more stable with Expo SDK
    // Ensure colors is always a tuple with at least two colors
    const safeProps = {
      ...props,
      // If only one color is provided, duplicate it to meet the type requirement
      colors: props.colors.length === 1 ? [props.colors[0], props.colors[0]] as const : props.colors,
      // Ensure locations is a readonly tuple with at least two numbers if provided
      locations: props.locations ? 
        (props.locations.length === 1 ? 
          [props.locations[0], props.locations[0]] as const : 
          props.locations) : 
        null
    };
    return <LinearGradient {...safeProps} />;
  } catch (error) {
    // If there's any error with LinearGradient, use fallback
    if (__DEV__) {
      console.warn('SafeLinearGradient: Falling back to solid color due to error:', error);
    }
    return <FallbackGradient {...props} />;
  }
};

export default SafeLinearGradient;
export type { SafeLinearGradientProps };