module.exports = function (api) {
  api.cache(true);
  
  return {
    presets: [
      'babel-preset-expo'
    ],
    plugins: [
      // Module resolver for path aliases
      [
        'module-resolver',
        {
          root: ['./src'],
          alias: {
            '@': './src',
            '@components': './src/components',
            '@screens': './src/screens',
            '@utils': './src/utils',
            '@hooks': './src/hooks',
            '@services': './src/services',
            '@api': './src/api',
            '@types': './src/types',
            '@navigation': './src/navigation',
            '@context': './src/context',
            '@styles': './src/styles',
            '@theme': './src/theme',
            '@config': './src/config',
            '@shims': './src/shims',
            // Handle react-dom for NativeBase compatibility
            'react-dom': './react-dom-shim.js',
            'react-dom/client': './react-dom-shim.js',
            'react-dom/server': './react-dom-shim.js'
          },
          extensions: ['.tsx', '.ts', '.js', '.jsx', '.json']
        }
      ]
    ]
  };
};