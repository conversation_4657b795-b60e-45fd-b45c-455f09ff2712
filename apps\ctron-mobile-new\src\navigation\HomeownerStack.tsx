//src/navigation/HomownerStack.tsx

import { createNativeStackNavigator } from '@react-navigation/native-stack';


import ChatListScreen from '../screens/Chat/ChatListScreen';
import ChatScreen from '../screens/Chat/ChatScreen';
// Original screens (keeping as backup)
import ModernBookJobScreen from '../screens/Homeowner/ModernBookJobScreen';
import ModernEditJobScreen from '../screens/Homeowner/ModernEditJobScreen';
import ModernHomeScreen from '../screens/Homeowner/ModernHomeScreen';
import ModernJobDetailsScreen from '../screens/Homeowner/ModernJobDetailsScreen';
import ModernMyJobsScreen from '../screens/Homeowner/ModernMyJobsScreen';
import ModernPaymentScreen from '../screens/Homeowner/ModernPaymentScreen';
// New NativeBase screens
import ModernHomeScreenNB from '../screens/Homeowner/ModernHomeScreenNB';
import ModernBookJobScreenNB from '../screens/Homeowner/ModernBookJobScreenNB';
import ModernMyJobsScreenNB from '../screens/Homeowner/ModernMyJobsScreenNB';
import TestNativeBaseScreen from '../screens/TestNativeBaseScreen';

import { HomeownerStackParamList } from './types';

const Stack = createNativeStackNavigator<HomeownerStackParamList>();

const HomeownerStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    {/* New NativeBase Screens - Primary */}
    <Stack.Screen name="Home" component={ModernHomeScreenNB} />
    <Stack.Screen name="MyJobs" component={ModernMyJobsScreenNB} />
    <Stack.Screen name="BookJob" component={ModernBookJobScreenNB} />
    
    {/* Test Screen for NativeBase Integration */}
    <Stack.Screen name="TestNB" component={TestNativeBaseScreen} />
    
    {/* Existing Screens - Keeping for now */}
    <Stack.Screen name="JobDetails" component={ModernJobDetailsScreen} />
    <Stack.Screen name="EditJob" component={ModernEditJobScreen} />
    <Stack.Screen name="Payment" component={ModernPaymentScreen} />
    <Stack.Screen name="ChatList" component={ChatListScreen} />
    <Stack.Screen name="Chat" component={ChatScreen} />
  </Stack.Navigator>
);

export default HomeownerStack;
