// Test Screen for NativeBase Integration
// Simple test to verify NativeBase is working correctly

import { MaterialIcons } from '@expo/vector-icons';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Avatar,
  Badge,
  Icon,
  Progress,
  Input,
  Select,
  useTheme,
  useToast,
} from 'native-base';
import React from 'react';

import { ServiceCard, JobStatusCard } from '../components/modern';

const TestNativeBaseScreen: React.FC = () => {
  const theme = useTheme();
  const toast = useToast();

  const showToast = () => {
    toast.show({
      title: 'Success!',
      description: 'NativeBase is working perfectly!',
      status: 'success',
    });
  };

  const mockService = {
    id: '1',
    title: 'Emergency Plumbing',
    description: 'Quick fixes for leaks, clogs, and pipe issues',
    category: 'Plumbing',
    price: 85,
    status: 'available' as const,
    rating: 4.8,
    reviewCount: 127,
    estimatedTime: '1-2 hours',
    technician: {
      id: '1',
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      specialization: 'Licensed Plumber',
      isVerified: true,
    },
  };

  const mockJob = {
    id: '1',
    title: 'Kitchen Sink Repair',
    description: 'Fix leaking kitchen sink',
    status: 'in_progress' as const,
    priority: 'medium' as const,
    scheduledDate: '2025-01-21T10:00:00Z',
    estimatedDuration: '2 hours',
    address: '123 Main St, London',
    price: 85,
    progress: 65,
    technician: {
      id: '1',
      name: 'Mike <PERSON>',
      phone: '+44 7700 900123',
      rating: 4.8,
    },
  };

  return (
    <Box flex={1} bg="gray.50" safeArea>
      <VStack space={6} p={4}>
        {/* Header */}
        <Box bg="white" rounded="xl" shadow={2} p={4}>
          <VStack space={4}>
            <Text fontSize="2xl" fontWeight="bold" color="gray.800">
              NativeBase Test Screen 🎉
            </Text>
            <Text fontSize="md" color="gray.600">
              Testing all NativeBase components and CTRON theme
            </Text>
          </VStack>
        </Box>

        {/* Basic Components Test */}
        <Box bg="white" rounded="xl" shadow={2} p={4}>
          <VStack space={4}>
            <Text fontSize="lg" fontWeight="semibold" color="gray.800">
              Basic Components
            </Text>
            
            <HStack space={3} alignItems="center">
              <Avatar bg="primary.500" size="md">
                MJ
              </Avatar>
              <VStack flex={1}>
                <Text fontSize="md" fontWeight="semibold">
                  Mike Johnson
                </Text>
                <Text fontSize="sm" color="gray.600">
                  Licensed Plumber
                </Text>
              </VStack>
              <Badge colorScheme="success" variant="solid">
                Available
              </Badge>
            </HStack>

            <Progress value={75} colorScheme="primary" size="md" />

            <HStack space={3}>
              <Button flex={1} colorScheme="primary" onPress={showToast}>
                Primary
              </Button>
              <Button flex={1} variant="outline" colorScheme="primary">
                Outline
              </Button>
            </HStack>

            <Input
              placeholder="Test input field"
              leftElement={
                <Icon
                  as={MaterialIcons}
                  name="search"
                  size="sm"
                  color="gray.400"
                  ml={3}
                />
              }
            />

            <Select placeholder="Choose option">
              <Select.Item label="Option 1" value="1" />
              <Select.Item label="Option 2" value="2" />
              <Select.Item label="Option 3" value="3" />
            </Select>
          </VStack>
        </Box>

        {/* Custom Components Test */}
        <VStack space={4}>
          <Text fontSize="lg" fontWeight="semibold" color="gray.800">
            Custom CTRON Components
          </Text>
          
          <ServiceCard
            service={mockService}
            onPress={() => showToast()}
            onBookPress={() => showToast()}
            variant="compact"
          />

          <JobStatusCard
            job={mockJob}
            onPress={() => showToast()}
            onContactPress={() => showToast()}
            onTrackPress={() => showToast()}
            variant="compact"
          />
        </VStack>

        {/* Theme Colors Test */}
        <Box bg="white" rounded="xl" shadow={2} p={4}>
          <VStack space={4}>
            <Text fontSize="lg" fontWeight="semibold" color="gray.800">
              CTRON Theme Colors
            </Text>
            
            <HStack space={2} flexWrap="wrap">
              <Box bg="primary.500" w={12} h={12} rounded="md" />
              <Box bg="secondary.500" w={12} h={12} rounded="md" />
              <Box bg="success.500" w={12} h={12} rounded="md" />
              <Box bg="warning.500" w={12} h={12} rounded="md" />
              <Box bg="error.500" w={12} h={12} rounded="md" />
            </HStack>
            
            <Text fontSize="sm" color="gray.600">
              All theme colors are working correctly!
            </Text>
          </VStack>
        </Box>

        {/* Success Message */}
        <Box bg="success.50" borderColor="success.200" borderWidth={1} rounded="xl" p={4}>
          <HStack space={3} alignItems="center">
            <Icon as={MaterialIcons} name="check-circle" color="success.500" size="lg" />
            <VStack flex={1}>
              <Text fontSize="md" fontWeight="semibold" color="success.700">
                NativeBase Integration Successful!
              </Text>
              <Text fontSize="sm" color="success.600">
                All components are working with the CTRON theme
              </Text>
            </VStack>
          </HStack>
        </Box>
      </VStack>
    </Box>
  );
};

export default TestNativeBaseScreen;