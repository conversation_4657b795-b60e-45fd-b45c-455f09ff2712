// src/screens/Technician/ActiveJobScreen.tsx
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';
import React, { useMemo, useState, useEffect, useCallback } from 'react';

import { JobAPI } from '../../api/job.api';
import { withScreenErrorBoundary } from '../../components/ScreenErrorBoundary';
import { getPresignedUrl, uploadToS3 } from '../../services/uploadService';
import type { Job } from '../../types/job';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator, Image } from '../../utils/platformUtils';
import { socket } from '../../utils/socket';

interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
}

type RouteParams = {
  jobId: string;
};

const ActiveJobScreen = () => {
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const navigation = useNavigation();
  const { jobId } = route.params;

  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [proofImage, setProofImage] = useState<string | null>(null);

  const fetchJob = useCallback(async () => {
    try {
      const res = await JobAPI.getJobDetails(jobId);
      setJob(res);
    } catch (err: unknown) {
      const apiError = err as ApiError;
      Alert.alert('Error', apiError?.response?.data?.message || 'Failed to load job');
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  useEffect(() => {
    fetchJob();
  }, [fetchJob]);

  const selectProofImage = async () => {
    const permission = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permission.granted) {
      return Alert.alert('Permission needed', 'Camera roll access is required.');
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 0.5,
    });

    if (!result.canceled && result.assets && result.assets[0]) {
      setProofImage(result.assets[0].uri);
    }
  };

  const updateStatus = async (status: 'COMPLETED' | 'IN_PROGRESS') => {
    if (status === 'COMPLETED' && !proofImage) {
      return Alert.alert('Proof Required', 'Please upload a photo before completing.');
    }

    try {
      setUpdating(true);
      let photoUrl: string | undefined;

      if (status === 'COMPLETED' && proofImage) {
        const fileType = 'image/jpeg';
        const { url, key } = await getPresignedUrl(fileType);
        await uploadToS3(url, proofImage, fileType);

        // Construct public S3 URL
        photoUrl = `https://${process.env.EXPO_PUBLIC_S3_BUCKET}.s3.${process.env.EXPO_PUBLIC_AWS_REGION}.amazonaws.com/${key}`;
      }

      // Send update to backend
      await JobAPI.updateStatus(jobId, status, { photoUrl });

      // Emit socket update
      socket.emit('jobStatusUpdated', jobId, status);

      Alert.alert('Success', `Marked as ${status}`);
      navigation.goBack();
    } catch (err: unknown) {
      const apiError = err as ApiError;
      Alert.alert('Error', apiError?.response?.data?.message || 'Update failed');
    } finally {
      setUpdating(false);
    }
  };

  if (loading || !job) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#004AAD" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Job #{job.id.slice(0, 8)}</Text>
      <Text style={styles.label}>Status:</Text>
      <Text style={styles.value}>{job.status}</Text>

      <Text style={styles.label}>Issue:</Text>
      <Text style={styles.value}>{job.issue}</Text>

      <Text style={styles.label}>Scheduled At:</Text>
      <Text style={styles.value}>
        {new Date(job.scheduledAt).toLocaleString()}
      </Text>

      <TouchableOpacity style={styles.uploadBtn} onPress={selectProofImage}>
        <Text style={styles.uploadBtnText}>
          {proofImage ? 'Change Photo' : 'Select Proof Photo'}
        </Text>
      </TouchableOpacity>

      {proofImage && (
        <Image source={{ uri: proofImage }} style={styles.preview} />
      )}

      {job.status !== 'COMPLETED' && (
        <TouchableOpacity
          style={[styles.button, updating && { opacity: 0.6 }]}
          onPress={() => updateStatus('COMPLETED')}
          disabled={updating}
        >
          <Text style={styles.buttonText}>
            {updating ? 'Uploading & Completing...' : 'Mark as Completed'}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
  },
  value: {
    fontSize: 16,
    marginBottom: 8,
  },
  uploadBtn: {
    backgroundColor: '#004AAD',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    alignItems: 'center',
  },
  uploadBtnText: {
    color: '#fff',
    fontWeight: '600',
  },
  preview: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginTop: 12,
    alignSelf: 'center',
  },
  button: {
    backgroundColor: '#10B981',
    padding: 14,
    borderRadius: 8,
    marginTop: 20,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default withScreenErrorBoundary(ActiveJobScreen, 'ActiveJob');

