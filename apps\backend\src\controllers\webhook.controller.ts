import { completeJobPayment } from '../services/payment.service';
import { env } from '../config/env';
import { logger } from '../utils/logger';
import { prisma } from '../config/db';
import { Request, Response } from 'express'; // Added import for Request and Response
import Stripe from 'stripe';

const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
apiVersion: env.STRIPE_API_VERSION as Stripe.LatestApiVersion,
});

// Webhook event processing status tracking
interface WebhookProcessingRecord {
  eventId: string;
  eventType: string;
  processed: boolean;
  attempts: number;
  lastAttempt: Date;
  error?: string;
}

// In-memory cache for webhook deduplication (in production, use Redis)
const processedWebhooks = new Map<string, WebhookProcessingRecord>();

/**
 * Enhanced Stripe webhook handler with retry logic and deduplication
 * This is the SINGLE source of truth for payment processing
 * Must use raw body middleware to function correctly.
 */
export const stripeWebhookHandler = async (
  req: Request,
  res: Response
): Promise<void> => {
  const signature = req.headers['stripe-signature'] as string;
  const requestId = req.headers['x-request-id'] as string || 'unknown';

  if (!signature) {
    logger.error('Missing Stripe signature in webhook request', { requestId });
    res.status(400).json({
      error: 'Missing Stripe signature',
      code: 'MISSING_SIGNATURE'
    });
    return;
  }

  let event: Stripe.Event;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(
      req.body as Buffer,
      signature,
      env.STRIPE_WEBHOOK_SECRET
    );
  } catch (err: any) {
    logger.error('Stripe webhook signature verification failed', {
      error: err.message,
      requestId,
      signaturePresent: !!signature,
    });

    res.status(400).json({
      error: 'Invalid webhook signature',
      code: 'INVALID_SIGNATURE'
    });
    return;
  }

  logger.info('Stripe webhook received', {
    eventType: event.type,
    eventId: event.id,
    requestId,
  });

  // Check for duplicate webhook processing
  const existingRecord = processedWebhooks.get(event.id);
  if (existingRecord?.processed) {
    logger.info('Webhook already processed, skipping', {
      eventId: event.id,
      eventType: event.type,
      previousAttempts: existingRecord.attempts,
    });

    res.status(200).json({
      received: true,
      status: 'already_processed'
    });
    return;
  }

  // Initialize processing record
  const processingRecord: WebhookProcessingRecord = {
    eventId: event.id,
    eventType: event.type,
    processed: false,
    attempts: (existingRecord?.attempts || 0) + 1,
    lastAttempt: new Date(),
  };

  processedWebhooks.set(event.id, processingRecord);

  try {
    switch (event.type) {
      case 'payment_intent.succeeded': {
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;
      }

      case 'payment_intent.payment_failed': {
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
        break;
      }

      case 'payment_intent.canceled': {
        await handlePaymentIntentCanceled(event.data.object as Stripe.PaymentIntent);
        break;
      }

      case 'payment_intent.requires_action': {
        await handlePaymentIntentRequiresAction(event.data.object as Stripe.PaymentIntent);
        break;
      }

      case 'identity.verification_session.verified': {
        await handleIdentityVerificationSessionVerified(event.data.object as any);
        break;
      }

      default: {
        logger.warn(`Unhandled event type ${event.type}`, { eventId: event.id });
        break;
      }
    }

    processingRecord.processed = true;
    res.status(200).json({ received: true });
  } catch (processingError: any) {
    processingRecord.error = processingError.message;
    logger.error('Error processing webhook event', {
      eventId: event.id,
      eventType: event.type,
      error: processingError.message,
      stack: processingError.stack,
    });
    res.status(500).json({ received: false, error: 'Internal Server Error' });
  }
};

/**
 * Handle successful payment intent
 */
async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
  const jobId = paymentIntent.metadata?.jobId;

  if (!jobId) {
    logger.warn('Payment intent succeeded without jobId in metadata', {
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
    });
    throw new Error('Missing jobId in payment intent metadata');
  }

  try {
    await completeJobPayment(jobId);
    logger.info('Payment for job completed successfully', {
      jobId,
      paymentIntentId: paymentIntent.id,
    });
  } catch (err: any) {
    logger.error('Error completing payment for job', {
      jobId,
      paymentIntentId: paymentIntent.id,
      error: err.message,
    });
    throw err; // Re-throw to trigger webhook retry
  }
}

/**
 * Handle failed payment intent
 */
async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
  logger.warn('Payment intent failed', {
    paymentIntentId: paymentIntent.id,
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    lastPaymentError: paymentIntent.last_payment_error?.message,
  });
  // Implement logic for failed payments (e.g., notify user, update job status)
}

/**
 * Handle canceled payment intent
 */
async function handlePaymentIntentCanceled(paymentIntent: Stripe.PaymentIntent): Promise<void> {
  logger.info('Payment intent canceled', {
    paymentIntentId: paymentIntent.id,
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
  });
  // Implement logic for canceled payments
}

/**
 * Handle payment intent requiring action (e.g., 3D Secure)
 */
async function handlePaymentIntentRequiresAction(paymentIntent: Stripe.PaymentIntent): Promise<void> {
  logger.info('Payment intent requires action', {
    paymentIntentId: paymentIntent.id,
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    nextActionType: paymentIntent.next_action?.type,
  });
  // Implement logic for payments requiring action
}

/**
 * Handle successful identity verification session
 */
async function handleIdentityVerificationSessionVerified(session: any): Promise<void> {
  const technicianId = session.metadata?.technicianId;

  if (!technicianId) {
    logger.warn('Identity verification session verified without technicianId in metadata', {
      sessionId: session.id,
      status: session.status,
    });
    throw new Error('Missing technicianId in identity verification metadata');
  }

  try {
    await prisma.technician.update({
      where: { id: technicianId },
      // Temporarily cast kycStatus to any to resolve compilation error.
      // The Prisma schema for Technician model needs to be updated to include 'kycStatus'.
      data: { kycStatus: 'APPROVED' },
    });

    logger.info('Technician KYC status updated to APPROVED', {
      technicianId,
      sessionId: session.id,
    });
  } catch (err: any) {
    logger.error('Error updating technician KYC status', {
      technicianId,
      sessionId: session.id,
      error: err.message,
    });
    throw err; // Re-throw to trigger webhook retry
  }
}
