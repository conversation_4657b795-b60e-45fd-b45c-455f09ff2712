/**
 * This script generates the native-modules.json configuration file
 * It analyzes the project to identify native modules that need special handling for web
 */

const fs = require('fs');
const path = require('path');

const chalk = require('chalk');
const glob = require('glob');

console.log(chalk.blue('Generating native modules configuration...'));

// Get all JavaScript and TypeScript files in the src directory
const srcDir = path.join(__dirname, '..', 'src');
const files = glob.sync(path.join(srcDir, '**/*.{js,jsx,ts,tsx}'));

// Known native modules that need special handling for web
const knownNativeModules = [
  '@react-native-community/datetimepicker',
  'expo-secure-store',
  'expo-notifications',
  'expo-location',
  'expo-image-picker',
  'expo-document-picker',
  'expo-background-task',
  'expo-task-manager',
  'react-native/Libraries/TurboModule/TurboModuleRegistry',
  'react-native/Libraries/Animated/NativeAnimatedModule',
  'react-native/Libraries/Animated/NativeAnimatedHelper'
];

// Regular expression to match import statements
const importRegex = /import[\s\n]+[^;]*?[\s\n]+from[\s\n]+['"]([^'"]*)['"]|require\(['"]([^'"]*)['"]\)/g;

// Set to store detected modules
const detectedModules = new Set(knownNativeModules);

// Scan files for imports
files.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    const moduleName = match[1] || match[2];
    
    // Check if this is a native module that needs special handling
    if (moduleName && (
      moduleName.startsWith('react-native/') ||
      moduleName.startsWith('@react-native') ||
      moduleName.startsWith('expo-') ||
      moduleName === 'react-native-gesture-handler' ||
      moduleName === 'react-native-reanimated'
    )) {
      detectedModules.add(moduleName);
    }
  }
});

// Create the config directory if it doesn't exist
const configDir = path.join(__dirname, '..', 'config');
if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
}

// Write the native-modules.json file
const nativeModulesFile = path.join(configDir, 'native-modules.json');
const nativeModulesConfig = {
  modules: Array.from(detectedModules).sort()
};

fs.writeFileSync(nativeModulesFile, JSON.stringify(nativeModulesConfig, null, 2));

console.log(chalk.green(`Generated native-modules.json with ${nativeModulesConfig.modules.length} modules`));
console.log(chalk.cyan('Modules detected:'));
nativeModulesConfig.modules.forEach(module => {
  console.log(chalk.white(`  - ${module}`));
});

console.log(chalk.blue('\nNative modules configuration generation completed.'));