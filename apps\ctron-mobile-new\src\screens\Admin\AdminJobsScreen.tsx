// CTRON Home - Admin Jobs Management Screen
// Comprehensive job management interface for administrators

import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useCallback, useMemo } from 'react';


import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Header } from '../../components/ui/Header';
import { useTheme } from '../../context/ThemeContext';
import api from '../../services/api';
import { typography, borderRadius, spacing } from '../../styles/theme';
import { AdminStackParamList } from '../../types/navigation';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Alert, ActivityIndicator } from '../../utils/platformUtils';

interface Job {
  id: string;
  issue: string;
  status: 'PENDING' | 'ASSIGNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  priority: 'low' | 'medium' | 'high';
  scheduledAt: string;
  createdAt: string;
  user: {
    fullName: string;
    email: string;
  };
  technician?: {
    user: {
      fullName: string;
    };
  };
  payment?: {
    amount: number;
    isReleased: boolean;
    isFrozen: boolean;
  };
}

type AdminJobsScreenNavigationProp = StackNavigationProp<AdminStackParamList, 'AdminJobs'>;

export default function AdminJobsScreen() {
  const { colors } = useTheme();
  const navigation = useNavigation<AdminJobsScreenNavigationProp>();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);

  const [filter, setFilter] = useState<'all' | 'pending' | 'active' | 'completed'>('all');

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.secondary,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      marginTop: spacing.lg,
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
    },
    filterContainer: {
      flexDirection: 'row',
      padding: spacing.lg,
      gap: spacing.sm,
    },
    filterButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
      backgroundColor: colors.background.primary,
      borderWidth: 1,
      borderColor: colors.border.light,
    },
    filterButtonActive: {
      backgroundColor: colors.primary.main,
      borderColor: colors.primary.main,
    },
    filterButtonText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      fontWeight: '500' as const,
    },
    filterButtonTextActive: {
      color: colors.background.primary,
    },
    listContainer: {
      padding: spacing.lg,
      paddingTop: 0,
    },
    jobCard: {
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    jobHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.md,
    },
    jobInfo: {
      flex: 1,
      marginRight: spacing.md,
    },
    jobTitle: {
      fontSize: typography.fontSize.base,
      fontWeight: '600' as const,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    jobCustomer: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    jobTechnician: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    jobMeta: {
      alignItems: 'flex-end',
      gap: spacing.sm,
    },
    statusBadge: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
    },
    statusText: {
      fontSize: typography.fontSize.xs,
      color: colors.background.primary,
      fontWeight: '600' as const,
    },
    priorityBadge: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
    },
    priorityText: {
      fontSize: typography.fontSize.xs,
      color: colors.background.primary,
      fontWeight: '600' as const,
    },
    jobDetails: {
      marginBottom: spacing.md,
    },
    jobDate: {
      fontSize: typography.fontSize.sm,
      color: colors.text.tertiary,
      marginBottom: spacing.xs,
    },
    paymentSection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border.light,
    },
    paymentAmount: {
      fontSize: typography.fontSize.base,
      fontWeight: '600' as const,
      color: colors.text.primary,
    },
    paymentActions: {
      flexDirection: 'row',
      gap: spacing.sm,
      alignItems: 'center',
    },
    paymentButton: {
      minWidth: 80,
    },
    frozenText: {
      fontSize: typography.fontSize.sm,
      color: colors.error.main || '#ef4444',
      fontWeight: '600' as const,
    },
    releasedText: {
      fontSize: typography.fontSize.sm,
      color: colors.success.main || '#10b981',
      fontWeight: '600' as const,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: spacing[8],
    },
    emptyText: {
      fontSize: typography.fontSize.base,
      color: colors.text.tertiary,
    },
    backIcon: {
      fontSize: 24,
      color: colors.text.secondary,
    },
  }), [colors, spacing]);

  const loadJobs = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/jobs', {
        params: filter !== 'all' ? { status: filter } : {},
      });
      setJobs((response.data as { jobs: Job[] }).jobs || []);
    } catch (error: unknown) {
      console.error('Failed to load jobs:', error);
      Alert.alert('Error', 'Failed to load jobs. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [filter]);

  useEffect(() => {
    loadJobs();
  }, [loadJobs]);



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return colors.warning.main || '#f59e0b';
      case 'ASSIGNED': return colors.info?.main || '#3b82f6';
      case 'IN_PROGRESS': return colors.primary.main;
      case 'COMPLETED': return colors.success.main || '#10b981';
      case 'CANCELLED': return colors.error.main || '#ef4444';
      default: return colors.text.secondary;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return colors.error.main || '#ef4444';
      case 'medium': return colors.warning.main || '#f59e0b';
      case 'low': return colors.success.main || '#10b981';
      default: return colors.text.secondary;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const handleJobPress = useCallback((jobId: string) => {
    navigation.navigate('AdminJobDetails', { jobId });
  }, [navigation]);

  const handlePaymentAction = useCallback(async (jobId: string, action: 'release' | 'freeze') => {
    try {
      const endpoint = action === 'release'
        ? `/api/payments/${jobId}/release-manually`
        : `/api/payments/${jobId}/freeze`;

      await api.patch(endpoint);
      Alert.alert('Success', `Payment ${action}d successfully`);
      loadJobs(); // Refresh the list
    } catch (error: unknown) {
      Alert.alert('Error', `Failed to ${action} payment`);
    }
  }, [loadJobs]);

  const renderFilterButton = (filterType: typeof filter, label: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        filter === filterType && styles.filterButtonActive,
      ]}
      onPress={() => setFilter(filterType)}
    >
      <Text
        style={[
          styles.filterButtonText,
          filter === filterType && styles.filterButtonTextActive,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderJobItem = ({ item }: { item: Job }) => (
    <TouchableOpacity onPress={() => handleJobPress(item.id)}>
      <Card style={styles.jobCard}>
        <View style={styles.jobHeader}>
          <View style={styles.jobInfo}>
            <Text style={styles.jobTitle} numberOfLines={2}>
              {item.issue}
            </Text>
            <Text style={styles.jobCustomer}>
              Customer: {item.user.fullName}
            </Text>
            {item.technician && (
              <Text style={styles.jobTechnician}>
                Technician: {item.technician.user.fullName}
              </Text>
            )}
          </View>
          <View style={styles.jobMeta}>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Text style={styles.statusText}>{item.status}</Text>
            </View>
            <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}>
              <Text style={styles.priorityText}>{item.priority.toUpperCase()}</Text>
            </View>
          </View>
        </View>

        <View style={styles.jobDetails}>
          <Text style={styles.jobDate}>
            Scheduled: {formatDate(item.scheduledAt)}
          </Text>
          <Text style={styles.jobDate}>
            Created: {formatDate(item.createdAt)}
          </Text>
        </View>

        {item.payment && (
          <View style={styles.paymentSection}>
            <Text style={styles.paymentAmount}>
              Amount: {formatCurrency(item.payment.amount)}
            </Text>
            <View style={styles.paymentActions}>
              {!item.payment.isReleased && !item.payment.isFrozen && (
                <Button
                  title="Release"
                  onPress={() => handlePaymentAction(item.id, 'release')}
                  variant="primary"
                  size="sm"
                  style={styles.paymentButton}
                />
              )}
              {!item.payment.isFrozen && (
                <Button
                  title="Freeze"
                  onPress={() => handlePaymentAction(item.id, 'freeze')}
                  variant="outline"
                  size="sm"
                  style={styles.paymentButton}
                />
              )}
              {item.payment.isFrozen && (
                <Text style={styles.frozenText}>FROZEN</Text>
              )}
              {item.payment.isReleased && (
                <Text style={styles.releasedText}>RELEASED</Text>
              )}
            </View>
          </View>
        )}
      </Card>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Loading jobs...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="Job Management"
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: 'Go back',
        }}
      />

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {renderFilterButton('all', 'All')}
        {renderFilterButton('pending', 'Pending')}
        {renderFilterButton('active', 'Active')}
        {renderFilterButton('completed', 'Completed')}
      </View>

      <FlatList
        data={jobs}
        renderItem={renderJobItem}
        keyExtractor={(item: Job) => item.id}
        contentContainerStyle={styles.listContainer}
        // Pull to refresh functionality can be added here if needed
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No jobs found</Text>
          </View>
        }
      />
    </View>
  );
}

