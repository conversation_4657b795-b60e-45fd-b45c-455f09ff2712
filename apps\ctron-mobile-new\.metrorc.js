/**
 * Metro bundler configuration for improved performance
 * This file configures Metro's global settings
 */
const os = require('os');

// Load the Babel warning suppression script
require('./scripts/suppress-babel-warnings');

module.exports = {
  // Increase the maximum number of files to watch
  maxWorkers: Math.max(os.cpus().length, 8),
  
  // Enable ES Module support
  resolver: {
    unstable_enablePackageExports: true,
  },
  
  // Enable web worker support for web platform
  transformer: {
    unstable_allowRequireContext: true,
  },
  
  // Increase the file watching timeout (in ms)
  watchFolders: {
    healthCheck: {
      enabled: true,
      interval: 5000,
      timeout: 500,
    },
  },
  
  // Configure caching for better performance
  // Using FileStore instead of custom store to avoid "store.clear is not a function" error
  cacheStores: [
    {
      name: 'metro',
      type: 'FileStore',
      // Increase cache size to 1GB
      maxSize: 1024 * 1024 * 1024, // 1GB in bytes
    },
  ],
  
  // Optimize startup performance
  resetCache: false,
  
  // Reduce file system polling for faster startup
  fileWatcher: {
    useWatchman: true,
    pollingInterval: 2000,
  },
  
  // Configure server for better performance
  server: {
    port: 8081,
    useGlobalHotkey: true,
    enhanceMiddleware: (middleware) => middleware,
  },
  
  // Configure reporter for better performance
  reporter: {
    update: {
      interval: 500,
    },
  },
};