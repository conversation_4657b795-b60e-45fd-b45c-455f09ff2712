// Utility functions for handling assets
import { Asset } from 'expo-asset';

import resolveAssetSource from './resolveAssetSource';

// Type definitions for asset sources
type AssetSource = number | any | string | { uri: string };

interface ResolvedAsset {
  uri: string;
  width: number | null;
  height: number | null;
  scale: number;
}

/**
 * Preload assets to ensure they're available when needed
 */
export const preloadAssets = async (assets: AssetSource[]): Promise<any[]> => {
  if (!assets || !assets.length) return [];
  
  try {
    return await Promise.all(assets.map(asset => (Asset as any).loadAsync(asset)));
  } catch (error) {
    console.error('Error preloading assets:', error);
    return [];
  }
};

/**
 * Resolve an asset source to get its URI and dimensions
 */
export const getAssetSource = (source: AssetSource): ResolvedAsset | null => {
  return resolveAssetSource(source);
};

/**
 * Get the URI from an asset source
 */
export const getAssetUri = (source: AssetSource): string | null => {
  const resolved = getAssetSource(source);
  return resolved ? (resolved.uri || null) : null;
};