# CTRON Mobile App - NativeBase Troubleshooting Guide

## 🚨 Common Issues and Solutions

This guide addresses common issues encountered during NativeBase integration and provides step-by-step solutions.

## 📱 Bundling Issues

### Issue 1: "Unable to resolve react-dom" Error

**Error Message:**
```
Unable to resolve "react-dom" from "node_modules\@react-aria\utils\dist\animation.main.js"
```

**Root Cause:**
NativeBase uses @react-aria packages that expect `react-dom` to be available, but React Native doesn't include it by default.

**Solution:**
1. **Add react-dom dependency:**
   ```bash
   yarn add react-dom@18.3.1
   ```

2. **Use the provided react-dom shim:**
   The project includes `react-dom-shim.js` that provides a minimal react-dom implementation for React Native.

3. **Metro config is already configured** to use the shim:
   ```javascript
   config.resolver.alias = {
     'react-dom': path.resolve(__dirname, 'react-dom-shim.js'),
     'react-dom/client': path.resolve(__dirname, 'react-dom-shim.js'),
     'react-dom/server': path.resolve(__dirname, 'react-dom-shim.js')
   };
   ```

4. **Clear cache and restart:**
   ```bash
   yarn start:reset
   ```

### Issue 2: Metro Bundling Timeout

**Error Message:**
```
Metro bundling failed after 60000ms
```

**Solution:**
1. **Increase Metro timeout:**
   ```bash
   npx expo start --max-workers 1
   ```

2. **Clear all caches:**
   ```bash
   yarn cache:clean
   npx expo start --clear
   ```

3. **Use optimized start script:**
   ```bash
   yarn start:optimized
   ```

### Issue 3: Socket.IO Client Node.js Module Error

**Error Message:**
```
The package at "node_modules\socket.io-client\node_modules\debug\src\node.js" attempted to import the Node standard library module "tty".
It failed because the native React runtime does not include the Node standard library.
```

**Root Cause:**
Socket.io-client uses Node.js modules (tty, util, os, fs, path, crypto) that aren't available in React Native.

**Solution:**
1. **Run the automated fixer:**
   ```bash
   yarn fix:socketio
   ```

2. **Manual fix - Node.js polyfills are provided:**
   - `node-polyfills/tty.js` - TTY module polyfill
   - `node-polyfills/util.js` - Util module polyfill
   - `node-polyfills/os.js` - OS module polyfill
   - `node-polyfills/fs.js` - File system polyfill
   - `node-polyfills/path.js` - Path module polyfill
   - `node-polyfills/crypto.js` - Crypto module polyfill

3. **Metro config includes aliases:**
   ```javascript
   config.resolver.alias = {
     'tty': path.resolve(__dirname, 'node-polyfills/tty.js'),
     'util': path.resolve(__dirname, 'node-polyfills/util.js'),
     // ... other polyfills
   };
   ```

4. **Clear cache and restart:**
   ```bash
   yarn start --clear
   ```

### Issue 4: @react-aria Package Issues

**Error Message:**
```
Unable to resolve module @react-aria/...
```

**Solution:**
1. **Ensure transpilePackages includes @react-aria:**
   ```javascript
   transpilePackages: [
     'native-base',
     '@react-aria/utils',
     '@react-stately/utils'
   ]
   ```

2. **Add to resolver alias if needed:**
   ```javascript
   config.resolver.alias = {
     '@react-aria/utils': path.resolve(__dirname, 'node_modules/@react-aria/utils'),
   };
   ```

## 🔧 Development Issues

### Issue 5: TypeScript Errors with NativeBase

**Error Message:**
```
Property 'colorScheme' does not exist on type...
```

**Solution:**
1. **Ensure NativeBase types are properly imported:**
   ```typescript
   import { NativeBaseProvider, extendTheme } from 'native-base';
   ```

2. **Check theme configuration:**
   ```typescript
   const theme = extendTheme({
     // Your theme configuration
   });
   ```

3. **Restart TypeScript server:**
   - In VS Code: `Ctrl+Shift+P` → "TypeScript: Restart TS Server"

### Issue 6: Component Not Rendering

**Symptoms:**
- Components appear blank
- No styling applied
- Console errors about theme

**Solution:**
1. **Verify NativeBaseProvider wrapper:**
   ```typescript
   // In App.tsx
   import { NativeBaseProvider } from 'native-base';
   import { ctronTheme } from './src/theme/nativeBaseTheme';

   export default function App() {
     return (
       <NativeBaseProvider theme={ctronTheme}>
         {/* Your app content */}
       </NativeBaseProvider>
     );
   }
   ```

2. **Check theme import:**
   ```typescript
   import { ctronTheme } from './src/theme/nativeBaseTheme';
   ```

3. **Verify component imports:**
   ```typescript
   import { Box, Text, Button } from 'native-base';
   ```

## 📱 Platform-Specific Issues

### Issue 7: iOS Build Failures

**Error Message:**
```
Build failed with CocoaPods errors
```

**Solution:**
1. **Clean iOS build:**
   ```bash
   cd ios && rm -rf Pods Podfile.lock && cd ..
   npx pod-install
   ```

2. **Update iOS deployment target:**
   ```ruby
   # In ios/Podfile
   platform :ios, '11.0'
   ```

### Issue 7: Android Build Failures

**Error Message:**
```
Android build failed with Gradle errors
```

**Solution:**
1. **Clean Android build:**
   ```bash
   cd android && ./gradlew clean && cd ..
   ```

2. **Update Android compile SDK:**
   ```gradle
   // In android/app/build.gradle
   compileSdkVersion 33
   targetSdkVersion 33
   ```

## 🎨 Styling Issues

### Issue 8: Theme Not Applied

**Symptoms:**
- Default NativeBase styling instead of CTRON theme
- Colors not matching brand guidelines

**Solution:**
1. **Verify theme structure:**
   ```typescript
   const ctronTheme = extendTheme({
     colors: {
       primary: {
         50: '#E3F2FD',
         500: '#2196F3', // Main CTRON blue
         900: '#0D47A1',
       }
     }
   });
   ```

2. **Check theme provider:**
   ```typescript
   <NativeBaseProvider theme={ctronTheme}>
   ```

3. **Use theme colors correctly:**
   ```typescript
   <Box bg="primary.500"> // Correct
   <Box bg="#2196F3">    // Avoid hardcoded colors
   ```

### Issue 9: Responsive Design Issues

**Symptoms:**
- Layout breaks on different screen sizes
- Components not adapting to screen dimensions

**Solution:**
1. **Use NativeBase responsive props:**
   ```typescript
   <Box
     w={{ base: "100%", md: "50%" }}
     p={{ base: 4, md: 6 }}
   >
   ```

2. **Test on multiple screen sizes:**
   ```bash
   # Test on different simulators
   npx expo start
   # Press 'i' for iOS, 'a' for Android
   ```

## 🔍 Debugging Tools

### Debug Scripts

Add these scripts to your workflow:

```bash
# Check bundle size
yarn analyze-bundle

# Type checking
yarn typecheck

# Lint checking
yarn lint:check

# Start with debugging
yarn start --dev --minify=false
```

### Debug Components

Create a debug screen to test components:

```typescript
// src/screens/DebugScreen.tsx
import React from 'react';
import { Box, Text, Button, VStack } from 'native-base';

export const DebugScreen = () => {
  return (
    <Box flex={1} bg="gray.50" safeArea p={4}>
      <VStack space={4}>
        <Text fontSize="xl">Debug Screen</Text>
        <Button colorScheme="primary">Test Button</Button>
        <Box bg="primary.100" p={4} rounded="md">
          <Text>Test Box with Theme Colors</Text>
        </Box>
      </VStack>
    </Box>
  );
};
```

## 📊 Performance Issues

### Issue 10: Slow App Performance

**Symptoms:**
- Slow navigation
- Laggy animations
- High memory usage

**Solution:**
1. **Optimize imports:**
   ```typescript
   // Instead of importing everything
   import { Box, Text } from 'native-base';
   
   // Use specific imports when possible
   import { Box } from 'native-base/src/components/primitives/Box';
   ```

2. **Use React.memo for expensive components:**
   ```typescript
   const ServiceCard = React.memo(({ service }) => {
     // Component implementation
   });
   ```

3. **Optimize FlatList rendering:**
   ```typescript
   <FlatList
     data={services}
     renderItem={renderServiceCard}
     getItemLayout={getItemLayout} // If items have fixed height
     removeClippedSubviews={true}
     maxToRenderPerBatch={10}
   />
   ```

## 🔧 Quick Fixes

### Reset Everything
```bash
# Nuclear option - reset everything
yarn cache:clean
rm -rf node_modules
yarn install
npx expo start --clear
```

### Check Dependencies
```bash
# Check for conflicting dependencies
yarn why react
yarn why react-native
```

### Verify Installation
```bash
# Run the NativeBase validator
node -e "
const { validateNativeBaseSetup } = require('./src/utils/nativeBaseValidator');
validateNativeBaseSetup();
"
```

## 📞 Getting Help

### Before Asking for Help

1. **Check this troubleshooting guide**
2. **Clear cache and restart:** `yarn start:reset`
3. **Check console for specific error messages**
4. **Test on a fresh simulator/device**
5. **Verify all dependencies are installed**

### When Reporting Issues

Include this information:
- **Error message** (full stack trace)
- **Platform** (iOS/Android/both)
- **Device/Simulator** details
- **Steps to reproduce**
- **Expected vs actual behavior**
- **Package versions** (`yarn list native-base`)

### Useful Commands for Debugging

```bash
# Check NativeBase version
yarn list native-base

# Check React version compatibility
yarn list react react-native

# Generate dependency tree
yarn list --depth=0

# Check for duplicate dependencies
yarn list --pattern react

# Clear Metro cache
npx expo start --clear

# Reset Metro cache and node_modules
yarn start:reset
```

## 🎯 Prevention Tips

### Best Practices

1. **Always use the theme system:**
   ```typescript
   // Good
   <Box bg="primary.500" />
   
   // Avoid
   <Box bg="#2196F3" />
   ```

2. **Import components consistently:**
   ```typescript
   import { Box, Text, Button } from 'native-base';
   ```

3. **Test on both platforms regularly:**
   ```bash
   yarn android
   yarn ios
   ```

4. **Keep dependencies updated:**
   ```bash
   yarn upgrade-interactive
   ```

5. **Use TypeScript strictly:**
   ```typescript
   // Enable strict mode in tsconfig.json
   "strict": true
   ```

## 🚀 Performance Optimization

### Bundle Size Optimization

1. **Use tree shaking:**
   ```typescript
   // Import only what you need
   import { Box } from 'native-base';
   ```

2. **Analyze bundle size:**
   ```bash
   yarn analyze-bundle
   ```

3. **Remove unused dependencies:**
   ```bash
   yarn remove unused-package
   ```

### Runtime Optimization

1. **Use React.memo for pure components**
2. **Implement proper key props in lists**
3. **Avoid inline functions in render**
4. **Use useCallback for event handlers**

## ✅ Health Check

Run this checklist to ensure everything is working:

- [ ] App starts without errors
- [ ] NativeBase components render correctly
- [ ] Theme colors are applied
- [ ] Navigation works smoothly
- [ ] No console warnings
- [ ] Performance is acceptable
- [ ] Both iOS and Android work

## 🎉 Success!

If you've followed this guide and resolved your issues:

1. **Document any new issues** you encountered
2. **Share solutions** with the team
3. **Update this guide** if needed
4. **Continue building** awesome features!

Remember: Most NativeBase issues are related to configuration, dependencies, or caching. The solutions in this guide should resolve 95% of common problems.