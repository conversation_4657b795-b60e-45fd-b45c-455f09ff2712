//src/context/TechnicianContext.tsx

import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';

import { TechnicianAPI } from '../api/technician.api';

import { useAuth } from './AuthContext';

export interface Technician {
  id: string;
  userId: string;
  specialization: string;
  rating?: number;
  completedJobs: number;
  isAvailable: boolean;
  hourlyRate?: number;
  distance?: number;
  responseTime?: string;
  skills?: string[];
  user: {
    id: string;
    fullName: string;
    email: string;
    phone?: string;
    profileImage?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface TechnicianContextType {
  technician: Technician | null;
  refreshTechnician: () => Promise<void>;
}

const TechnicianContext = createContext<TechnicianContextType>(null!);

export const TechnicianProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { token } = useAuth();
  const [technician, setTechnician] = useState<Technician | null>(null);

  const refreshTechnician = useCallback(async () => {
    if (!token) return;
    try {
      const res = await TechnicianAPI.getProfile();
      if (res?.technician) {
        setTechnician(res.technician);
      }
    } catch (err) {
      console.error('[TechnicianContext] Failed to load technician profile:', err);
    }
  }, [token]);

  useEffect(() => {
    refreshTechnician();
  }, [token, refreshTechnician]);

  return (
    <TechnicianContext.Provider value={{ technician, refreshTechnician }}>
      {children}
    </TechnicianContext.Provider>
  );
};

export const useTechnician = () => useContext(TechnicianContext);