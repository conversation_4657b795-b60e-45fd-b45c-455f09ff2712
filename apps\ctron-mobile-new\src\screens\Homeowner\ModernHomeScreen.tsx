// CTRON Home - Modern Homeowner Dashboard v2.0
// Redesigned with glassmorphism, modern cards, and contemporary UI patterns - Enhanced Fade Effects

import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import SafeLinearGradient from '../../components/SafeLinearGradient';
import { BlurView } from 'expo-blur';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';

// Removed unused design-system imports to prevent VirtualizedList errors
import { useAuth } from '../../context/AuthContext';
import { useJobs } from '../../context/JobContext';
import { useTheme } from '../../context/ThemeContext';
import type { Job } from '../../types/job';
import type { HomeownerStackParamList } from '../../navigation/types';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  RefreshControl,
  StatusBar,
  SafeAreaView
} from '../../utils/platformUtils';

type HomeScreenNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'Home'>;

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ModernHomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { user, logout } = useAuth();
  const { myJobs = [], refreshJobs, loading } = useJobs();
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'ongoing' | 'completed'>('ongoing');
  const [userStats, setUserStats] = useState({ totalJobs: 0, completedJobs: 0, rating: 0 });
  const [unreadCount, setUnreadCount] = useState(0);
  const isMountedRef = useRef(true);
  const { colors, spacing, typography, borderRadius, shadows } = useTheme();
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerOpacity = useRef(new Animated.Value(1)).current;

  const jobs: Job[] = myJobs;
  const firstName = user?.fullName?.split(' ')[0] || 'Homeowner';

  // Use useMemo for derived values to prevent infinite loops
  const ongoingJobs = useMemo(() => jobs.filter(
    job => job.status === 'PENDING' || job.status === 'IN_PROGRESS' || job.status === 'ACCEPTED'
  ), [jobs]);
  
  const completedJobs = useMemo(() => jobs.filter(job => job.status === 'COMPLETED'), [jobs]);
  const recentJobs = useMemo(() => jobs.slice(0, 3), [jobs]);

  // Calculate real stats from jobs data - only depend on jobs length to prevent infinite loop
  useEffect(() => {
    const totalJobs = jobs.length;
    const completed = jobs.filter(job => job.status === 'COMPLETED').length;
    const rating = completed > 0 ? 4.5 + (completed * 0.1) : 0; // Simple rating calculation
    
    setUserStats({
      totalJobs,
      completedJobs: completed,
      rating: Math.min(rating, 5.0)
    });
  }, [jobs.length, jobs]);

  useEffect(() => {
    refreshJobs();
    
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      })
    ]).start();

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleRefresh = useCallback(async () => {
    if (!isMountedRef.current) return;
    setRefreshing(true);
    await refreshJobs();
    if (isMountedRef.current) {
      setRefreshing(false);
    }
  }, [refreshJobs]);

  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    {
      useNativeDriver: false,
      listener: (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        const headerThreshold = 60;
        const fadeThreshold = 200;
        const maxFade = 300;
        
        // Header fade effect - more gradual
        const headerOpacityValue = Math.max(0.2, 1 - (offsetY / (headerThreshold * 2)));
        Animated.timing(headerOpacity, {
          toValue: headerOpacityValue,
          duration: 150,
          useNativeDriver: true,
        }).start();
        
        // Content fade effect - smoother transition
        let contentOpacity = 1;
        if (offsetY > fadeThreshold) {
          const fadeProgress = Math.min(1, (offsetY - fadeThreshold) / (maxFade - fadeThreshold));
          contentOpacity = Math.max(0.3, 1 - (fadeProgress * 0.7));
        }
        
        Animated.timing(fadeAnim, {
          toValue: contentOpacity,
          duration: 100,
          useNativeDriver: true,
        }).start();
      },
    }
  );

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const QuickActionCard = ({ icon, title, subtitle, onPress, color, gradient }: {
    icon: keyof typeof Ionicons.glyphMap;
    title: string;
    subtitle: string;
    onPress: () => void;
    color: string;
    gradient?: string[];
  }) => (
    <TouchableOpacity style={styles.quickActionCard} onPress={onPress}>
      <View style={styles.glassCard}>
        <BlurView intensity={25} tint="light" />
      </View>
      {gradient && gradient.length >= 2 && (
        <SafeLinearGradient
          colors={gradient as [string, string, ...string[]]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.cardGradient}
        />
      )}
      <View style={styles.quickActionContent}>
        <View style={[styles.iconContainer, { backgroundColor: color + '25', shadowColor: color, shadowOffset: { width: 0, height: 4 }, shadowOpacity: 0.3, shadowRadius: 8, elevation: 6 }]}>
          <Ionicons name={icon} size={26} color={color} />
        </View>
        <Text style={styles.quickActionTitle}>{title}</Text>
        <Text style={styles.quickActionSubtitle}>{subtitle}</Text>
      </View>
    </TouchableOpacity>
  );

  const StatCard = ({ value, label, trend, color, icon }: {
    value: string;
    label: string;
    trend?: string;
    color: string;
    icon?: keyof typeof Ionicons.glyphMap;
  }) => (
    <View style={styles.statCard}>
      <View style={styles.statGlass}>
        <BlurView intensity={20} tint="light" />
      </View>
      <SafeLinearGradient
        colors={[color + '15', color + '05']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.statGradient}
      />
      <View style={styles.statContent}>
        {icon && (
          <View style={[styles.statIcon, { backgroundColor: color + '20' }]}>
            <Ionicons name={icon} size={16} color={color} />
          </View>
        )}
        <Text style={[styles.statValue, { color }]}>{value}</Text>
        <Text style={styles.statLabel}>{label}</Text>
        {trend && (
          <View style={styles.trendContainer}>
            <Ionicons 
              name={trend.startsWith('+') ? 'trending-up' : 'trending-down'} 
              size={12} 
              color={trend.startsWith('+') ? colors.semantic.success : colors.semantic.error} 
            />
            <Text style={[styles.trendText, { 
              color: trend.startsWith('+') ? colors.semantic.success : colors.semantic.error 
            }]}>{trend}</Text>
          </View>
        )}
      </View>
    </View>
  );

  const ModernJobCard = ({ job }: { job: Job }) => {
    const statusColors = {
      'PENDING': '#FF9500',
      'ASSIGNED': '#007AFF',
      'IN_PROGRESS': '#34C759',
      'COMPLETED': '#8E8E93',
      'CANCELLED': '#FF3B30'
    };

    const statusLabels = {
      'PENDING': 'Pending',
      'ASSIGNED': 'Assigned',
      'IN_PROGRESS': 'In Progress',
      'COMPLETED': 'Completed',
      'CANCELLED': 'Cancelled'
    };

    const statusIcons = {
      'PENDING': 'time-outline',
      'ASSIGNED': 'person-outline',
      'IN_PROGRESS': 'construct-outline',
      'COMPLETED': 'checkmark-circle-outline',
      'CANCELLED': 'close-circle-outline'
    };

    return (
      <TouchableOpacity 
        style={styles.modernJobCard}
        onPress={() => navigation.navigate('JobDetails', { jobId: job.id })}
      >
        <View style={styles.jobCardGlass}>
          <BlurView intensity={30} tint="light" />
        </View>
        <SafeLinearGradient
          colors={[statusColors[job.status as keyof typeof statusColors] + '08', 'transparent']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.jobCardGradient}
        />
        <View style={styles.jobCardContent}>
          <View style={styles.jobCardHeader}>
            <View style={styles.statusContainer}>
              <View style={[styles.statusBadge, { backgroundColor: statusColors[job.status as keyof typeof statusColors] + '20' }]}>
                <Ionicons 
                  name={statusIcons[job.status as keyof typeof statusIcons] as keyof typeof Ionicons.glyphMap} 
                  size={12} 
                  color={statusColors[job.status as keyof typeof statusColors]} 
                />
                <Text style={[styles.statusText, { color: statusColors[job.status as keyof typeof statusColors] }]}>
                  {statusLabels[job.status as keyof typeof statusLabels]}
                </Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={16} color={colors.text.secondary} />
          </View>
          
          <Text style={styles.jobTitle} numberOfLines={1}>{job.title || job.issue}</Text>
          <Text style={styles.jobDescription} numberOfLines={2}>
            {job.description || job.issue}
          </Text>
          
          <View style={styles.jobCardFooter}>
            <View style={styles.jobMeta}>
              <View style={styles.metaItem}>
                <Ionicons name="calendar-outline" size={14} color={colors.text.secondary} />
                <Text style={styles.jobDate}>
                  {new Date(job.scheduledAt || job.createdAt).toLocaleDateString()}
                </Text>
              </View>
              {job.technician && (
                <View style={styles.metaItem}>
                  <Ionicons name="person-outline" size={14} color={colors.text.secondary} />
                  <Text style={styles.technicianName} numberOfLines={1}>
                    {job.technician.user.fullName}
                  </Text>
                </View>
              )}
            </View>
            
            {job.technician && (
              <View style={styles.technicianAvatar}>
                <SafeLinearGradient
                  colors={[colors.primary.main, colors.primary.dark || colors.primary.main]}
                  style={styles.avatarGradient}
                >
                  <Text style={styles.technicianInitial}>
                    {job.technician.user.fullName.charAt(0)}
                  </Text>
                </SafeLinearGradient>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const styles = useMemo(() => StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    gradientBackground: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: screenHeight * 0.4,
      zIndex: -1,
    },
    topBackground: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: (StatusBar.currentHeight || 44) + 100,
      zIndex: 1,
    },
    scrollContainer: {
      flex: 1,
    },
    header: {
      backgroundColor: 'transparent',
      paddingTop: spacing.xl,
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.lg,
      zIndex: 2,
      marginTop: spacing.md,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    profileSection: {
      flex: 1,
    },
    greeting: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    userName: {
      fontSize: typography.fontSize.xxl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    profileAvatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: colors.primary.main,
      justifyContent: 'center',
      alignItems: 'center',
    },
    profileInitial: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    statusIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.xs,
    },
    onlineStatus: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#34C759',
      marginRight: spacing.xs,
    },
    statusText: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      fontWeight: typography.fontWeight.medium,
    },
    avatarGradient: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    notificationBadge: {
      position: 'absolute',
      top: -4,
      right: -4,
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: '#FF3B30',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background.primary,
    },
    badgeText: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    quickActionsContainer: {
      paddingHorizontal: spacing.lg,
      marginBottom: spacing.xl,
    },
    quickActionsGrid: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: spacing.md,
    },
    quickActionCard: {
      flex: 1,
      height: 110,
      marginHorizontal: spacing.xs,
    },
    glassCard: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.2)',
      overflow: 'hidden',
    },
    quickActionContent: {
      flex: 1,
      borderRadius: borderRadius.lg,
      padding: spacing.sm,
      position: 'relative',
      justifyContent: 'center',
      alignItems: 'center',
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    quickActionTitle: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
      textAlign: 'center',
    },
    quickActionSubtitle: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: 14,
    },
    cardGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      opacity: 0.1,
    },
    actionArrow: {
      position: 'absolute',
      bottom: spacing.md,
      right: spacing.md,
      opacity: 0.6,
    },
    statsContainer: {
      paddingHorizontal: spacing.lg,
      marginBottom: spacing.xl,
    },
    statsGrid: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: spacing.md,
    },
    statCard: {
      flex: 1,
      height: 80,
    },
    statGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.md,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.15)',
    },
    statContent: {
      flex: 1,
      borderRadius: borderRadius.md,
      padding: spacing.md,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
    },
    statValue: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      marginBottom: spacing.xs,
    },
    statLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      textAlign: 'center',
    },
    trendContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.xs,
    },
    trendText: {
      fontSize: typography.fontSize.xs,
      marginLeft: spacing.xs,
    },
    statGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.md,
      opacity: 0.3,
    },
    statIcon: {
      width: 24,
      height: 24,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: spacing.xs,
    },
    sectionContainer: {
      paddingHorizontal: spacing.lg,
      marginBottom: spacing.xl,
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    sectionTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    viewAllButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
      backgroundColor: 'rgba(0, 122, 255, 0.1)',
    },
    viewAllText: {
      fontSize: typography.fontSize.sm,
      color: colors.primary.main,
      fontWeight: typography.fontWeight.medium,
    },
    tabContainer: {
      flexDirection: 'row',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: borderRadius.lg,
      padding: spacing.xs,
      marginBottom: spacing.lg,
    },
    tab: {
      flex: 1,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borderRadius.md,
      alignItems: 'center',
    },
    activeTab: {
      backgroundColor: colors.primary.main,
    },
    tabText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.secondary,
    },
    activeTabText: {
      color: colors.text.inverse,
    },
    modernJobCard: {
      marginBottom: spacing.md,
    },
    jobCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    jobCardContent: {
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
      position: 'relative',
      overflow: 'hidden',
    },
    jobCardHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    statusDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: spacing.sm,
    },
    jobTitle: {
      flex: 1,
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
    },
    jobDescription: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      lineHeight: 20,
      marginBottom: spacing.md,
    },
    jobCardFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    jobMeta: {
      flexDirection: 'column',
      alignItems: 'flex-start',
      flex: 1,
    },
    jobDate: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      marginLeft: spacing.xs,
    },
    technicianInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    technicianAvatar: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: colors.primary.main,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.xs,
    },
    technicianInitial: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    technicianName: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      maxWidth: 80,
    },
    emptyState: {
      alignItems: 'center',
      paddingVertical: spacing.xxl,
    },
    emptyIcon: {
      marginBottom: spacing.lg,
    },
    emptyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    emptyDescription: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      textAlign: 'center',
      marginBottom: spacing.lg,
    },
    jobCardGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
    },
    statusContainer: {
      flex: 1,
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
      alignSelf: 'flex-start',
    },
    metaItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.xs,
    },
    statusTextSecondary: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.medium,
      marginLeft: spacing.xs,
    },
    bookServiceButton: {
      backgroundColor: colors.primary.main,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderRadius: borderRadius.md,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: spacing.md,
    },
    bookServiceButtonText: {
      color: colors.text.inverse,
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
    },

  }), [colors, spacing, typography, borderRadius]);

  return (
    <SafeAreaView style={styles.safeArea}>
      <Animated.View 
        style={[
          styles.container,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
          }
        ]}
      >
        <View style={styles.gradientBackground}>
          <SafeLinearGradient
            colors={['#667eea', '#764ba2', '#f093fb']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </View>

        {/* Top Background to prevent content scrolling into status bar area */}
        <View style={styles.topBackground}>
          <SafeLinearGradient
            colors={['#667eea', '#764ba2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </View>
      


      <ScrollView 
        style={styles.scrollContainer}
        contentContainerStyle={{ paddingTop: 0 }}
        showsVerticalScrollIndicator={false}
        bounces={true}
        scrollEventThrottle={16}
        onScroll={handleScroll}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary.main}
            progressViewOffset={60}
          />
        }
      >
        {/* Header */}
        <Animated.View style={[styles.header, { opacity: headerOpacity }]}>
          <View style={styles.headerContent}>
            <View style={styles.profileSection}>
              <Text style={styles.greeting}>{getGreeting()},</Text>
              <Text style={styles.userName}>{firstName} 👋✨</Text>
              <View style={styles.statusIndicator}>
                <View style={styles.onlineStatus} />
                <Text style={styles.statusText}>Online</Text>
              </View>
            </View>
            <TouchableOpacity 
              style={styles.profileAvatar}
              onPress={() => navigation.navigate('Profile')}
            >
              <SafeLinearGradient
                colors={[colors.primary.main, colors.primary.dark || colors.primary.main]}
                style={styles.avatarGradient}
              >
                <Text style={styles.profileInitial}>{firstName.charAt(0)}</Text>
              </SafeLinearGradient>
              {unreadCount > 0 && (
                <View style={styles.notificationBadge}>
                  <Text style={styles.badgeText}>{unreadCount}</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View style={[styles.quickActionsContainer, { opacity: fadeAnim }]}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <QuickActionCard
              icon="add-circle"
              title="Book Service"
              subtitle="Find a technician"
              onPress={() => navigation.navigate('BookJob')}
              color="#007AFF"
              gradient={['#007AFF', '#0051D5']}
            />
            <QuickActionCard
              icon="chatbubbles"
              title="Messages"
              subtitle="Chat with techs"
              onPress={() => navigation.navigate('ChatList')}
              color="#34C759"
              gradient={['#34C759', '#28A745']}
            />
            <QuickActionCard
              icon="card"
              title="Payments"
              subtitle="Manage billing"
              onPress={() => {
                // Only navigate if there are completed jobs to pay for
                const payableJob = completedJobs.find(job => job.status === 'COMPLETED');
                if (payableJob) {
                  navigation.navigate('Payment', { jobId: payableJob.id });
                } else {
                  // Show alert or navigate to jobs list
                  navigation.navigate('MyJobs');
                }
              }}
              color="#FF9500"
              gradient={['#FF9500', '#FF6B00']}
            />
          </View>
        </Animated.View>

        {/* Stats */}
        <Animated.View style={[styles.statsContainer, { opacity: fadeAnim }]}>
          <Text style={[styles.sectionTitle, { marginBottom: spacing.lg }]}>Overview</Text>
          <View style={styles.statsGrid}>
            <StatCard
              value={ongoingJobs.length.toString()}
              label="Active Jobs"
              color={colors.primary.main}
              icon="construct"
            />
            <StatCard
              value={userStats.completedJobs.toString()}
              label="Completed"
              trend={userStats.completedJobs > 0 ? `+${Math.round((userStats.completedJobs / userStats.totalJobs) * 100)}%` : undefined}
              color={colors.semantic.success}
              icon="checkmark-circle"
            />
            <StatCard
              value={userStats.rating > 0 ? userStats.rating.toFixed(1) : "0.0"}
              label="Rating"
              color={colors.semantic.warning}
              icon="star"
            />
          </View>
        </Animated.View>

        {/* Recent Jobs */}
        <Animated.View style={[styles.sectionContainer, { opacity: fadeAnim }]}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Jobs</Text>
            <TouchableOpacity 
              style={styles.viewAllButton}
              onPress={() => navigation.navigate('MyJobs')}
            >
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          {/* Tab Selector */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'ongoing' && styles.activeTab]}
              onPress={() => setActiveTab('ongoing')}
            >
              <Text style={[styles.tabText, activeTab === 'ongoing' && styles.activeTabText]}>
                Ongoing ({ongoingJobs.length})
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'completed' && styles.activeTab]}
              onPress={() => setActiveTab('completed')}
            >
              <Text style={[styles.tabText, activeTab === 'completed' && styles.activeTabText]}>
                Completed ({completedJobs.length})
              </Text>
            </TouchableOpacity>
          </View>

          {/* Jobs List */}
          {(activeTab === 'ongoing' ? ongoingJobs : completedJobs).length > 0 ? (
            (activeTab === 'ongoing' ? ongoingJobs : completedJobs)
              .slice(0, 3)
              .map((job) => (
                <ModernJobCard key={job.id} job={job} />
              ))
          ) : (
            <View style={styles.emptyState}>
              <Ionicons 
                name={activeTab === 'ongoing' ? 'construct-outline' : 'checkmark-circle-outline'} 
                size={64} 
                color={colors.text.secondary} 
                style={styles.emptyIcon}
              />
              <Text style={styles.emptyTitle}>
                {activeTab === 'ongoing' ? 'No Active Jobs' : 'No Completed Jobs'}
              </Text>
              <Text style={styles.emptyDescription}>
                {activeTab === 'ongoing' 
                  ? 'Book your first service to get started'
                  : 'Complete some jobs to see them here'
                }
              </Text>
              {activeTab === 'ongoing' && (
                <TouchableOpacity
                  style={styles.bookServiceButton}
                  onPress={() => navigation.navigate('BookJob')}
                >
                  <Text style={styles.bookServiceButtonText}>Book a Service</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </Animated.View>
      </ScrollView>
      </Animated.View>
    </SafeAreaView>
  );
};

export default ModernHomeScreen;