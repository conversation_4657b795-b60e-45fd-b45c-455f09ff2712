// CTRON Home - Socket.IO Service
// Real-time communication service for chat functionality

import { io, Socket } from 'socket.io-client';

import { SOCKET_URL } from '../config/api.config';
import { API_CONSTANTS } from '../config/constants';
import { getAuthToken } from '../utils/auth.utils';

interface SocketEventMap {
  // Chat events
  'chat:join': (data: { chatId: string }) => void;
  'chat:leave': (data: { chatId: string }) => void;
  'chat:sendMessage': (data: { chatId: string; content: string; attachments?: unknown[] }) => void;
  'chat:typing': (data: { chatId: string; isTyping: boolean }) => void;
  'chat:markRead': (data: { messageId: string }) => void;
  'chat:getOnlineUsers': (data: { chatId: string }) => void;

  // Server responses
  'chat:newMessage': (data: { message: unknown; chatId: string }) => void;
  'chat:userTyping': (data: { userId: string; chatId: string; isTyping: boolean; timestamp: string }) => void;
  'chat:messageRead': (data: { messageId: string; readBy: string; timestamp: string }) => void;
  'chat:onlineUsers': (data: { chatId: string; onlineUsers: string[]; count: number }) => void;
  'chat:userJoined': (data: { userId: string; chatId: string; timestamp: string }) => void;
  'chat:userLeft': (data: { userId: string; chatId: string; timestamp: string }) => void;

  // Job tracking events
  'technicianLocationUpdate': (data: unknown) => void;
  'jobStatusChanged': (data: unknown) => void;
  'jobUpdate': (data: unknown) => void;
  'joinJob': (jobId: string) => void;

  // Connection events
  'connect': () => void;
  'disconnect': (reason: string) => void;
  'error': (error: unknown) => void;
}

class SocketIOService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private eventListeners: Map<string, ((...args: unknown[]) => void)[]> = new Map();

  /**
   * Initialize and connect to Socket.IO server
   */
  async connect(): Promise<void> {
    if (this.socket?.connected) {
      return;
    }

    try {
      const token = await getAuthToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      this.socket = io(SOCKET_URL, {
        auth: {
          token,
        },
        transports: ['polling', 'websocket'], // Try polling first for mobile
        timeout: API_CONSTANTS.SOCKET_TIMEOUT,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: API_CONSTANTS.RECONNECTION_DELAY,
    reconnectionDelayMax: API_CONSTANTS.RECONNECTION_DELAY_MAX,
        forceNew: true,
        upgrade: true,
      });

      this.setupEventListeners();

      return new Promise((resolve, reject) => {
        if (!this.socket) {
          reject(new Error('Socket not initialized'));
          return;
        }

        this.socket.on('connect', () => {
          console.info('🔌 Socket.IO connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          console.error('🔌 Socket.IO connection error:', error);
          this.isConnected = false;
          reject(error);
        });

        this.socket.on('disconnect', (reason) => {
          console.info('🔌 Socket.IO disconnected:', reason);
          this.isConnected = false;

          if (reason === 'io server disconnect') {
            // Server disconnected, try to reconnect
            this.reconnect();
          }
        });
      });
    } catch (error) {
      console.error('🔌 Socket.IO connection failed:', error);
      throw error;
    }
  }

  /**
   * Disconnect from Socket.IO server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.eventListeners.clear();
      console.info('🔌 Socket.IO disconnected manually');
    }
  }

  /**
   * Check if socket is connected
   */
  get connected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  /**
   * Emit an event to the server
   */
  emit<K extends keyof SocketEventMap>(event: K, data: Parameters<SocketEventMap[K]>[0]): void {
    if (!this.connected) {
      console.info(`🔌 Cannot emit ${event}: Socket not connected`);
      return;
    }

    this.socket?.emit(event, data);
  }

  /**
   * Listen for events from the server
   */
  on<K extends keyof SocketEventMap>(event: K, callback: SocketEventMap[K]): void {
    if (!this.eventListeners.has(event as string)) {
      this.eventListeners.set(event as string, []);
    }

    this.eventListeners.get(event as string)?.push(callback as (...args: unknown[]) => void);
    this.socket?.on(event as string, callback as (...args: unknown[]) => void);
  }

  /**
   * Remove event listener
   */
  off<K extends keyof SocketEventMap>(event: K, callback: SocketEventMap[K]): void {
    const listeners = this.eventListeners.get(event as string);
    if (listeners) {
      const index = listeners.indexOf(callback as (...args: unknown[]) => void);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }

    this.socket?.off(event as string, callback as (...args: unknown[]) => void);
  }

  /**
   * Remove all listeners for an event
   */
  removeAllListeners(event?: string): void {
    if (event) {
      this.eventListeners.delete(event);
      this.socket?.removeAllListeners(event);
    } else {
      this.eventListeners.clear();
      this.socket?.removeAllListeners();
    }
  }

  /**
   * Setup default event listeners
   */
  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('error', (error: unknown) => {
      console.error('🔌 Socket.IO error:', error);
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.info(`🔌 Socket.IO reconnected after ${attemptNumber} attempts`);
      this.isConnected = true;
      this.reconnectAttempts = 0;
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('🔌 Socket.IO reconnection error:', error);
      this.reconnectAttempts++;
    });

    this.socket.on('reconnect_failed', () => {
      console.error('🔌 Socket.IO reconnection failed after maximum attempts');
      this.isConnected = false;
    });
  }

  /**
   * Manual reconnection attempt
   */
  private async reconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🔌 Maximum reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    console.info(`🔌 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    try {
      await this.connect();
    } catch (error) {
      console.error('🔌 Reconnection attempt failed:', error);

      // Wait before next attempt
      setTimeout(() => {
        this.reconnect();
      }, Math.min(API_CONSTANTS.RECONNECTION_DELAY * Math.pow(2, this.reconnectAttempts), API_CONSTANTS.RECONNECTION_DELAY_MAX * 6));
    }
  }

  /**
   * Get connection status
   */
  getStatus(): {
    connected: boolean;
    reconnectAttempts: number;
    socketId?: string;
  } {
    return {
      connected: this.connected,
      reconnectAttempts: this.reconnectAttempts,
      socketId: this.socket?.id,
    };
  }
}

// Export singleton instance
export const SocketService = new SocketIOService();
export type { SocketEventMap };
