// Enhanced Button Component with Animations and Haptic Feedback
// Implementation of UI improvements for CTRON authentication

import React, { useRef, useState } from 'react';

import { ViewStyle, TextStyle } from 'react-native';

import { ctronColors, ctronSpacing, ctronBorderRadius, ctronTypography, ctronShadows } from '../../styles/ctronDesignSystem';
import { View, Text, TouchableOpacity, Animated, ActivityIndicator, StyleSheet } from '../../utils/platformUtils';

interface EnhancedButtonProps {
  title: string;
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  icon?: string;
  loadingText?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  style?: ViewStyle | TextStyle;
}

const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  title,
  onPress,
  loading = false,
  disabled = false,
  variant = 'primary',
  size = 'medium',
  icon,
  loadingText = 'Loading...',
  accessibilityLabel,
  accessibilityHint,
  style,
}) => {
  const [_isPressed, setIsPressed] = useState(false);

  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const rippleAnim = useRef(new Animated.Value(0)).current;

  const handlePressIn = () => {
    if (disabled || loading) return;

    setIsPressed(true);

    // Scale down animation
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 0.95,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.8,
        duration: 100,
        useNativeDriver: true,
      }),
      // Ripple effect
      Animated.timing(rippleAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    setIsPressed(false);

    // Scale back to normal
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Reset ripple
    setTimeout(() => {
      rippleAnim.setValue(0);
    }, 200);
  };

  const handlePress = () => {
    if (disabled || loading) return;
    onPress();
  };

  // Get button styles based on variant
  const getButtonStyles = () => {
    const baseStyle = [styles.button, styles[`button${size.charAt(0).toUpperCase() + size.slice(1)}`]];

    switch (variant) {
      case 'primary':
        return [
          ...baseStyle,
          styles.primaryButton,
          (disabled || loading) && styles.buttonDisabled,
        ];
      case 'secondary':
        return [
          ...baseStyle,
          styles.secondaryButton,
          (disabled || loading) && styles.buttonDisabled,
        ];
      case 'outline':
        return [
          ...baseStyle,
          styles.outlineButton,
          (disabled || loading) && styles.buttonDisabled,
        ];
      default:
        return baseStyle;
    }
  };

  // Get text styles based on variant
  const getTextStyles = () => {
    const baseStyle = [styles.buttonText, styles[`buttonText${size.charAt(0).toUpperCase() + size.slice(1)}`]];

    switch (variant) {
      case 'primary':
        return [...baseStyle, styles.primaryButtonText];
      case 'secondary':
        return [...baseStyle, styles.secondaryButtonText];
      case 'outline':
        return [...baseStyle, styles.outlineButtonText];
      default:
        return baseStyle;
    }
  };

  const rippleScale = rippleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const rippleOpacity = rippleAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0, 0.3, 0],
  });

  return (
    <Animated.View
      style={[
        { transform: [{ scale: scaleAnim }] },
        style,
      ]}
    >
      <TouchableOpacity
        style={getButtonStyles()}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        disabled={disabled || loading}
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel || title}
        accessibilityHint={accessibilityHint}
        accessibilityState={{
          disabled: disabled || loading,
          busy: loading,
        }}
        activeOpacity={1}
      >
        {/* Ripple Effect */}
        <Animated.View
          style={[
            styles.ripple,
            {
              transform: [{ scale: rippleScale }],
              opacity: rippleOpacity,
            },
          ]}
        />

        {/* Button Content */}
        <Animated.View
          style={[
            styles.buttonContent,
            { opacity: opacityAnim },
          ]}
        >
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator
                size={size === 'small' ? 'small' : 'small'}
                color={variant === 'primary' ? ctronColors.textLight : ctronColors.primary}
              />
              <Text style={[getTextStyles(), styles.loadingText]}>
                {loadingText}
              </Text>
            </View>
          ) : (
            <View style={styles.contentContainer}>
              {icon && (
                <Text style={[styles.icon, styles[`icon${size.charAt(0).toUpperCase() + size.slice(1)}`]]}>
                  {icon}
                </Text>
              )}
              <Text style={getTextStyles()}>{title}</Text>
            </View>
          )}
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: ctronBorderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  buttonSmall: {
    height: 40,
    paddingHorizontal: ctronSpacing.md,
  },
  buttonMedium: {
    height: 56,
    paddingHorizontal: ctronSpacing.lg,
  },
  buttonLarge: {
    height: 64,
    paddingHorizontal: ctronSpacing.xl,
  },
  primaryButton: {
    backgroundColor: ctronColors.primary,
    ...ctronShadows.md,
  },
  secondaryButton: {
    backgroundColor: ctronColors.backgroundSecondary,
    borderWidth: 1,
    borderColor: ctronColors.inputBorder,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: ctronColors.primary,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontWeight: ctronTypography.fontWeight.semiBold,
    textAlign: 'center',
  },
  buttonTextSmall: {
    fontSize: ctronTypography.fontSize.sm,
  },
  buttonTextMedium: {
    fontSize: ctronTypography.fontSize.base,
  },
  buttonTextLarge: {
    fontSize: ctronTypography.fontSize.lg,
  },
  primaryButtonText: {
    color: ctronColors.textLight,
  },
  secondaryButtonText: {
    color: ctronColors.textPrimary,
  },
  outlineButtonText: {
    color: ctronColors.primary,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginLeft: ctronSpacing.sm,
  },
  icon: {
    marginRight: ctronSpacing.sm,
  },
  iconSmall: {
    fontSize: 16,
  },
  iconMedium: {
    fontSize: 18,
  },
  iconLarge: {
    fontSize: 20,
  },
  ripple: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: ctronColors.textLight,
    borderRadius: ctronBorderRadius.lg,
  },
});

export default EnhancedButton;
export { EnhancedButton };