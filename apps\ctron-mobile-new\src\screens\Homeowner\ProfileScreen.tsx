// CTRON Home - Homeowner Profile Screen
// Allow homeowners to view and update their profile information

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { BlurView } from 'expo-blur';
import React, { useState, useEffect, useRef, useMemo } from 'react';
import Toast from 'react-native-toast-message';

import SafeLinearGradient from '../../components/SafeLinearGradient';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import type { HomeownerStackParamList } from '../../navigation/types';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Animated,
  Dimensions,
  StatusBar,
  SafeAreaView,
  Alert
} from '../../utils/platformUtils';

type ProfileScreenNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'Profile'>;

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ProfileScreen = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const { user, updateProfile, logout } = useAuth();
  const { colors, spacing, typography, borderRadius } = useTheme();
  
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    fullName: user?.fullName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    address: user?.address || '',
  });

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const handleSave = async () => {
    try {
      setLoading(true);
      await updateProfile(formData);
      setEditing(false);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Profile updated successfully',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to update profile',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: () => logout()
        }
      ]
    );
  };

  const ProfileField = ({ label, value, onChangeText, editable = true, keyboardType = 'default' }: {
    label: string;
    value: string;
    onChangeText: (text: string) => void;
    editable?: boolean;
    keyboardType?: 'default' | 'email-address' | 'phone-pad';
  }) => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>{label}</Text>
      <View style={styles.fieldInputContainer}>
        <TextInput
          style={[styles.fieldInput, !editing && styles.fieldInputDisabled]}
          value={value}
          onChangeText={onChangeText}
          editable={editing && editable}
          keyboardType={keyboardType}
          placeholderTextColor={colors.text.secondary}
        />
      </View>
    </View>
  );

  const styles = useMemo(() => StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    gradientBackground: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: screenHeight * 0.3,
      zIndex: -1,
    },
    topBackground: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: (StatusBar.currentHeight || 44) + 100,
      zIndex: 1,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: spacing.xl,
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.lg,
      zIndex: 2,
    },
    headerTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    headerButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    scrollContainer: {
      flex: 1,
      paddingHorizontal: spacing.lg,
    },
    profileHeader: {
      alignItems: 'center',
      marginBottom: spacing.xl,
    },
    avatarContainer: {
      position: 'relative',
      marginBottom: spacing.lg,
    },
    avatar: {
      width: 100,
      height: 100,
      borderRadius: 50,
      justifyContent: 'center',
      alignItems: 'center',
    },
    avatarInitial: {
      fontSize: typography.fontSize.xxl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    editAvatarButton: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.primary.main,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background.primary,
    },
    profileName: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    profileRole: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      textTransform: 'capitalize',
    },
    formContainer: {
      marginBottom: spacing.xl,
    },
    fieldContainer: {
      marginBottom: spacing.lg,
    },
    fieldLabel: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.secondary,
      marginBottom: spacing.sm,
    },
    fieldInputContainer: {
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderRadius: borderRadius.lg,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    fieldInput: {
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
    },
    fieldInputDisabled: {
      opacity: 0.7,
    },
    actionButtons: {
      gap: spacing.md,
      marginBottom: spacing.xl,
    },
    primaryButton: {
      backgroundColor: colors.primary.main,
      paddingVertical: spacing.md,
      borderRadius: borderRadius.lg,
      alignItems: 'center',
      justifyContent: 'center',
    },
    secondaryButton: {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      paddingVertical: spacing.md,
      borderRadius: borderRadius.lg,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.2)',
    },
    dangerButton: {
      backgroundColor: 'rgba(255, 59, 48, 0.1)',
      paddingVertical: spacing.md,
      borderRadius: borderRadius.lg,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: 'rgba(255, 59, 48, 0.3)',
    },
    buttonText: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.inverse,
    },
    secondaryButtonText: {
      color: colors.text.primary,
    },
    dangerButtonText: {
      color: '#FF3B30',
    },
  }), [colors, spacing, typography, borderRadius]);

  return (
    <SafeAreaView style={styles.safeArea}>
      <Animated.View 
        style={[
          styles.container,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        <View style={styles.gradientBackground}>
          <SafeLinearGradient
            colors={['#667eea', '#764ba2', '#f093fb']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </View>

        <View style={styles.topBackground}>
          <SafeLinearGradient
            colors={['#667eea', '#764ba2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </View>

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.headerButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={20} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile</Text>
          <TouchableOpacity 
            style={styles.headerButton}
            onPress={() => setEditing(!editing)}
          >
            <Ionicons name={editing ? "close" : "create"} size={20} color={colors.text.primary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
          {/* Profile Header */}
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <SafeLinearGradient
                colors={[colors.primary.main, colors.primary.dark || colors.primary.main]}
                style={styles.avatar}
              >
                <Text style={styles.avatarInitial}>
                  {formData.fullName.charAt(0) || 'U'}
                </Text>
              </SafeLinearGradient>
              {editing && (
                <TouchableOpacity style={styles.editAvatarButton}>
                  <Ionicons name="camera" size={16} color={colors.text.inverse} />
                </TouchableOpacity>
              )}
            </View>
            <Text style={styles.profileName}>{formData.fullName || 'User'}</Text>
            <Text style={styles.profileRole}>{user?.role?.toLowerCase() || 'homeowner'}</Text>
          </View>

          {/* Form */}
          <View style={styles.formContainer}>
            <ProfileField
              label="Full Name"
              value={formData.fullName}
              onChangeText={(text) => setFormData(prev => ({ ...prev, fullName: text }))}
            />
            <ProfileField
              label="Email"
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              keyboardType="email-address"
              editable={false}
            />
            <ProfileField
              label="Phone"
              value={formData.phone}
              onChangeText={(text) => setFormData(prev => ({ ...prev, phone: text }))}
              keyboardType="phone-pad"
            />
            <ProfileField
              label="Address"
              value={formData.address}
              onChangeText={(text) => setFormData(prev => ({ ...prev, address: text }))}
            />
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            {editing ? (
              <>
                <TouchableOpacity 
                  style={styles.primaryButton}
                  onPress={handleSave}
                  disabled={loading}
                >
                  <Text style={styles.buttonText}>
                    {loading ? 'Saving...' : 'Save Changes'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={styles.secondaryButton}
                  onPress={() => {
                    setEditing(false);
                    setFormData({
                      fullName: user?.fullName || '',
                      email: user?.email || '',
                      phone: user?.phone || '',
                      address: user?.address || '',
                    });
                  }}
                >
                  <Text style={styles.secondaryButtonText}>Cancel</Text>
                </TouchableOpacity>
              </>
            ) : (
              <TouchableOpacity 
                style={styles.dangerButton}
                onPress={handleLogout}
              >
                <Text style={styles.dangerButtonText}>Logout</Text>
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </Animated.View>
    </SafeAreaView>
  );
};

export default ProfileScreen;