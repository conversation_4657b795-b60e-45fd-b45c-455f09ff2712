// backend/src/utils/startupValidator.ts
import { logger } from './logger';
import { env } from '../config/env';
import { testDatabaseConnection } from '../config/db';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validate all critical system components on startup
 */
export async function validateSystemStartup(): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  logger.info('🔍 Running system startup validation...');

  // 1. Environment Variables Validation
  try {
    validateEnvironmentVariables(result);
  } catch (error) {
    result.errors.push(`Environment validation failed: ${error}`);
    result.isValid = false;
  }

  // 2. Database Connection Validation
  try {
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      result.errors.push('Database connection failed');
      result.isValid = false;
    } else {
      logger.info('✅ Database connection validated');
    }
  } catch (error) {
    result.errors.push(`Database validation failed: ${error}`);
    result.isValid = false;
  }

  // 3. External Services Validation
  try {
    await validateExternalServices(result);
  } catch (error) {
    result.warnings.push(`External services validation failed: ${error}`);
  }

  // 4. Security Configuration Validation
  try {
    validateSecurityConfiguration(result);
  } catch (error) {
    result.warnings.push(`Security validation failed: ${error}`);
  }

  // Log results
  if (result.isValid) {
    logger.info('✅ System startup validation passed');
  } else {
    logger.error('❌ System startup validation failed');
    result.errors.forEach(error => logger.error(`  - ${error}`));
  }

  if (result.warnings.length > 0) {
    logger.warn('⚠️ System startup warnings:');
    result.warnings.forEach(warning => logger.warn(`  - ${warning}`));
  }

  return result;
}

/**
 * Validate environment variables
 */
function validateEnvironmentVariables(result: ValidationResult): void {
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'OPENAI_API_KEY',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_BUCKET_NAME'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    result.errors.push(`Missing required environment variables: ${missingVars.join(', ')}`);
    result.isValid = false;
  }

  // Check for test keys in production
  if (env.NODE_ENV === 'production') {
    if (env.STRIPE_SECRET_KEY.includes('test')) {
      result.errors.push('Production environment cannot use Stripe test keys');
      result.isValid = false;
    }

    if (env.JWT_SECRET.length < 64) {
      result.warnings.push('JWT_SECRET should be at least 64 characters in production');
    }
  }

  logger.info('✅ Environment variables validated');
}

/**
 * Validate external services connectivity
 */
async function validateExternalServices(result: ValidationResult): Promise<void> {
  // Test Stripe connectivity
  try {
    const Stripe = require('stripe');
    const stripe = new Stripe(env.STRIPE_SECRET_KEY);
    await stripe.balance.retrieve();
    logger.info('✅ Stripe connection validated');
  } catch (error) {
    result.warnings.push('Stripe connection failed - payment features may not work');
  }

  // Test OpenAI connectivity
  try {
    const OpenAI = require('openai');
    const openai = new OpenAI({ apiKey: env.OPENAI_API_KEY });
    await openai.models.list();
    logger.info('✅ OpenAI connection validated');
  } catch (error) {
    result.warnings.push('OpenAI connection failed - AI features may not work');
  }

  // Test AWS S3 connectivity
  try {
    const { S3Client, HeadBucketCommand } = require('@aws-sdk/client-s3');
    const s3Client = new S3Client({
      region: env.AWS_REGION,
      credentials: {
        accessKeyId: env.AWS_ACCESS_KEY_ID,
        secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
      },
    });
    
    await s3Client.send(new HeadBucketCommand({ Bucket: env.AWS_BUCKET_NAME }));
    logger.info('✅ AWS S3 connection validated');
  } catch (error) {
    result.warnings.push('AWS S3 connection failed - file upload features may not work');
  }
}

/**
 * Validate security configuration
 */
function validateSecurityConfiguration(result: ValidationResult): void {
  // Check JWT secret strength
  if (env.JWT_SECRET.length < 32) {
    result.errors.push('JWT_SECRET must be at least 32 characters');
    result.isValid = false;
  }

  if (env.JWT_REFRESH_SECRET.length < 32) {
    result.errors.push('JWT_REFRESH_SECRET must be at least 32 characters');
    result.isValid = false;
  }

  // Check CORS configuration
  if (env.NODE_ENV === 'production' && env.CORS_ORIGIN === '*') {
    result.warnings.push('CORS is set to allow all origins in production - consider restricting');
  }

  logger.info('✅ Security configuration validated');
}

/**
 * Display validation results in a formatted way
 */
export function displayValidationResults(result: ValidationResult): void {
  if (!result.isValid) {
    logger.error('🚨 CRITICAL STARTUP ERRORS:');
    result.errors.forEach(error => logger.error(`  ❌ ${error}`));
    logger.error('🛑 Server cannot start with these errors. Please fix them and try again.');
  }

  if (result.warnings.length > 0) {
    logger.warn('⚠️  STARTUP WARNINGS:');
    result.warnings.forEach(warning => logger.warn(`  ⚠️  ${warning}`));
    logger.warn('💡 These warnings won\'t prevent startup but may affect functionality.');
  }

  if (result.isValid && result.warnings.length === 0) {
    logger.info('🎉 All startup validations passed successfully!');
  }
}