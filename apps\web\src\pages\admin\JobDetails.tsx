// apps/web/src/pages/admin/JobDetails.tsx

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, MapPin, Clock, User, Phone, Mail } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { LoadingState } from '../../components/ui/LoadingState';
import { ErrorState } from '../../components/ui/ErrorState';
import { PageContainer } from '../../components/ui/PageContainer';

interface JobDetail {
  id: string;
  issue: string;
  description: string;
  status: string;
  createdAt: string;
  scheduledAt: string;
  completedAt?: string;
  address: string;
  customer: {
    name: string;
    phone: string;
    email: string;
  };
  technician?: {
    name: string;
    phone: string;
    rating: number;
  };
  payment: {
    amount: number;
    status: string;
    isReleased: boolean;
    isFrozen: boolean;
  };
}

const JobDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [job, setJob] = useState<JobDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch job details from API
    const fetchJobDetails = async () => {
      if (!id) {
        setLoading(false);
        return;
      }

      try {
        const response = await fetch(`/api/admin/jobs/${id}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          const jobData: JobDetail = {
            id: data.job.id,
            issue: data.job.issue,
            description: data.job.description || '',
            status: data.job.status,
            createdAt: data.job.createdAt,
            scheduledAt: data.job.scheduledAt,
            completedAt: data.job.completedAt,
            address: data.job.address || 'Address not specified',
            customer: {
              name: data.job.homeowner.fullName,
              phone: data.job.homeowner.phone || 'Not provided',
              email: data.job.homeowner.email,
            },
            technician: data.job.technician ? {
              name: data.job.technician.user.fullName,
              phone: data.job.technician.user.phone || 'Not provided',
              rating: data.job.technician.rating || 0,
            } : undefined,
            payment: data.job.payment ? {
              amount: data.job.payment.amount,
              status: data.job.payment.status,
              isReleased: data.job.payment.isReleased,
              isFrozen: data.job.payment.isFrozen,
            } : {
              amount: 0,
              status: 'PENDING',
              isReleased: false,
              isFrozen: false,
            },
          };
          setJob(jobData);
        } else {
          console.error('Failed to fetch job details');
        }
      } catch (error) {
        console.error('Error fetching job details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchJobDetails();
  }, [id]);

  if (loading) {
    return (
      <PageContainer>
        <LoadingState message="Loading job details..." />
      </PageContainer>
    );
  }

  if (!job) {
    return (
      <PageContainer>
        <ErrorState
          title="Job not found"
          message="The requested job could not be found or you don't have access to it."
          actionTitle="Back to Jobs"
          onAction={() => navigate('/admin/jobs')}
        />
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4">
        <Button
          variant="outline"
          onClick={() => navigate('/jobs')}
          className="self-start"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Jobs
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Job Details</h1>
          <p className="text-gray-600 mt-1">Job #{job.id}</p>
        </div>
        <Badge className="self-start sm:self-auto">{job.status}</Badge>
      </div>

      {/* Job Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Main Details */}
        <div className="bg-white rounded-2xl p-4 md:p-6 shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Job Information</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-1">{job.issue}</h3>
              <p className="text-gray-600 text-sm">{job.description}</p>
            </div>
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <MapPin className="w-4 h-4" />
              <span>{job.address}</span>
            </div>
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="w-4 h-4" />
              <span>Scheduled: {new Date(job.scheduledAt).toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Customer Details */}
        <div className="bg-white rounded-2xl p-4 md:p-6 shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Details</h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-gray-900">{job.customer.name}</span>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-4 h-4 text-gray-500" />
              <a href={`tel:${job.customer.phone}`} className="text-blue-600 hover:underline">
                {job.customer.phone}
              </a>
            </div>
            <div className="flex items-center gap-3">
              <Mail className="w-4 h-4 text-gray-500" />
              <a href={`mailto:${job.customer.email}`} className="text-blue-600 hover:underline">
                {job.customer.email}
              </a>
            </div>
          </div>
        </div>

        {/* Technician Details */}
        {job.technician && (
          <div className="bg-white rounded-2xl p-4 md:p-6 shadow-sm border border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Assigned Technician</h2>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <User className="w-4 h-4 text-gray-500" />
                <span className="text-gray-900">{job.technician.name}</span>
                <Badge variant="secondary" className="text-xs">
                  ⭐ {job.technician.rating}
                </Badge>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-gray-500" />
                <a href={`tel:${job.technician.phone}`} className="text-blue-600 hover:underline">
                  {job.technician.phone}
                </a>
              </div>
            </div>
          </div>
        )}

        {/* Payment Information */}
        <div className="bg-white rounded-2xl p-4 md:p-6 shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h2>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Amount:</span>
              <span className="font-semibold text-gray-900">£{job.payment.amount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Status:</span>
              <Badge 
                className={`text-xs ${
                  job.payment.isReleased ? 'bg-green-100 text-green-700' :
                  job.payment.isFrozen ? 'bg-red-100 text-red-700' :
                  'bg-yellow-100 text-yellow-700'
                }`}
              >
                {job.payment.isReleased ? 'Released' : 
                 job.payment.isFrozen ? 'Frozen' : 'Pending'}
              </Badge>
            </div>
          </div>
        </div>
      </div>
      </div>
    </PageContainer>
  );
};

export default JobDetails;
