// apps/mobile/src/components/Chat/TypingIndicator.tsx

import React, { useEffect, useRef } from 'react';

import { designSystem } from '../../styles/designSystem';
import { View, Text, StyleSheet } from '../../utils/platformUtils';

// Simple animation replacement for Animated - removed unused function

const { colors, spacing, typography, borderRadius } = designSystem;

interface TypingIndicatorProps {
  users: string[];
  maxUsersToShow?: number;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  users,
  maxUsersToShow = 3,
}) => {
  // Simple animation values for web compatibility
  const fadeAnim = useRef({ _value: 0 }).current;
  const dot1Anim = useRef({ _value: 0 }).current;
  const dot2Anim = useRef({ _value: 0 }).current;
  const dot3Anim = useRef({ _value: 0 }).current;

  useEffect(() => {
    if (users.length > 0) {
      // Simple fade in for web
      fadeAnim._value = 1;

      // Simple dot animation for web
      const animateDots = () => {
        // No complex animation for web compatibility
        dot1Anim._value = 1;
        dot2Anim._value = 1;
        dot3Anim._value = 1;
      };

      animateDots();
    } else {
      // Simple fade out for web
      fadeAnim._value = 0;
    }

    // No cleanup needed for simple web animations
  }, [users.length, dot1Anim, dot2Anim, dot3Anim, fadeAnim]);

  if (users.length === 0) {
    return null;
  }

  const getTypingText = () => {
    const displayUsers = users.slice(0, maxUsersToShow);
    const remainingCount = users.length - maxUsersToShow;

    if (users.length === 1) {
      return `${displayUsers[0]} is typing`;
    } else if (users.length === 2) {
      return `${displayUsers[0]} and ${displayUsers[1]} are typing`;
    } else if (users.length <= maxUsersToShow) {
      const lastUser = displayUsers.pop();
      return `${displayUsers.join(', ')} and ${lastUser} are typing`;
    } else {
      return `${displayUsers.join(', ')} and ${remainingCount} other${remainingCount > 1 ? 's' : ''
        } are typing`;
    }
  };

  const AnimatedDot: React.FC<{ animValue: { _value: number } }> = ({ animValue }) => (
    <View
      style={[
        styles.dot,
        {
          opacity: animValue._value || 1,
        },
      ]}
    />
  );

  return (
    <View style={[styles.container, { opacity: fadeAnim._value || 1 }]}>
      <View style={styles.bubble}>
        <View style={styles.content}>
          <Text style={styles.typingText}>{getTypingText()}</Text>
          <View style={styles.dotsContainer}>
            <AnimatedDot animValue={dot1Anim} />
            <AnimatedDot animValue={dot2Anim} />
            <AnimatedDot animValue={dot3Anim} />
          </View>
        </View>
      </View>

      {/* Bubble tail */}
      <View style={styles.tail} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignSelf: 'flex-start',
    marginHorizontal: spacing.md,
    marginVertical: spacing.xs,
    maxWidth: '80%',
  },
  bubble: {
    backgroundColor: colors.secondarySystemBackground,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginLeft: spacing.sm,
    borderWidth: 1,
    borderColor: colors.separator,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  typingText: {
    ...typography.caption1,
    color: colors.secondaryLabel,
    fontStyle: 'italic',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: colors.secondaryLabel,
  },
  tail: {
    position: 'absolute',
    left: -spacing.xs,
    bottom: spacing.sm,
    width: 0,
    height: 0,
    borderStyle: 'solid',
    borderLeftWidth: 0,
    borderRightWidth: spacing.sm,
    borderTopWidth: spacing.sm,
    borderBottomWidth: 0,
    borderLeftColor: 'transparent',
    borderRightColor: colors.secondarySystemBackground,
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
  },
});

