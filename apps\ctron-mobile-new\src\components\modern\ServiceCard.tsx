// CTRON Home - Modern Service Card Component
// Professional service card using NativeBase for enhanced UI

import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Avatar,
  Badge,
  Button,
  Icon,
  Pressable,
  useTheme,
} from 'native-base';
import { MaterialIcons } from '@expo/vector-icons';

export interface ServiceCardProps {
  service: {
    id: string;
    title: string;
    description: string;
    category: string;
    price: number;
    status: 'available' | 'busy' | 'offline';
    rating: number;
    reviewCount: number;
    estimatedTime: string;
    technician: {
      id: string;
      name: string;
      avatar?: string;
      specialization: string;
      isVerified: boolean;
    };
  };
  onPress?: () => void;
  onBookPress?: () => void;
  variant?: 'default' | 'compact' | 'featured';
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onPress,
  onBookPress,
  variant = 'default',
}) => {
  const theme = useTheme();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'success';
      case 'busy':
        return 'warning';
      case 'offline':
        return 'gray';
      default:
        return 'gray';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'busy':
        return 'Busy';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  if (variant === 'compact') {
    return (
      <Pressable onPress={onPress}>
        <Box
          bg="white"
          rounded="lg"
          shadow={2}
          p={3}
          mb={2}
          borderWidth={1}
          borderColor="gray.100"
          _pressed={{
            bg: 'gray.50',
            shadow: 3,
          }}
        >
          <HStack space={3} alignItems="center">
            <Avatar
              size="sm"
              source={service.technician.avatar ? { uri: service.technician.avatar } : undefined}
              bg="primary.500"
            >
              {service.technician.name.charAt(0)}
            </Avatar>
            <VStack flex={1} space={1}>
              <Text fontSize="md" fontWeight="semibold" numberOfLines={1}>
                {service.title}
              </Text>
              <Text fontSize="sm" color="gray.500" numberOfLines={1}>
                {service.technician.name} • {service.category}
              </Text>
            </VStack>
            <VStack alignItems="flex-end" space={1}>
              <Text fontSize="lg" fontWeight="bold" color="primary.600">
                £{service.price}
              </Text>
              <Badge colorScheme={getStatusColor(service.status)} variant="solid" size="sm">
                {getStatusText(service.status)}
              </Badge>
            </VStack>
          </HStack>
        </Box>
      </Pressable>
    );
  }

  if (variant === 'featured') {
    return (
      <Pressable onPress={onPress}>
        <Box
          bg="white"
          rounded="2xl"
          shadow={4}
          p={5}
          mb={4}
          borderWidth={2}
          borderColor="primary.100"
          _pressed={{
            bg: 'primary.50',
            shadow: 5,
            borderColor: 'primary.200',
          }}
        >
          <VStack space={4}>
            {/* Header */}
            <HStack justifyContent="space-between" alignItems="flex-start">
              <HStack space={3} alignItems="center" flex={1}>
                <Avatar
                  size="lg"
                  source={service.technician.avatar ? { uri: service.technician.avatar } : undefined}
                  bg="primary.500"
                >
                  {service.technician.name.charAt(0)}
                </Avatar>
                <VStack flex={1}>
                  <HStack alignItems="center" space={2}>
                    <Text fontSize="lg" fontWeight="bold" numberOfLines={1}>
                      {service.technician.name}
                    </Text>
                    {service.technician.isVerified && (
                      <Icon as={MaterialIcons} name="verified" color="primary.500" size="sm" />
                    )}
                  </HStack>
                  <Text fontSize="sm" color="gray.600" numberOfLines={1}>
                    {service.technician.specialization}
                  </Text>
                  <HStack space={2} alignItems="center" mt={1}>
                    <Icon as={MaterialIcons} name="star" color="yellow.400" size="sm" />
                    <Text fontSize="sm" color="gray.600">
                      {service.rating} ({service.reviewCount} reviews)
                    </Text>
                  </HStack>
                </VStack>
              </HStack>
              <Badge colorScheme={getStatusColor(service.status)} variant="solid">
                {getStatusText(service.status)}
              </Badge>
            </HStack>

            {/* Service Details */}
            <VStack space={2}>
              <Text fontSize="xl" fontWeight="semibold" color="gray.800">
                {service.title}
              </Text>
              <Text fontSize="md" color="gray.600" numberOfLines={2}>
                {service.description}
              </Text>
            </VStack>

            {/* Service Info */}
            <HStack justifyContent="space-between" alignItems="center">
              <VStack>
                <Text fontSize="sm" color="gray.500">
                  Estimated Time
                </Text>
                <Text fontSize="md" fontWeight="semibold" color="gray.700">
                  {service.estimatedTime}
                </Text>
              </VStack>
              <VStack alignItems="flex-end">
                <Text fontSize="sm" color="gray.500">
                  Starting from
                </Text>
                <Text fontSize="2xl" fontWeight="bold" color="primary.600">
                  £{service.price}
                </Text>
              </VStack>
            </HStack>

            {/* Action Button */}
            <Button
              colorScheme="primary"
              size="lg"
              onPress={onBookPress}
              isDisabled={service.status !== 'available'}
              _text={{
                fontWeight: 'bold',
              }}
            >
              {service.status === 'available' ? 'Book Service' : 'Currently Unavailable'}
            </Button>
          </VStack>
        </Box>
      </Pressable>
    );
  }

  // Default variant
  return (
    <Pressable onPress={onPress}>
      <Box
        bg="white"
        rounded="xl"
        shadow={2}
        p={4}
        mb={3}
        borderWidth={1}
        borderColor="gray.100"
        _pressed={{
          bg: 'gray.50',
          shadow: 3,
        }}
      >
        <VStack space={3}>
          {/* Header */}
          <HStack justifyContent="space-between" alignItems="center">
            <HStack space={3} alignItems="center" flex={1}>
              <Avatar
                size="md"
                source={service.technician.avatar ? { uri: service.technician.avatar } : undefined}
                bg="primary.500"
              >
                {service.technician.name.charAt(0)}
              </Avatar>
              <VStack flex={1}>
                <HStack alignItems="center" space={2}>
                  <Text fontSize="md" fontWeight="semibold" numberOfLines={1}>
                    {service.technician.name}
                  </Text>
                  {service.technician.isVerified && (
                    <Icon as={MaterialIcons} name="verified" color="primary.500" size="xs" />
                  )}
                </HStack>
                <Text fontSize="sm" color="gray.500" numberOfLines={1}>
                  {service.category}
                </Text>
              </VStack>
            </HStack>
            <Badge colorScheme={getStatusColor(service.status)} variant="solid">
              {getStatusText(service.status)}
            </Badge>
          </HStack>

          {/* Service Title */}
          <Text fontSize="lg" fontWeight="semibold" color="gray.800" numberOfLines={1}>
            {service.title}
          </Text>

          {/* Description */}
          <Text fontSize="md" color="gray.600" numberOfLines={2}>
            {service.description}
          </Text>

          {/* Footer */}
          <HStack justifyContent="space-between" alignItems="center">
            <HStack space={4} alignItems="center">
              <HStack space={1} alignItems="center">
                <Icon as={MaterialIcons} name="star" color="yellow.400" size="sm" />
                <Text fontSize="sm" color="gray.600">
                  {service.rating}
                </Text>
                <Text fontSize="sm" color="gray.400">
                  ({service.reviewCount})
                </Text>
              </HStack>
              <HStack space={1} alignItems="center">
                <Icon as={MaterialIcons} name="schedule" color="gray.400" size="sm" />
                <Text fontSize="sm" color="gray.600">
                  {service.estimatedTime}
                </Text>
              </HStack>
            </HStack>
            <Text fontSize="lg" fontWeight="bold" color="primary.600">
              £{service.price}
            </Text>
          </HStack>

          {/* Book Button */}
          <Button
            colorScheme="primary"
            size="md"
            onPress={onBookPress}
            isDisabled={service.status !== 'available'}
            variant={service.status === 'available' ? 'solid' : 'outline'}
          >
            {service.status === 'available' ? 'Book Service' : 'Unavailable'}
          </Button>
        </VStack>
      </Box>
    </Pressable>
  );
};

export default ServiceCard;