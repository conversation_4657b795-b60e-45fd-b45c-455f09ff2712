// OS polyfill for React Native
// This provides a minimal os implementation for Node.js modules

const os = {
  EOL: '\n',
  
  arch: () => 'arm64', // Default for mobile
  
  platform: () => {
    // Try to detect platform from React Native
    try {
      const { Platform } = require('react-native');
      return Platform.OS;
    } catch (e) {
      return 'mobile';
    }
  },
  
  release: () => '1.0.0',
  
  type: () => {
    try {
      const { Platform } = require('react-native');
      return Platform.OS === 'ios' ? 'Darwin' : 'Linux';
    } catch (e) {
      return 'Mobile';
    }
  },
  
  homedir: () => '/home/<USER>',
  tmpdir: () => '/tmp',
  
  hostname: () => 'mobile-device',
  
  cpus: () => [
    {
      model: 'Mobile CPU',
      speed: 2000,
      times: {
        user: 0,
        nice: 0,
        sys: 0,
        idle: 0,
        irq: 0
      }
    }
  ],
  
  totalmem: () => 4 * 1024 * 1024 * 1024, // 4GB
  freemem: () => 2 * 1024 * 1024 * 1024,  // 2GB
  
  loadavg: () => [0.1, 0.1, 0.1],
  
  uptime: () => Date.now() / 1000,
  
  networkInterfaces: () => ({}),
  
  userInfo: () => ({
    uid: 1000,
    gid: 1000,
    username: 'mobile',
    homedir: '/home/<USER>',
    shell: '/bin/sh'
  })
};

module.exports = os;