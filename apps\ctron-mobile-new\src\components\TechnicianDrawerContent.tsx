// src/components/TechnicianDrawerContent.tsx

import { DrawerContentScrollView, DrawerItemList } from '@react-navigation/drawer';

import { useAuth } from '../context/AuthContext';
import { useJobs } from '../context/JobContext';
import { useTheme } from '../context/ThemeContext';
import { View, Text, StyleSheet, TouchableOpacity, Image } from '../utils/platformUtils';

export default function TechnicianDrawerContent(props: any) {
  const { colors, typography, spacing, borderRadius } = useTheme();
  const styles = getStyles(colors, typography, spacing, borderRadius);
  const { user, logout } = useAuth();
  const { assignedJobs } = useJobs();

  const now = new Date();
  const thisMonthCompleted = assignedJobs.filter(
    (job) => 
      job.status === 'COMPLETED' &&
      new Date(job.scheduledAt).getMonth() === now.getMonth() &&
      new Date(job.scheduledAt).getFullYear() === now.getFullYear()
  );

  const monthlyEarnings = thisMonthCompleted.length * 50; // Assume £50/job

  return (
    <DrawerContentScrollView {...props}>
      <View style={styles.header}>
        <Image
          source={{ uri: 'https://i.pravatar.cc/100?u=' + user?.userId }}
          style={styles.avatar}
        />
        <Text style={styles.name}>{user?.fullName}</Text>
        <Text style={styles.verification}>✅ Verified Technician</Text>

        <View style={styles.balanceBox}>
          <Text style={styles.balanceLabel}>Earnings (This Month)</Text>
          <Text style={styles.balanceValue}>£{monthlyEarnings}</Text>
        </View>
      </View>

      {/* Drawer Links */}
      <DrawerItemList {...props} />

      {/* Footer */}
      <TouchableOpacity style={styles.logoutButton} onPress={() => logout()}>
        <Text style={styles.logoutText}>Log Out</Text>
      </TouchableOpacity>
    </DrawerContentScrollView>
  );
}

const getStyles = (colors: any, typography: any, spacing: any, borderRadius: any) => StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
    borderBottomWidth: 1,
    borderColor: colors.border.secondary,
    marginBottom: spacing.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: borderRadius.full,
    backgroundColor: colors.background.secondary,
    marginBottom: spacing.md,
  },
  name: {
    ...typography.h3,
    color: colors.text.primary,
  },
  verification: {
    ...typography.caption,
    color: colors.success.main,
    marginTop: spacing.xs,
  },
  balanceBox: {
    marginTop: spacing.md,
    alignItems: 'center',
  },
  balanceLabel: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  balanceValue: {
    ...typography.h2,
    color: colors.primary.main,
  },
  logoutButton: {
    marginTop: spacing.lg,
    marginHorizontal: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.error.main,
    borderRadius: borderRadius.md,
    alignItems: 'center',
  },
  logoutText: {
    ...typography.button,
    color: colors.white,
  },
});

