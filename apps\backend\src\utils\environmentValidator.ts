// src/utils/environmentValidator.ts

import { logger } from './logger';

interface EnvironmentConfig {
  NODE_ENV: string;
  PORT: string;
  HOST: string;
  DATABASE_URL: string;
  JWT_SECRET: string;
  JWT_REFRESH_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  AWS_ACCESS_KEY_ID?: string;
  AWS_SECRET_ACCESS_KEY?: string;
  AWS_REGION?: string;
  AWS_S3_BUCKET?: string;
  OPENAI_API_KEY?: string;
  SMTP_HOST?: string;
  SMTP_PORT?: string;
  SMTP_USER?: string;
  SMTP_PASS?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validates that all required environment variables are present and properly formatted
 */
export const validateEnvironmentConfig = (): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const config: EnvironmentConfig = {
    NODE_ENV: process.env.NODE_ENV || '',
    PORT: process.env.PORT || '',
    HOST: process.env.HOST || '',
    DATABASE_URL: process.env.DATABASE_URL || '',
    JWT_SECRET: process.env.JWT_SECRET || '',
    JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET || '',
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY || '',
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET || '',
    AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
    AWS_REGION: process.env.AWS_REGION,
    AWS_S3_BUCKET: process.env.AWS_S3_BUCKET,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    SMTP_HOST: process.env.SMTP_HOST,
    SMTP_PORT: process.env.SMTP_PORT,
    SMTP_USER: process.env.SMTP_USER,
    SMTP_PASS: process.env.SMTP_PASS,
  };

  // Required environment variables
  const requiredVars = [
    'NODE_ENV',
    'PORT',
    'HOST',
    'DATABASE_URL',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
  ];

  // Check for missing required variables
  requiredVars.forEach(varName => {
    const value = config[varName as keyof EnvironmentConfig];
    if (!value || value.trim() === '') {
      errors.push(`Missing required environment variable: ${varName}`);
    }
  });

  // Validate NODE_ENV
  const validNodeEnvs = ['development', 'staging', 'production'];
  if (config.NODE_ENV && !validNodeEnvs.includes(config.NODE_ENV)) {
    errors.push(`Invalid NODE_ENV: ${config.NODE_ENV}. Must be one of: ${validNodeEnvs.join(', ')}`);
  }

  // Validate PORT
  if (config.PORT) {
    const port = parseInt(config.PORT, 10);
    if (isNaN(port) || port < 1 || port > 65535) {
      errors.push(`Invalid PORT: ${config.PORT}. Must be a number between 1 and 65535`);
    }
  }

  // Validate DATABASE_URL format
  if (config.DATABASE_URL && !config.DATABASE_URL.startsWith('postgresql://')) {
    errors.push('DATABASE_URL must be a valid PostgreSQL connection string starting with postgresql://');
  }

  // Validate JWT secrets length
  if (config.JWT_SECRET && config.JWT_SECRET.length < 32) {
    errors.push('JWT_SECRET must be at least 32 characters long for security');
  }

  if (config.JWT_REFRESH_SECRET && config.JWT_REFRESH_SECRET.length < 32) {
    errors.push('JWT_REFRESH_SECRET must be at least 32 characters long for security');
  }

  // Validate Stripe keys format
  if (config.STRIPE_SECRET_KEY && !config.STRIPE_SECRET_KEY.startsWith('sk_')) {
    errors.push('STRIPE_SECRET_KEY must start with "sk_"');
  }

  if (config.STRIPE_WEBHOOK_SECRET && !config.STRIPE_WEBHOOK_SECRET.startsWith('whsec_')) {
    errors.push('STRIPE_WEBHOOK_SECRET must start with "whsec_"');
  }

  // Check for development-specific issues
  if (config.NODE_ENV === 'development') {
    if (config.JWT_SECRET === 'your-super-secret-jwt-key' || config.JWT_SECRET.length < 32) {
      warnings.push('JWT_SECRET should be a secure random string of at least 32 characters');
    }
    
    if (config.STRIPE_SECRET_KEY && config.STRIPE_SECRET_KEY.includes('test')) {
      warnings.push('Using Stripe test keys in development - ensure production keys are used in production');
    }
  }

  // Check for production-specific requirements
  if (config.NODE_ENV === 'production') {
    if (config.HOST === 'localhost' || config.HOST === '127.0.0.1') {
      warnings.push('HOST is set to localhost in production - this may cause connectivity issues');
    }

    if (!config.AWS_ACCESS_KEY_ID || !config.AWS_SECRET_ACCESS_KEY) {
      warnings.push('AWS credentials not configured - file upload functionality may not work');
    }

    if (!config.SMTP_HOST || !config.SMTP_USER) {
      warnings.push('SMTP configuration not complete - email functionality may not work');
    }
  }

  // Check for optional but recommended variables
  if (!config.OPENAI_API_KEY) {
    warnings.push('OPENAI_API_KEY not configured - AI assistant features will not work');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Displays environment validation results
 */
export const displayValidationResults = (result: ValidationResult): void => {
  if (!result.isValid) {
    logger.error('🚨 Environment Configuration Errors:');
    result.errors.forEach(error => logger.error(`  - ${error}`));
    
    if (process.env.NODE_ENV === 'production') {
      logger.error('❌ Application cannot start with configuration errors in production');
      process.exit(1);
    }
  }

  if (result.warnings.length > 0) {
    logger.warn('⚠️ Environment Configuration Warnings:');
    result.warnings.forEach(warning => logger.warn(`  - ${warning}`));
  }

  if (result.isValid && result.warnings.length === 0) {
    logger.info('✅ Environment configuration validation passed');
  }
};

/**
 * Performs environment validation and displays results
 * Should be called during application startup
 */
export const validateAndDisplayEnvironment = (): boolean => {
  const result = validateEnvironmentConfig();
  
  logger.info('🔍 Validating environment configuration...');
  
  displayValidationResults(result);
  
  return result.isValid;
};

/**
 * Gets environment configuration with fallbacks and type safety
 */
export const getEnvironmentConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  const config = {
    nodeEnv: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || (isDevelopment ? '3001' : '8080'), 10),
    host: process.env.HOST || (isDevelopment ? 'localhost' : '0.0.0.0'),
    databaseUrl: process.env.DATABASE_URL || '',
    jwtSecret: process.env.JWT_SECRET || '',
    jwtRefreshSecret: process.env.JWT_REFRESH_SECRET || '',
    stripeSecretKey: process.env.STRIPE_SECRET_KEY || '',
    stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
    aws: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      region: process.env.AWS_REGION || 'eu-west-2',
      s3Bucket: process.env.AWS_S3_BUCKET || '',
    },
    openaiApiKey: process.env.OPENAI_API_KEY || '',
    smtp: {
      host: process.env.SMTP_HOST || '',
      port: parseInt(process.env.SMTP_PORT || '587', 10),
      user: process.env.SMTP_USER || '',
      pass: process.env.SMTP_PASS || '',
    },
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
    isStaging: process.env.NODE_ENV === 'staging',
  };

  return config;
};

export default {
  validateEnvironmentConfig,
  validateAndDisplayEnvironment,
  displayValidationResults,
  getEnvironmentConfig,
};
