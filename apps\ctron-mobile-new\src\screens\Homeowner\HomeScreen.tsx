// CTRON Home - Enhanced Homeowner Home Screen
// Main dashboard for homeowners with design system integration

import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';



import { Button, Card, Header, JobCard, Screen, LoadingState, EmptyState } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { useJobs } from '../../context/JobContext';
import { useTheme } from '../../context/ThemeContext';
import { designSystem } from '../../styles/designSystem';
import type { Job } from '../../types/job';
import type { HomeownerStackParamList } from '../../types/navigation';
import {
  View,
  Text,
  StyleSheet,
  FlatList
} from '../../utils/platformUtils';
// ... existing code ...

type HomeScreenNavigationProp = StackNavigationProp<HomeownerStackParamList, 'Home'>;

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { user, logout } = useAuth();
  const { myJobs = [], refreshJobs, loading } = useJobs();
  const [refreshing, setRefreshing] = useState(false);
  const isMountedRef = useRef(true);
  const { colors, spacing, typography, borderRadius, shadows } = useTheme();

  const jobs: Job[] = myJobs;
  const firstName = user?.fullName?.split(' ')[0] || 'Homeowner';

  const ongoingJobs = jobs.filter(
    job => job.status === 'PENDING' || job.status === 'IN_PROGRESS'
  );
  const completedJobs = jobs.filter(job => job.status === 'COMPLETED');

  const lastCompleted = completedJobs.sort((a, b) =>
    new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  )[0];

  useEffect(() => {
    refreshJobs();

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleRefresh = useCallback(async () => {
    if (!isMountedRef.current) return;

    setRefreshing(true);
    await refreshJobs();

    if (isMountedRef.current) {
      setRefreshing(false);
    }
  }, [refreshJobs]);

  const mapJobStatus = (status: string) => {
    const statusMap: { [key: string]: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled' } = {
      'PENDING': 'pending',
      'ASSIGNED': 'assigned',
      'IN_PROGRESS': 'active',
      'COMPLETED': 'completed',
      'CANCELLED': 'cancelled',
    };
    return statusMap[status] || 'pending';
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      {/* Welcome Section */}
      <View style={styles.welcomeSection}>
        <Text style={styles.welcomeText}>Good morning,</Text>
        <Text style={styles.nameText}>{firstName} 👋</Text>
      </View>

      {/* Quick Action Button */}
      <Button
        title="+ Book a Technician"
        onPress={() => navigation.navigate('BookJob')}
        variant="primary"
        size="lg"
        fullWidth
        style={styles.bookButton}
      />

      {/* Summary Cards */}
      <View style={styles.summaryContainer}>
        <Card style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Total Jobs</Text>
          <Text style={styles.summaryValue}>{jobs.length}</Text>
        </Card>

        <Card style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Ongoing</Text>
          <Text style={styles.summaryValue}>{ongoingJobs.length}</Text>
        </Card>

        <Card style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Last Done</Text>
          <Text style={styles.summaryValue}>
            {lastCompleted
              ? new Date(lastCompleted.updatedAt).toLocaleDateString()
              : 'N/A'}
          </Text>
        </Card>
      </View>

      {/* Section Title */}
      <Text style={styles.sectionTitle}>Recent Jobs</Text>
    </View>
  );

  const renderJobItem = useCallback(({ item }: { item: Job }) => {
    const jobData = {
      id: item.id,
      title: `Job #${item.id.slice(0, 8)}`,
      description: item.issue || 'Service request',
      status: mapJobStatus(item.status),
      scheduledAt: item.scheduledAt,
      location: item.address || 'Location not specified',
      price: undefined,
    };

    return (
      <JobCard
        job={jobData}
        onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
        showTechnician={false}
        showPrice={false}
      />
    );
  }, [navigation]);

  const renderEmptyComponent = useCallback(() => (
    <EmptyState
      title="No jobs yet"
      message="Book your first service to get started with CTRON Home"
      icon="🏠"
      actionTitle="Book a Service"
      onAction={() => navigation.navigate('BookJob')}
    />
  ), [navigation]);

  // Styles definition moved inside component
  const styles = useMemo(() => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  headerContainer: {
    padding: spacing.md,
    backgroundColor: colors.background.primary,
  },
  welcomeSection: {
    marginBottom: spacing.md,
  },
  welcomeText: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  nameText: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
  },
  profileButton: {
    padding: spacing.sm,
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  jobCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.md,
    ...shadows.md,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  jobCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  jobTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.text.primary,
  },
  jobStatus: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  jobDetails: {
    marginBottom: spacing.sm,
  },
  jobDetailText: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  jobActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: spacing.sm,
  },
  actionButton: {
    marginLeft: spacing.sm,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.primary[500],
  },
  actionButtonText: {
    ...typography.button,
    color: colors.text.inverse,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
  },
  logoutButton: {
    padding: spacing.sm,
  },
  logoutIcon: {
    fontSize: typography.fontSize.lg,
    color: colors.primary[500],
  },
  bookButton: {
    marginTop: spacing.md,
    backgroundColor: colors.primary.main,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacing.md,
    paddingHorizontal: spacing.md,
  },
  summaryCard: {
    flex: 1,
    marginHorizontal: spacing.xs,
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  summaryValue: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.lg,
  },
  emptyButton: {
    minWidth: 120,
  },
  contentContainer: {
    paddingBottom: spacing.xl,
  },

  }), [colors, spacing, typography, borderRadius, shadows]);

  if (loading) {
    return (
      <Screen>
        <Header
          title="CTRON Home"
          rightAction={{
            icon: <Text style={styles.logoutIcon}>👤</Text>,
            onPress: () => logout(true),
            accessibilityLabel: 'User menu',
          }}
        />
        <LoadingState message="Loading your jobs..." />
      </Screen>
    );
  }

  return (
    <Screen>
      <Header
        title="CTRON Home"
        rightAction={{
          icon: <Text style={styles.logoutIcon}>👤</Text>,
          onPress: () => logout(true),
          accessibilityLabel: 'User menu',
        }}
      />
      <FlatList
        data={jobs.slice(0, 5)} // Show only recent jobs
        keyExtractor={(item: unknown) => (item as { id: string }).id}
        renderItem={renderJobItem}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyComponent}
        refreshControl={null}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={5}
        getItemLayout={(data: unknown, index: number) => ({
          length: 120, // Approximate item height
          offset: 120 * index,
          index,
        })}
      />
    </Screen>
  );
}

export default HomeScreen;
