// CTRON Home - Modern My Jobs Screen v2.0
// Enhanced job listing with advanced filtering, search, and modern UI - Updated Styling

import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import React, { useEffect, useState, useMemo, useRef } from 'react';
import SafeLinearGradient from '../../components/SafeLinearGradient';
import { BlurView } from 'expo-blur';
import type { ViewStyle } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { JobAPI } from '../../api/job.api';
import type { Job } from '../../types/job';
import { Button, StatusBadge } from '../../design-system';
import { useTheme } from '../../context/ThemeContext';
import type { HomeownerStackParamList } from '../../navigation/types';
import { 
  View, 
  Text, 
  FlatList, 
  StyleSheet, 
  TouchableOpacity, 
  ActivityIndicator,
  TextInput,
  Animated,
  Dimensions,
  RefreshControl,
  StatusBar,
  SafeAreaView
} from '../../utils/platformUtils';

interface JobWithDetails {
  id: string;
  title: string;
  description: string;
  status: 'PENDING' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  scheduledDate?: string;
  address: string;
  serviceType: string;
  price?: number;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  technician?: {
    id: string;
    user: {
      fullName: string;
    };
    rating?: number;
    specialization?: string;
  };
  createdAt: string;
  updatedAt: string;
}

type NavigationProp = NativeStackNavigationProp<HomeownerStackParamList>;

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ModernMyJobsScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { colors, spacing, typography, borderRadius } = useTheme();

  const [jobs, setJobs] = useState<JobWithDetails[]>([]);
  const [filteredJobs, setFilteredJobs] = useState<JobWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'active' | 'completed' | 'pending'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'status' | 'priority'>('date');
  const [showFilters, setShowFilters] = useState(false);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const filterSlideAnim = useRef(new Animated.Value(-100)).current;

  const loadJobs = async () => {
    try {
      setLoading(true);
      const response = await JobAPI.getMyJobs();
      const jobsData = (response || []).map((job: any) => ({
        ...job,
        description: job.description || job.issue || 'No description available',
        title: job.title || job.issue || 'Untitled Job'
      }) as JobWithDetails);
      setJobs(jobsData);
      setFilteredJobs(jobsData);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load your jobs',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadJobs();
    
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  useEffect(() => {
    filterAndSortJobs();
  }, [jobs, searchQuery, selectedFilter, sortBy]);

  useEffect(() => {
    Animated.timing(filterSlideAnim, {
      toValue: showFilters ? 0 : -100,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [showFilters]);

  const filterAndSortJobs = () => {
    let filtered = jobs;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(job => 
        job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.serviceType.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (selectedFilter !== 'all') {
      if (selectedFilter === 'active') {
        filtered = filtered.filter(job => ['PENDING', 'ACCEPTED', 'IN_PROGRESS'].includes(job.status));
      } else if (selectedFilter === 'completed') {
        filtered = filtered.filter(job => ['COMPLETED', 'CANCELLED'].includes(job.status));
      } else if (selectedFilter === 'pending') {
        filtered = filtered.filter(job => job.status === 'PENDING');
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        case 'status':
          return a.status.localeCompare(b.status);
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          return (priorityOrder[b.priority || 'medium'] || 2) - (priorityOrder[a.priority || 'medium'] || 2);
        default:
          return 0;
      }
    });

    setFilteredJobs(filtered);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadJobs();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return '#FF9500';
      case 'ACCEPTED': return '#007AFF';
      case 'IN_PROGRESS': return '#34C759';
      case 'COMPLETED': return '#8E8E93';
      case 'CANCELLED': return '#FF3B30';
      default: return colors.neutral.main;
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'urgent': return '#FF3B30';
      case 'high': return '#FF9500';
      case 'medium': return '#007AFF';
      case 'low': return '#8E8E93';
      default: return '#8E8E93';
    }
  };

  const FilterChip = ({ label, value, isActive, onPress }: {
    label: string;
    value: string;
    isActive: boolean;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      style={[
        styles.filterChip, 
        isActive && styles.activeFilterChip,
        { 
          borderWidth: 2,
          borderColor: isActive ? colors.primary.main : 'rgba(255, 255, 255, 0.3)',
          backgroundColor: isActive ? colors.primary.main : 'rgba(255, 255, 255, 0.15)'
        }
      ]}
      onPress={onPress}
    >
      <Text style={[styles.filterChipText, isActive && styles.activeFilterChipText]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const JobCard = ({ item }: { item: JobWithDetails }) => {
    const statusColor = getStatusColor(item.status);
    const priorityColor = getPriorityColor(item.priority);

    return (
      <TouchableOpacity
        style={[
          styles.jobCard,
          { 
            borderWidth: 1,
            borderColor: 'rgba(255, 255, 255, 0.2)',
            backgroundColor: 'rgba(255, 255, 255, 0.08)'
          }
        ]}
        onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
      >
        <View style={styles.jobCardGlass as ViewStyle}>
              <BlurView intensity={20} />
            </View>
        <View style={styles.jobCardContent}>
          {/* Header */}
          <View style={styles.jobCardHeader}>
            <View style={styles.jobTitleContainer}>
              <View style={[styles.statusIndicator, { backgroundColor: statusColor }]} />
              <Text style={styles.jobTitle} numberOfLines={1}>{item.title}</Text>
            </View>
            <View style={styles.jobCardActions}>
              {item.priority && (
                <View style={[styles.priorityBadge, { backgroundColor: priorityColor + '20' }]}>
                  <Text style={[styles.priorityText, { color: priorityColor }]}>
                    {item.priority.toUpperCase()}
                  </Text>
                </View>
              )}
              <Ionicons name="chevron-forward" size={16} color={colors.text.secondary} />
            </View>
          </View>

          {/* Content */}
          <Text style={styles.jobDescription} numberOfLines={2}>
            {item.description}
          </Text>

          {/* Service Type */}
          <View style={styles.serviceTypeContainer}>
            <Ionicons name="construct-outline" size={14} color={colors.text.secondary} />
            <Text style={styles.serviceType}>{item.serviceType}</Text>
          </View>

          {/* Footer */}
          <View style={styles.jobCardFooter}>
            <View style={styles.jobMeta}>
              <View style={styles.metaItem}>
                <Ionicons name="calendar-outline" size={14} color={colors.text.secondary} />
                <Text style={styles.metaText}>
                  {new Date(item.scheduledDate || item.createdAt).toLocaleDateString()}
                </Text>
              </View>
              
              {item.price && (
                <View style={styles.metaItem}>
                  <Ionicons name="card-outline" size={14} color={colors.text.secondary} />
                  <Text style={styles.metaText}>£{item.price}</Text>
                </View>
              )}
            </View>

            {item.technician && (
              <View style={styles.technicianContainer}>
                <View style={styles.technicianAvatar}>
                  <Text style={styles.technicianInitial}>
                    {item.technician.user.fullName.charAt(0)}
                  </Text>
                </View>
                <View style={styles.technicianInfo}>
                  <Text style={styles.technicianName} numberOfLines={1}>
                    {item.technician.user.fullName}
                  </Text>
                  {item.technician.rating && (
                    <View style={styles.ratingContainer}>
                      <Ionicons name="star" size={12} color="#FFD700" />
                      <Text style={styles.ratingText}>{item.technician.rating}</Text>
                    </View>
                  )}
                </View>
              </View>
            )}
          </View>

          {/* Status Badge */}
          <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
            <Text style={styles.statusText}>{item.status.replace('_', ' ')}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const EmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="briefcase-outline" size={64} color={colors.text.secondary} />
      <Text style={styles.emptyTitle}>No Jobs Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery 
          ? `No jobs match "${searchQuery}"`
          : selectedFilter === 'all'
          ? 'You haven\'t booked any services yet'
          : `No ${selectedFilter} jobs found`
        }
      </Text>
      {!searchQuery && selectedFilter === 'all' && (
        <Button
          title="Book Your First Service"
          onPress={() => navigation.navigate('BookJob')}
          variant="primary"
          size="md"
          style={styles.emptyButton}
        />
      )}
    </View>
  );

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    gradientBackground: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: screenHeight * 0.3,
      zIndex: -1,
    },
    topBackground: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: (StatusBar.currentHeight || 44) + 100,
      zIndex: 1,
    },
    header: {
      paddingTop: (StatusBar.currentHeight || 44) + spacing.md,
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.lg,
      backgroundColor: 'transparent',
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    headerTitle: {
      fontSize: 32,
      fontWeight: '800',
      color: colors.text.primary,
      textShadowColor: 'rgba(0, 0, 0, 0.1)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    headerActions: {
      flexDirection: 'row',
      gap: spacing.sm,
    },
    headerButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    searchContainer: {
      marginBottom: spacing.xl,
    },
    searchInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      borderRadius: borderRadius.xl,
      paddingHorizontal: spacing.lg,
      height: 52,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.2)',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 3,
    },
    searchInput: {
      flex: 1,
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      marginLeft: spacing.sm,
    },
    filtersContainer: {
      paddingHorizontal: spacing.lg,
      marginTop: spacing.md,
      marginBottom: spacing.xl,
    },
    filtersRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    filtersScrollView: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    filterChip: {
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderRadius: borderRadius.full,
      backgroundColor: 'rgba(255, 255, 255, 0.2)', // Increased opacity for visibility
      borderWidth: 2, // Increased border width
      borderColor: 'rgba(255, 255, 255, 0.4)', // Increased border opacity
      marginRight: spacing.md,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 }, // Increased shadow
      shadowOpacity: 0.12,
      shadowRadius: 8,
      elevation: 5,
      transform: [{ scale: 1 }],
    },
    activeFilterChip: {
      backgroundColor: colors.primary.main,
      borderColor: colors.primary.main,
      shadowColor: colors.primary.main,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.4,
      shadowRadius: 8,
      elevation: 6,
      transform: [{ scale: 1.05 }],
    },
    filterChipText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      fontWeight: typography.fontWeight.medium,
    },
    activeFilterChipText: {
      color: colors.text.inverse,
    },
    sortButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
    },
    sortText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginRight: spacing.xs,
    },
    jobsList: {
      flex: 1,
      paddingHorizontal: spacing.lg,
    },
    jobCard: {
      marginBottom: spacing.lg,
      borderRadius: borderRadius.lg,
      overflow: 'hidden',
      elevation: 8,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      transform: [{ scale: 1 }],
      backgroundColor: 'rgba(255, 255, 255, 0.05)', // Add background to ensure visibility
    },
    jobCardGlass: {
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.12)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.18)',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.1,
      shadowRadius: 16,
      elevation: 6,
    },
    jobCardContent: {
      padding: spacing.xl,
      gap: spacing.md,
      position: 'relative',
      zIndex: 1,
    },
    jobCardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: spacing.sm,
    },
    jobTitleContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    statusIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: spacing.sm,
    },
    jobTitle: {
      flex: 1,
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
    },
    jobCardActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.sm,
    },
    priorityBadge: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
    },
    priorityText: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.bold,
    },
    jobDescription: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      lineHeight: 20,
      marginBottom: spacing.sm,
    },
    serviceTypeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    serviceType: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginLeft: spacing.xs,
      fontWeight: typography.fontWeight.medium,
    },
    jobCardFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
    },
    jobMeta: {
      flex: 1,
    },
    metaItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.xs,
    },
    metaText: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      marginLeft: spacing.xs,
    },
    technicianContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    technicianAvatar: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.primary.main,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.sm,
    },
    technicianInitial: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    technicianInfo: {
      flex: 1,
    },
    technicianName: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    ratingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    ratingText: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      marginLeft: spacing.xs,
    },
    statusBadge: {
      position: 'absolute',
      top: spacing.sm,
      right: spacing.sm,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
    },
    statusText: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
      textTransform: 'capitalize',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: spacing.xl,
      paddingVertical: spacing.xxl,
    },
    emptyTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginTop: spacing.lg,
      marginBottom: spacing.sm,
    },
    emptyDescription: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: 22,
      marginBottom: spacing.xl,
    },
    emptyButton: {
      minWidth: 200,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
  }), [colors, spacing, typography, borderRadius]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <Animated.View 
        style={[
          styles.container,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
      <View style={styles.gradientBackground}>
        <SafeLinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>

      <View style={styles.topBackground}>
        <SafeLinearGradient
          colors={['#667eea', '#764ba2']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>My Jobs ✨</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={() => setShowFilters(!showFilters)}
            >
              <Ionicons name="options-outline" size={20} color={colors.text.primary} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={() => navigation.navigate('BookJob')}
            >
              <Ionicons name="add" size={20} color={colors.text.primary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color={colors.text.secondary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search jobs..."
              placeholderTextColor={colors.text.secondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={colors.text.secondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>

      {/* Filters */}
      <Animated.View 
        style={[
          styles.filtersContainer,
          { transform: [{ translateY: filterSlideAnim }] }
        ]}
      >
        <View style={styles.filtersRow}>
          <View style={styles.filtersScrollView}>
            <FilterChip
              label="All"
              value="all"
              isActive={selectedFilter === 'all'}
              onPress={() => setSelectedFilter('all')}
            />
            <FilterChip
              label="Active"
              value="active"
              isActive={selectedFilter === 'active'}
              onPress={() => setSelectedFilter('active')}
            />
            <FilterChip
              label="Completed"
              value="completed"
              isActive={selectedFilter === 'completed'}
              onPress={() => setSelectedFilter('completed')}
            />
            <FilterChip
              label="Pending"
              value="pending"
              isActive={selectedFilter === 'pending'}
              onPress={() => setSelectedFilter('pending')}
            />
          </View>
          
          <TouchableOpacity 
            style={styles.sortButton}
            onPress={() => {
              const sortOptions = ['date', 'status', 'priority'] as const;
              const currentIndex = sortOptions.indexOf(sortBy);
              const nextIndex = (currentIndex + 1) % sortOptions.length;
              setSortBy(sortOptions[nextIndex]);
            }}
          >
            <Text style={styles.sortText}>Sort: {sortBy}</Text>
            <Ionicons name="swap-vertical" size={16} color={colors.text.secondary} />
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Jobs List */}
      <FlatList
        style={styles.jobsList}
        data={filteredJobs}
        renderItem={({ item }: { item: JobWithDetails }) => <JobCard item={item} />}
        keyExtractor={(item: JobWithDetails) => item.id}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary.main}
          />
        }
        ListEmptyComponent={EmptyState}
        contentContainerStyle={filteredJobs.length === 0 ? { flex: 1 } : undefined}
      />
      </Animated.View>
    </SafeAreaView>
  );
};

export default ModernMyJobsScreen;