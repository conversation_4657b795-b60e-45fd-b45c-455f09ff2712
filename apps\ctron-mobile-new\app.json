{"expo": {"name": "CTRON Home", "slug": "ctron-mobile", "version": "1.0.0", "sdkVersion": "52.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "scheme": "ctronhome", "assetBundlePatterns": ["**/*"], "platforms": ["ios", "android"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.ctron.home"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.ctron.home", "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.RECORD_AUDIO"]}, "plugins": ["expo-secure-store", "expo-font", "expo-notifications", "expo-location", "expo-image-picker", "expo-document-picker", "expo-background-task", ["expo-splash-screen", {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-asset"], "updates": {"enabled": false, "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0}, "runtimeVersion": {"policy": "appVersion"}, "newArchEnabled": false, "extra": {"APP_ENVIRONMENT": "development", "eas": {"projectId": "31a00d7d-8d6d-4a4c-bdb9-8f0e9844f909"}}, "owner": "zacjactech"}}