// CTRON Home - Technician API Service
// API calls for technician-related functionality

import { API_BASE_URL } from '../config/api.config';
import { getAuthToken } from '../utils/auth.utils';

export interface Technician {
  id: string;
  userId: string;
  specialization: string;
  rating?: number;
  completedJobs: number;
  isAvailable: boolean;
  hourlyRate?: number;
  distance?: number;
  responseTime?: string;
  skills?: string[];
  user: {
    id: string;
    fullName: string;
    email: string;
    phone?: string;
    profileImage?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface TechnicianProfile {
  id: string;
  specialization: string;
  experience: number;
  certifications: string[];
  serviceAreas: string[];
  hourlyRate: number;
  isAvailable: boolean;
  rating: number;
  completedJobs: number;
  bio?: string;
  skills: string[];
}

// Helper function to make authenticated API calls
const makeAuthenticatedRequest = async (method: string, endpoint: string, data?: unknown) => {
  const token = await getAuthToken();

  if (!token) {
    throw new Error('Authentication token not found. Please log in again.');
  }

  const config: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  };

  if (data) {
    config.body = JSON.stringify(data);
  }

  if (__DEV__) {
    console.log(`🔗 Technician API Request: ${method} ${endpoint}`);
    console.log('📦 Request data:', data);
  }

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

    if (__DEV__) {
      console.log(`✅ Technician API Response: ${method} ${endpoint} - ${response.status}`);
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  } catch (error: unknown) {
    if (__DEV__) {
      console.error(`❌ Technician API Error: ${method} ${endpoint}`);
      console.error('Error:', error);
    }

    // Enhanced error handling for common HTTP status codes
    if (error instanceof Error) {
      if (error.message.includes('401')) {
        throw new Error('Authentication failed. Please log in again.');
      } else if (error.message.includes('403')) {
        throw new Error('You do not have permission to perform this action.');
      } else if (error.message.includes('404')) {
        throw new Error('The requested resource was not found.');
      } else if (error.message.includes('500')) {
        throw new Error('Server error. Please try again later.');
      }
    }

    throw error;
  }
};

export const TechnicianAPI = {
  /**
   * Get all available technicians
   */
  getAvailableTechnicians: async (): Promise<{ technicians: Technician[] }> => {
    return makeAuthenticatedRequest('GET', '/api/technicians/available');
  },

  /**
   * Get technicians by specialization
   */
  getTechniciansBySpecialization: async (specialization: string): Promise<{ technicians: Technician[] }> => {
    return makeAuthenticatedRequest('GET', `/api/technicians/specialization/${specialization}`);
  },

  /**
   * Get technician profile by ID
   */
  getTechnicianProfile: async (technicianId: string): Promise<{ technician: Technician }> => {
    return makeAuthenticatedRequest('GET', `/api/technicians/${technicianId}`);
  },

  /**
   * Update technician availability status
   */
  updateAvailability: async (isAvailable: boolean): Promise<{ success: boolean }> => {
    return makeAuthenticatedRequest('PUT', '/api/technicians/availability', { isAvailable });
  },

  /**
   * Update technician profile
   */
  updateProfile: async (profileData: Partial<TechnicianProfile>): Promise<{ technician: Technician }> => {
    return makeAuthenticatedRequest('PUT', '/api/technicians/profile', profileData);
  },

  /**
   * Get technician earnings
   */
  getEarnings: async (period?: 'today' | 'week' | 'month'): Promise<{
    todayEarnings: number;
    weeklyEarnings: number;
    monthlyEarnings: number;
    completedToday: number;
    completedWeek: number;
    completedMonth: number;
  }> => {
    const queryParam = period ? `?period=${period}` : '';
    return makeAuthenticatedRequest('GET', `/api/technicians/earnings${queryParam}`);
  },

  /**
   * Get technician job history
   */
  getJobHistory: async (page = 1, limit = 20): Promise<{
    jobs: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> => {
    return makeAuthenticatedRequest('GET', `/api/technicians/jobs?page=${page}&limit=${limit}`);
  },

  /**
   * Search technicians by location and specialization
   */
  searchTechnicians: async (params: {
    latitude?: number;
    longitude?: number;
    specialization?: string;
    radius?: number; // in kilometers
    sortBy?: 'distance' | 'rating' | 'price';
  }): Promise<{ technicians: Technician[] }> => {
    const queryParams = new URLSearchParams();
    
    if (params.latitude) queryParams.append('lat', params.latitude.toString());
    if (params.longitude) queryParams.append('lng', params.longitude.toString());
    if (params.specialization) queryParams.append('specialization', params.specialization);
    if (params.radius) queryParams.append('radius', params.radius.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);

    const queryString = queryParams.toString();
    const endpoint = `/api/technicians/search${queryString ? `?${queryString}` : ''}`;

    return makeAuthenticatedRequest('GET', endpoint);
  },

  /**
   * Rate a technician after job completion
   */
  rateTechnician: async (technicianId: string, jobId: string, rating: number, review?: string): Promise<{ success: boolean }> => {
    return makeAuthenticatedRequest('POST', `/api/technicians/${technicianId}/rate`, {
      jobId,
      rating,
      review,
    });
  },

  /**
   * Get technician reviews
   */
  getTechnicianReviews: async (technicianId: string, page = 1, limit = 10): Promise<{
    reviews: Array<{
      id: string;
      rating: number;
      review?: string;
      jobId: string;
      homeowner: {
        fullName: string;
      };
      createdAt: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    averageRating: number;
  }> => {
    return makeAuthenticatedRequest('GET', `/api/technicians/${technicianId}/reviews?page=${page}&limit=${limit}`);
  },

  /**
   * Get technician profile
   */
  getProfile: async (): Promise<{ technician: Technician }> => {
    return makeAuthenticatedRequest('GET', '/api/technicians/profile');
  },

  /**
   * Find technicians with search criteria
   */
  findTechnicians: async (params: {
    latitude?: number;
    longitude?: number;
    specialization?: string;
    radius?: number;
    sortBy?: 'distance' | 'rating' | 'price';
  }): Promise<{ technicians: Technician[] }> => {
    const queryParams = new URLSearchParams();
    
    if (params.latitude) queryParams.append('lat', params.latitude.toString());
    if (params.longitude) queryParams.append('lng', params.longitude.toString());
    if (params.specialization) queryParams.append('specialization', params.specialization);
    if (params.radius) queryParams.append('radius', params.radius.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);

    const queryString = queryParams.toString();
    const endpoint = `/api/technicians/search${queryString ? `?${queryString}` : ''}`;

    return makeAuthenticatedRequest('GET', endpoint);
  },

  /**
   * Create verification session for KYC
   */
  createVerificationSession: async (technicianId: string): Promise<{ sessionUrl: string }> => {
    return makeAuthenticatedRequest('POST', `/api/technicians/${technicianId}/verification`);
  },
};