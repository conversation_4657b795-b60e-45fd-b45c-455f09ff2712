// apps/backend/src/routes/ai.routes.ts

import { Router, Response } from 'express';
import { z } from 'zod';
import { authenticateToken } from '../middleware/auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { AIService } from '../services/ai.service';
import { logger } from '../utils/logger';
import { AuthenticatedRequest } from '../types/auth';

const router = Router();
const aiService = new AIService();

// Validation schemas
const querySchema = z.object({
  body: z.object({
    query: z.string().min(1, 'Query is required').max(1000, 'Query too long'),
    context: z.string().optional(),
  }),
});

/**
 * @route POST /api/ai/query
 * @desc Process AI query with role-based context
 * @access Private
 */
router.post(
  '/query',
  authenticateToken,
  validateRequest(querySchema),
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { query, context } = req.body;
      const { userId, role } = req.user!;

      logger.info(`AI query from ${role} user ${userId}: ${query.substring(0, 50)}...`);

      const response = await aiService.processQuery({
        query,
        context,
        userId,
        role,
      });

      res.status(200).json({
        success: true,
        data: response,
      });
    } catch (error: any) {
      logger.error('AI query processing failed:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to process AI query',
      });
    }
  }
);

/**
 * @route GET /api/ai/templates
 * @desc Get query templates for the user's role
 * @access Private
 */
router.get('/templates', authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { role } = req.user!;

    const templates = await aiService.getQueryTemplates(role);

    res.status(200).json({
      success: true,
      data: { templates },
    });
  } catch (error: any) {
    logger.error('Failed to get query templates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get query templates',
    });
  }
});

/**
 * @route GET /api/ai/suggestions
 * @desc Get AI-powered suggestions for the user
 * @access Private
 */
router.get('/suggestions', authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userId, role } = req.user!;

    // Generate contextual suggestions
    const suggestions = await aiService.getQueryTemplates(role);

    res.status(200).json({
      success: true,
      data: { suggestions },
    });
  } catch (error: any) {
    logger.error('Failed to get AI suggestions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get AI suggestions',
    });
  }
});

/**
 * @route POST /api/ai/feedback
 * @desc Submit feedback on AI response quality
 * @access Private
 */
router.post('/feedback', authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { queryId, rating, feedback } = req.body;
    const { userId } = req.user!;

    // Validate input
    if (!queryId || !rating || rating < 1 || rating > 5) {
      res.status(400).json({
        success: false,
        message: 'Valid query ID and rating (1-5) are required',
      });
      return;
    }

    // Log feedback for improvement
    logger.info(`AI feedback from user ${userId}: Query ${queryId}, Rating: ${rating}, Feedback: ${feedback}`);

    // In a real implementation, you'd save this to a database
    // await aiService.saveFeedback(queryId, userId, rating, feedback);

    res.status(200).json({
      success: true,
      message: 'Feedback submitted successfully',
    });
  } catch (error: any) {
    logger.error('Failed to submit AI feedback:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit feedback',
    });
  }
});

/**
 * @route GET /api/ai/history
 * @desc Get user's AI query history
 * @access Private
 */
router.get('/history', authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userId } = req.user!;
    const limit = parseInt(req.query.limit as string) || 20;
    const page = parseInt(req.query.page as string) || 1;

    // Get query history from AI service
    const history = await aiService.getQueryHistory(userId, { limit, page });

    res.status(200).json({
      success: true,
      data: history,
    });
  } catch (error: any) {
    logger.error('Failed to get AI history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get query history',
    });
  }
});

/**
 * @route GET /api/ai/status
 * @desc Get AI service status and capabilities
 * @access Private
 */
router.get('/status', authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const status = {
      available: true,
      model: 'gpt-4',
      capabilities: [
        'Role-based assistance',
        'Contextual responses',
        'Query templates',
        'Performance analytics',
      ],
      supportedRoles: ['ADMIN', 'TECHNICIAN', 'HOMEOWNER'],
      maxQueryLength: 1000,
      responseTime: '2-5 seconds',
    };

    res.status(200).json({
      success: true,
      data: status,
    });
  } catch (error: any) {
    logger.error('Failed to get AI status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get AI status',
    });
  }
});

export default router;
