// src/utils/storage.ts
import AsyncStorage from './asyncStorage';
import * as SecureStore from './secureStore';

export const saveItem = async (key: string, value: unknown) => {
  const serialized = typeof value === 'string' ? value : JSON.stringify(value);
  await SecureStore.setItemAsync(key, serialized);
};

export const getItem = async <T = string>(key: string): Promise<T | null> => {
  const raw = await SecureStore.getItemAsync(key);
  try {
    return raw ? (JSON.parse(raw) as T) : null;
  } catch {
    return raw as unknown as T; // fallback if value is primitive
  }
};

export const deleteItem = async (key: string) => {
  await SecureStore.deleteItemAsync(key);
};

export const clear = async () => {
  await AsyncStorage.clear();
};
