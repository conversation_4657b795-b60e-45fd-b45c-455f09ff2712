/**
 * This script checks and fixes the AppEntry.js file in node_modules/expo
 * to ensure it's properly formatted and doesn't contain syntax errors.
 */

const fs = require('fs');
const path = require('path');

const chalk = require('chalk');

console.log(chalk.blue('Checking and fixing AppEntry.js...'));

// Path to the AppEntry.js file
const appEntryPath = path.join(__dirname, '..', 'node_modules', 'expo', 'AppEntry.js');

// Check if the file exists
if (!fs.existsSync(appEntryPath)) {
  console.log(chalk.red(`AppEntry.js not found at ${appEntryPath}`));
  process.exit(1);
}

// Read the current content
let content = fs.readFileSync(appEntryPath, 'utf8');
console.log(chalk.yellow('Current AppEntry.js content:'));
console.log(content);

// The correct content for AppEntry.js
const correctContent = `import registerRootComponent from 'expo/src/launch/registerRootComponent';

import App from '../../App';

registerRootComponent(App);
`;

// Check if the content is already correct
if (content === correctContent) {
  console.log(chalk.green('AppEntry.js is already correct.'));
} else {
  // Fix the file
  console.log(chalk.yellow('Fixing AppEntry.js...'));
  fs.writeFileSync(appEntryPath, correctContent);
  console.log(chalk.green('AppEntry.js has been fixed.'));
}

// Also check for any BOM (Byte Order Mark) issues
const buffer = fs.readFileSync(appEntryPath);
if (buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
  console.log(chalk.yellow('BOM detected in AppEntry.js, removing...'));
  const contentWithoutBOM = buffer.slice(3);
  fs.writeFileSync(appEntryPath, contentWithoutBOM);
  console.log(chalk.green('BOM removed from AppEntry.js.'));
}

console.log(chalk.blue('AppEntry.js check and fix completed.'));