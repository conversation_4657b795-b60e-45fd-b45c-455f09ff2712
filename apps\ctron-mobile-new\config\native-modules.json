{"modules": ["react-native/Libraries/Image/Image", "react-native/Libraries/Utilities/codegenNativeCommands", "react-native/Libraries/Components/View/ViewNativeComponent", "react-native/Libraries/Components/TextInput/TextInputNativeCommands", "react-native/Libraries/Utilities/Platform", "react-native/Libraries/Utilities/Dimensions", "react-native/Libraries/Utilities/NativeDeviceInfo", "react-native/Libraries/Utilities/NativePlatformConstantsIOS", "react-native/Libraries/Utilities/NativePlatformConstantsAndroid", "react-native/Libraries/StyleSheet/PlatformColorValueTypes", "react-native/Libraries/Animated/NativeAnimatedHelper", "react-native/Libraries/NativeComponent/NativeComponentRegistry", "react-native/Libraries/EventEmitter/NativeEventEmitter", "react-native/Libraries/EventEmitter/RCTDeviceEventEmitter", "react-native/Libraries/EventEmitter/RCTEventEmitter", "react-native/Libraries/ReactNative/AppRegistry", "react-native/Libraries/ReactNative/UIManager", "react-native/Libraries/Renderer/shims/ReactNative", "react-native/Libraries/Vibration/Vibration", "react-native/Libraries/Linking/Linking", "react-native/Libraries/LogBox/LogBox", "react-native/Libraries/PermissionsAndroid/PermissionsAndroid", "react-native/Libraries/Alert/Alert", "react-native/Libraries/AppState/AppState", "react-native/Libraries/Storage/AsyncStorage", "react-native/Libraries/Geolocation/Geolocation", "react-native/Libraries/PushNotificationIOS/PushNotificationIOS", "react-native/Libraries/Settings/Settings", "react-native/Libraries/Share/Share", "react-native/Libraries/TurboModule/TurboModuleRegistry", "react-native/Libraries/Performance/PureComponentDebug", "react-native/Libraries/Performance/SamplingProfiler", "react-native/Libraries/Performance/Systrace", "react-native/Libraries/Performance/ReactPerf", "react-native/Libraries/Performance/NativePerformance", "react-native/Libraries/Performance/NativePerformanceObserver", "react-native/Libraries/SourceCode/SourceCode", "../Utilities/Platform", "../../Utilities/Platform", "../NativeComponent/NativeComponentRegistry", "../../NativeComponent/NativeComponentRegistry", "../EventEmitter/NativeEventEmitter", "../../EventEmitter/NativeEventEmitter", "../LogBox/LogBox", "../../LogBox/LogBox", "../SourceCode/SourceCode", "../../SourceCode/SourceCode", "../Performance/NativePerformance", "../../Performance/NativePerformance", "../Performance/NativePerformanceObserver", "../../Performance/NativePerformanceObserver", "../TurboModule/TurboModuleRegistry", "../../TurboModule/TurboModuleRegistry"]}