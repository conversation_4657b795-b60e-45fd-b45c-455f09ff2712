// CTRON Home - Screen Component
// Consistent screen wrapper with proper styling and layout

import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useTheme } from '../../context/ThemeContext';
import { View, StyleSheet } from '../../utils/platformUtils';

interface ScreenProps {
  children: React.ReactNode;
  backgroundColor?: string;
  statusBarStyle?: 'light' | 'dark' | 'auto';
  safeArea?: boolean;
  padding?: boolean;
  style?: any;
}

export const Screen: React.FC<ScreenProps> = ({
  children,
  backgroundColor,
  statusBarStyle = 'dark',
  safeArea = true,
  padding = false,
  style,
}) => {
  const { colors, spacing } = useTheme();

  const screenStyle = [
    styles.container,
    {
      backgroundColor: backgroundColor || colors.background.primary,
      padding: padding ? spacing.md : 0,
    },
    style,
  ];

  const content = (
    <View style={screenStyle}>
      <StatusBar style={statusBarStyle} backgroundColor={backgroundColor || colors.background.primary} />
      {children}
    </View>
  );

  if (safeArea) {
    return (
      <SafeAreaView edges={['top', 'left', 'right']}>
        <View style={[styles.container, { backgroundColor: backgroundColor || colors.background.primary }]}>
          <StatusBar style={statusBarStyle} backgroundColor={backgroundColor || colors.background.primary} />
          {children}
        </View>
      </SafeAreaView>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});