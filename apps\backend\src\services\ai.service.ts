// apps/backend/src/services/ai.service.ts

import OpenAI from 'openai';
import { prisma } from '../config/db';
import { logger } from '../utils/logger';
import { env } from '../config/env';
import { Job, Review } from '@prisma/client';

interface AIQuery {
  query: string;
  context?: string;
  userId: string;
  role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
}

interface AIResponse {
  response: string;
  confidence: number;
  sources?: string[];
  suggestions?: string[];
}

export class AIService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: env.OPENAI_API_KEY,
    });
  }

  /**
   * Process AI query with role-based context
   */
  async processQuery(query: AIQuery): Promise<AIResponse> {
    try {
      logger.info(`Processing AI query for ${query.role}: ${query.query.substring(0, 50)}...`);

      // Get relevant context based on user role
      const context = await this.getContextForRole(query.userId, query.role);
      
      // Build system prompt based on role
      const systemPrompt = this.buildSystemPrompt(query.role, context);
      
      // Get AI response
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: query.query },
        ],
        max_tokens: 500,
        temperature: 0.7,
      });

      const response = completion.choices[0]?.message?.content || 'I apologize, but I could not generate a response.';
      
      // Save query and response for analytics
      await this.saveQueryHistory(query, response);

      return {
        response,
        confidence: 0.85, // You could implement confidence scoring
        sources: this.extractSources(context),
        suggestions: await this.generateSuggestions(query.role),
      };
    } catch (error) {
      logger.error('AI query processing failed:', error);
      throw new Error('Failed to process AI query');
    }
  }

  /**
   * Get relevant context based on user role
   */
  private async getContextForRole(userId: string, role: string): Promise<string> {
    try {
      let context = '';

      switch (role) {
        case 'ADMIN':
          // Admin gets comprehensive system overview
          const adminStats = await this.getAdminContext();
          context = `System Overview:
- Total Users: ${adminStats.totalUsers}
- Active Jobs: ${adminStats.activeJobs}
- Pending Jobs: ${adminStats.pendingJobs}
- Total Revenue: £${adminStats.totalRevenue}
- Average Rating: ${adminStats.averageRating}
- Recent Issues: ${adminStats.recentIssues.join(', ')}`;
          break;

        case 'TECHNICIAN':
          // Technician gets their job and performance context
          const techContext = await this.getTechnicianContext(userId);
          context = `Technician Profile:
- Specialization: ${techContext.specialization}
- Current Rating: ${techContext.rating}
- Active Jobs: ${techContext.activeJobs}
- Completed Jobs: ${techContext.completedJobs}
- Recent Reviews: ${techContext.recentReviews.join(', ')}`;
          break;

        case 'HOMEOWNER':
          // Homeowner gets their job history and preferences
          const homeownerContext = await this.getHomeownerContext(userId);
          context = `User Profile:
- Total Jobs: ${homeownerContext.totalJobs}
- Recent Jobs: ${homeownerContext.recentJobs.join(', ')}
- Preferred Services: ${homeownerContext.preferredServices.join(', ')}
- Average Spending: £${homeownerContext.averageSpending}`;
          break;
      }

      return context;
    } catch (error) {
      logger.error('Failed to get context for role:', error);
      return '';
    }
  }

  /**
   * Build system prompt based on role
   */
  private buildSystemPrompt(role: string, context: string): string {
    const basePrompt = `You are CTRON Assistant, an AI helper for the CTRON Home services platform. 
You provide helpful, accurate, and professional responses related to home services, job management, and platform usage.

Current Context:
${context}

Guidelines:
- Be helpful and professional
- Provide specific, actionable advice
- Reference relevant platform features
- Keep responses concise but informative
- If you don't know something, say so clearly`;

    const roleSpecificPrompts = {
      ADMIN: `
Role: You are assisting a platform administrator.
Focus on: System management, user analytics, business insights, operational efficiency, and platform optimization.
You can help with: User management, job oversight, financial reporting, system health, and strategic decisions.`,

      TECHNICIAN: `
Role: You are assisting a service technician.
Focus on: Job management, customer communication, technical guidance, scheduling, and performance improvement.
You can help with: Job details, customer preferences, technical tips, scheduling optimization, and service quality.`,

      HOMEOWNER: `
Role: You are assisting a homeowner/customer.
Focus on: Service requests, job tracking, payment information, and general platform usage.
You can help with: Creating jobs, understanding services, tracking progress, payment questions, and platform navigation.`,
    };

    return basePrompt + '\n' + (roleSpecificPrompts[role as keyof typeof roleSpecificPrompts] || '');
  }

  /**
   * Get admin context data
   */
  private async getAdminContext() {
    const [
      totalUsers,
      activeJobs,
      pendingJobs,
      payments,
      reviews,
      recentJobs,
    ] = await Promise.all([
      prisma.user.count(),
      prisma.job.count({ where: { status: { in: ['ACCEPTED', 'IN_PROGRESS'] } } }),
      prisma.job.count({ where: { status: 'PENDING' } }),
      prisma.payment.aggregate({ _sum: { amount: true } }),
      prisma.review.aggregate({ _avg: { rating: true } }),
      prisma.job.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: { issue: true },
      }),
    ]);

    return {
      totalUsers,
      activeJobs,
      pendingJobs,
      totalRevenue: payments._sum.amount || 0,
      averageRating: reviews._avg.rating || 0,
      recentIssues: recentJobs.map((job: { issue: string }) => job.issue),
    };
  }

  /**
   * Get technician context data
   */
  private async getTechnicianContext(userId: string) {
    const technician = await prisma.technician.findUnique({
      where: { userId },
      include: {
        jobs: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          include: { review: true },
        },
      },
    });

    if (!technician) {
      throw new Error('Technician not found');
    }

    const activeJobs = technician.jobs.filter((job: Job & { review?: Review | null }) =>
      ['ACCEPTED', 'IN_PROGRESS'].includes(job.status)
    ).length;

    const completedJobs = technician.jobs.filter((job: Job & { review?: Review | null }) =>
      job.status === 'COMPLETED'
    ).length;

    const recentReviews = technician.jobs
      .filter((job: Job & { review?: Review | null }) => job.review)
      .slice(0, 3)
      .map((job: Job & { review?: Review | null }) => `${job.review!.rating}/5: ${job.review!.comment || 'No comment'}`);

    return {
      specialization: technician.specialization,
      rating: technician.rating || 0,
      activeJobs,
      completedJobs,
      recentReviews,
    };
  }

  /**
   * Get homeowner context data
   */
  private async getHomeownerContext(userId: string) {
    const [jobs, payments] = await Promise.all([
      prisma.job.findMany({
        where: { userId },
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: { payment: true },
      }),
      prisma.payment.aggregate({
        where: { userId },
        _avg: { amount: true },
      }),
    ]);

    const recentJobs = jobs.slice(0, 5).map((job: Job) => job.issue);
    const preferredServices = [...new Set(jobs.map((job: Job) => job.issue.split(' ')[0]))].slice(0, 3);

    return {
      totalJobs: jobs.length,
      recentJobs,
      preferredServices,
      averageSpending: payments._avg.amount || 0,
    };
  }

  /**
   * Extract sources from context
   */
  private extractSources(context: string): string[] {
    // Simple implementation - could be enhanced
    return ['Platform Database', 'User History', 'System Analytics'];
  }

  /**
   * Generate role-based suggestions
   */
  private async generateSuggestions(role: string): Promise<string[]> {
    const suggestions = {
      ADMIN: [
        'View system analytics dashboard',
        'Check pending job approvals',
        'Review user feedback trends',
        'Monitor payment processing',
      ],
      TECHNICIAN: [
        'Check your upcoming jobs',
        'Update your availability status',
        'View customer feedback',
        'Optimize your service area',
      ],
      HOMEOWNER: [
        'Create a new service request',
        'Track your current jobs',
        'View payment history',
        'Rate completed services',
      ],
    };

    return suggestions[role as keyof typeof suggestions] || [];
  }

  /**
   * Save query history for analytics
   */
  private async saveQueryHistory(query: AIQuery, response: string): Promise<void> {
    try {
      // You could create an AIQuery model in Prisma for this
      logger.info(`AI Query saved for user ${query.userId}`);
    } catch (error) {
      logger.error('Failed to save query history:', error);
    }
  }

  /**
   * Get query templates for role
   */
  async getQueryTemplates(role: string): Promise<string[]> {
    const templates = {
      ADMIN: [
        'What are the current system performance metrics?',
        'Show me recent user feedback trends',
        'How can I improve technician efficiency?',
        'What are the most common service requests?',
      ],
      TECHNICIAN: [
        'How can I improve my customer ratings?',
        'What are the best practices for my specialization?',
        'How do I handle difficult customers?',
        'What tools do I need for common repairs?',
      ],
      HOMEOWNER: [
        'How do I create a service request?',
        'What should I expect during a service visit?',
        'How do payments work on the platform?',
        'How can I find the best technician for my needs?',
      ],
    };

    return templates[role as keyof typeof templates] || [];
  }

  /**
   * Get query history for a user
   */
  async getQueryHistory(userId: string, options: { limit: number; page: number }) {
    try {
      // For now, return empty history since we don't have a query history table
      // In a real implementation, you'd query from an AIQueryHistory table
      return {
        queries: [],
        pagination: {
          page: options.page,
          limit: options.limit,
          total: 0,
          totalPages: 0,
        },
      };
    } catch (error) {
      logger.error('Failed to get query history:', error);
      throw new Error('Failed to get query history');
    }
  }
}
