// src/models/user.model.ts
import { Role, User } from '@prisma/client';
import { prisma } from '../config/db';

export const UserModel = {
  createUser: async (data: {
    email: string;
    password: string;
    fullName: string;
    phone: string;
    role: Role;
  }): Promise<User> => {
    return prisma.user.create({ data });
  },

  findByEmail: async (email: string): Promise<User | null> => {
    return prisma.user.findUnique({ where: { email } });
  },

  findById: async (id: string): Promise<User | null> => {
    return prisma.user.findUnique({ where: { id } });
  },

  getAll: async (): Promise<User[]> => {
    return prisma.user.findMany();
  },

  updateUser: async (id: string, data: Partial<{ fullName: string; phone: string }>): Promise<User> => {
    return prisma.user.update({
      where: { id },
      data,
    });
  },
};
