# Hooks Directory

## Stripe Implementation

The Stripe implementation in this application uses a platform-specific approach to ensure compatibility across web and native platforms.

### Files Structure

- `useStripe.ts` - Entry point that dynamically resolves to the appropriate platform-specific implementation
- `useStripe.native.ts` - Native implementation using `@stripe/stripe-react-native`
- `useStripe.web.ts` - Web implementation using `@stripe/stripe-js`

### Usage

To use Stripe functionality in your components, import the `useStripe` hook from the hooks directory:

```typescript
import { useStripe } from '../hooks/useStripe';
// or use the higher-level service
import { useStripePayment } from '../services/stripe';
```

The hook will automatically use the correct implementation based on the platform.

### Error Handling

All Stripe operations include robust error handling to prevent app crashes. The implementation includes:

1. Fallback implementations if a platform-specific module fails to load
2. Consistent error reporting across platforms
3. Type safety with TypeScript interfaces

### Testing

When testing in development:

- Web implementation simulates successful payments
- Native implementation uses the actual Stripe SDK

### Extending

To add new Stripe functionality:

1. Add the method signature to the `StripeInterface` in `src/types/stripe.ts`
2. Implement the method in both `useStripe.native.ts` and `useStripe.web.ts`
3. Update the fallback implementation in `useStripe.ts`