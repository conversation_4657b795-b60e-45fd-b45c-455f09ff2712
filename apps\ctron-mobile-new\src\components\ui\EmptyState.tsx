// CTRON Home - Empty State Component
// Consistent empty state displays across the app

import React from 'react';
import { useTheme } from '../../context/ThemeContext';
import { Button } from './Button';
import { View, Text, StyleSheet } from '../../utils/platformUtils';

interface EmptyStateProps {
  title: string;
  message: string;
  icon?: string;
  actionTitle?: string;
  onAction?: () => void;
  style?: any;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  message,
  icon = '📋',
  actionTitle,
  onAction,
  style,
}) => {
  const { colors, spacing, typography } = useTheme();

  return (
    <View style={[styles.container, style]}>
      <Text style={[styles.icon, { fontSize: 48, marginBottom: spacing.lg }]}>
        {icon}
      </Text>
      
      <Text style={[styles.title, {
        fontSize: typography.fontSize.xl,
        fontWeight: typography.fontWeight.bold,
        color: colors.text.primary,
        marginBottom: spacing.sm,
      }]}>
        {title}
      </Text>
      
      <Text style={[styles.message, {
        fontSize: typography.fontSize.base,
        color: colors.text.secondary,
        marginBottom: spacing.lg,
      }]}>
        {message}
      </Text>
      
      {actionTitle && onAction && (
        <Button
          title={actionTitle}
          onPress={onAction}
          variant="primary"
          style={styles.actionButton}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  icon: {
    textAlign: 'center',
  },
  title: {
    textAlign: 'center',
  },
  message: {
    textAlign: 'center',
    lineHeight: 24,
  },
  actionButton: {
    minWidth: 120,
  },
});