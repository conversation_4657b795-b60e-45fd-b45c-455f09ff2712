// CTRON Home - Mock Data Detector
// Utility to detect and prevent hardcoded data in production

import Constants from 'expo-constants';

import { debugLogger } from './debugLogger';

/**
 * Common patterns that indicate hardcoded/mock data
 */

/**
 * Common patterns that indicate hardcoded/mock data
 */
const MOCK_DATA_PATTERNS = [
  // Common test/mock identifiers
  /test[-_]?user/i,
  /mock[-_]?data/i,
  /sample[-_]?data/i,
  /dummy[-_]?data/i,
  /fake[-_]?data/i,
  /placeholder/i,

  // Test emails and phones
  /@test\.com$/i,
  /@example\.com$/i,
  /^(\+?1)?[-.\s]?\(?555\)?[-.\s]?\d{3}[-.\s]?\d{4}$/,
  /^07\d{9}$/,

  // Common test names
  /john\s+smith/i,
  /jane\s+doe/i,
  /test\s+technician/i,
  /test\s+homeowner/i,

  // Test IDs
  /^test[-_]/i,
  /[-_]test$/i,
  /^mock[-_]/i,
  /[-_]mock$/i,
];

/**
 * Hardcoded URLs that should be environment variables
 */
const HARDCODED_URL_PATTERNS = [
  /https?:\/\/localhost/i,
  /https?:\/\/127\.0\.0\.1/i,
  /https?:\/\/192\.168\./i,
  /https?:\/\/10\./i,
  /api\.ctronhome\.com/i,
];

/**
 * Check if a value appears to be mock/test data
 */
export const isMockData = (value: unknown): boolean => {
  if (typeof value !== 'string') {
    return false;
  }

  return MOCK_DATA_PATTERNS.some(pattern => pattern.test(value));
};

/**
 * Check if a URL is hardcoded and should be an environment variable
 */
export const isHardcodedUrl = (url: string): boolean => {
  return HARDCODED_URL_PATTERNS.some(pattern => pattern.test(url));
};

/**
 * Validate an object for mock data
 */
export const validateObjectForMockData = (obj: unknown, objectName: string = 'object'): string[] => {
  const issues: string[] = [];

  const checkValue = (value: unknown, path: string) => {
    if (typeof value === 'string') {
      if (isMockData(value)) {
        issues.push(`Mock data detected at ${path}: "${value}"`);
      }
      if (isHardcodedUrl(value)) {
        issues.push(`Hardcoded URL detected at ${path}: "${value}"`);
      }
    } else if (Array.isArray(value)) {
      value.forEach((item, index) => {
        checkValue(item, `${path}[${index}]`);
      });
    } else if (value && typeof value === 'object') {
      Object.keys(value).forEach(key => {
        checkValue((value as any)[key], `${path}.${key}`);
      });
    }
  };

  checkValue(obj, objectName);
  return issues;
};

/**
 * Development-only function to check for mock data
 * Only runs in development mode
 */
export const checkForMockData = (data: unknown, context: string = 'data') => {
  if (!__DEV__) {
    return;
  }

  const issues = validateObjectForMockData(data, context);

  if (issues.length > 0) {
    debugLogger.warn(`Mock data detected in ${context}:`, issues);

    // In development, log warnings
    console.warn(`🚨 Mock data detected in ${context}:`);
    issues.forEach(issue => console.warn(`  - ${issue}`));
  }
};

/**
 * Validate environment configuration for production readiness
 */
export const validateEnvironmentConfig = (): { isValid: boolean; issues: string[] } => {
  const issues: string[] = [];

  // Check required environment variables
  const requiredEnvVars = [
    'EXPO_PUBLIC_API_BASE_URL',
    'EXPO_PUBLIC_SOCKET_URL',
    'EXPO_PUBLIC_STRIPE_PUBLIC_KEY',
  ];

  requiredEnvVars.forEach(envVar => {
    const value = (Constants.expoConfig?.extra as any)?.[envVar];
    if (!value) {
      issues.push(`Missing required environment variable: ${envVar}`);
    } else if (isHardcodedUrl(value as string)) {
      issues.push(`Environment variable ${envVar} contains hardcoded URL: ${value}`);
    } else if (isMockData(value)) {
      issues.push(`Environment variable ${envVar} contains mock data: ${value}`);
    }
  });

  return {
    isValid: issues.length === 0,
    issues,
  };
};

/**
 * Production safety check - throws error if mock data is detected in production
 */
export const ensureNoMockDataInProduction = (data: unknown, context: string = 'data') => {
  if (__DEV__) {
    return; // Only check in production
  }

  const issues = validateObjectForMockData(data, context);

  if (issues.length > 0) {
    const errorMessage = `Production safety check failed - mock data detected in ${context}:\n${issues.join('\n')}`;
    debugLogger.error(errorMessage);
    throw new Error(errorMessage);
  }
};
