// CTRON Home - Web Page Container Component
// Consistent page wrapper for web admin

import React from 'react';

interface PageContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '7xl' | 'full';
  padding?: boolean;
}

export const PageContainer: React.FC<PageContainerProps> = ({
  children,
  className = '',
  maxWidth = '7xl',
  padding = true,
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full',
  };

  const paddingClass = padding ? 'p-6' : '';

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 ${paddingClass}`}>
      <div className={`${maxWidthClasses[maxWidth]} mx-auto space-y-8 ${className}`}>
        {children}
      </div>
    </div>
  );
};

export default PageContainer;