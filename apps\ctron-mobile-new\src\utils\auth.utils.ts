// CTRON Home - Authentication Utilities
// Helper functions for authentication token management

import AsyncStorage from './asyncStorage';
import * as SecureStore from './secureStore';


// Storage keys (using SecureStore for consistency with AuthContext)
const AUTH_TOKEN_KEY = 'token'; // Same key as used in AuthContext
const USER_DATA_KEY = '@ctron_user_data';

/**
 * Get the stored authentication token
 */
export const getAuthToken = async (): Promise<string | null> => {
  try {
    const token = await SecureStore.getItemAsync(AUTH_TOKEN_KEY);
    return token;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

/**
 * Store the authentication token
 */
export const setAuthToken = async (token: string | null): Promise<void> => {
  try {
    if (token) {
      await SecureStore.setItemAsync(AUTH_TOKEN_KEY, token);
    } else {
      await SecureStore.deleteItemAsync(AUTH_TOKEN_KEY);
    }
  } catch (error) {
    console.error('Error setting auth token:', error);
    throw error;
  }
};

/**
 * Remove the authentication token
 */
export const removeAuthToken = async (): Promise<void> => {
  try {
    await SecureStore.deleteItemAsync(AUTH_TOKEN_KEY);
  } catch (error) {
    console.error('Error removing auth token:', error);
    throw error;
  }
};

/**
 * Get stored user data
 */
export const getUserData = async (): Promise<unknown | null> => {
  try {
    const userData = await AsyncStorage.getItem(USER_DATA_KEY);
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error getting user data:', error);
    return null;
  }
};

/**
 * Store user data
 */
export const setUserData = async (userData: unknown): Promise<void> => {
  try {
    await AsyncStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));
  } catch (error) {
    console.error('Error setting user data:', error);
    throw error;
  }
};

/**
 * Remove user data
 */
export const removeUserData = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(USER_DATA_KEY);
  } catch (error) {
    if (__DEV__) {
      console.error('Error removing user data:', error);
    }
    throw error;
  }
};

/**
 * Clear all authentication data
 */
export const clearAuthData = async (): Promise<void> => {
  try {
    await Promise.all([
      removeAuthToken(),
      removeUserData(),
    ]);
  } catch (error) {
    if (__DEV__) {
      console.error('Error clearing auth data:', error);
    }
    throw error;
  }
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    const token = await getAuthToken();
    return !!token;
  } catch (error) {
    if (__DEV__) {
      console.error('Error checking authentication:', error);
    }
    return false;
  }
};
