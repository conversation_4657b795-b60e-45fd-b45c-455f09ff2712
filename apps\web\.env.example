# ========================================
# CTRON WEB ADMIN - ENVIRONMENT VARIABLES
# ========================================

# REQUIRED VARIABLES (App will fail to start without these)
# ==========================================================

# Backend API Configuration
VITE_API_BASE_URL=http://localhost:3001
VITE_API_URL=http://localhost:3001/api
VITE_SOCKET_URL=http://localhost:3001

# OPTIONAL VARIABLES (App will work without these but features may be limited)
# =============================================================================

# Stripe Configuration (for payment management)
VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_key_here

# App Configuration
VITE_APP_ENVIRONMENT=development

# Development Configuration
NODE_ENV=development
