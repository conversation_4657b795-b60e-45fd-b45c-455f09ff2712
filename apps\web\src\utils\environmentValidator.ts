// src/utils/environmentValidator.ts

interface EnvironmentConfig {
  VITE_API_URL?: string;
  VITE_API_BASE_URL?: string;
  VITE_SOCKET_URL?: string;
  VITE_STRIPE_PUBLIC_KEY?: string;
  VITE_APP_NAME?: string;
  VITE_APP_VERSION?: string;
  VITE_APP_ENVIRONMENT?: string;
  VITE_GOOGLE_MAPS_API_KEY?: string;
  VITE_ANALYTICS_ID?: string;
  VITE_ENABLE_ANALYTICS?: string;
  VITE_ENABLE_DEBUG?: string;
  MODE?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validates that all required environment variables are present and properly formatted
 */
export const validateEnvironmentConfig = (): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const config: EnvironmentConfig = {
    VITE_API_URL: import.meta.env.VITE_API_URL,
    VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
    VITE_SOCKET_URL: import.meta.env.VITE_SOCKET_URL,
    VITE_STRIPE_PUBLIC_KEY: import.meta.env.VITE_STRIPE_PUBLIC_KEY,
    VITE_APP_NAME: import.meta.env.VITE_APP_NAME,
    VITE_APP_VERSION: import.meta.env.VITE_APP_VERSION,
    VITE_APP_ENVIRONMENT: import.meta.env.VITE_APP_ENVIRONMENT,
    VITE_GOOGLE_MAPS_API_KEY: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
    VITE_ANALYTICS_ID: import.meta.env.VITE_ANALYTICS_ID,
    VITE_ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS,
    VITE_ENABLE_DEBUG: import.meta.env.VITE_ENABLE_DEBUG,
    MODE: import.meta.env.MODE,
  };

  // Required environment variables
  const requiredVars = [
    'VITE_API_URL',
    'VITE_API_BASE_URL',
    'VITE_SOCKET_URL',
  ];

  // Check for missing required variables
  requiredVars.forEach(varName => {
    const value = config[varName as keyof EnvironmentConfig];
    if (!value || value.trim() === '') {
      errors.push(`Missing required environment variable: ${varName}`);
    }
  });

  // Validate URL formats
  const urlVars = [
    'VITE_API_URL',
    'VITE_API_BASE_URL',
    'VITE_SOCKET_URL',
  ];

  urlVars.forEach(varName => {
    const value = config[varName as keyof EnvironmentConfig];
    if (value && !isValidUrl(value)) {
      errors.push(`Invalid URL format for ${varName}: ${value}`);
    }
  });

  // Check for development-specific issues
  if (config.MODE === 'development') {
    // Check for localhost URLs that might not work in production
    urlVars.forEach(varName => {
      const value = config[varName as keyof EnvironmentConfig];
      if (value && value.includes('localhost')) {
        warnings.push(
          `${varName} uses localhost - ensure production URLs are configured for production builds`
        );
      }
    });
  }

  // Check for Stripe configuration
  if (!config.VITE_STRIPE_PUBLIC_KEY) {
    warnings.push('VITE_STRIPE_PUBLIC_KEY is not set - payment functionality will not work');
  } else if (config.VITE_STRIPE_PUBLIC_KEY.includes('your_stripe_public_key_here')) {
    warnings.push('VITE_STRIPE_PUBLIC_KEY appears to be a placeholder - update with real Stripe key');
  }

  // Check for consistent base URLs
  if (config.VITE_API_URL && config.VITE_API_BASE_URL) {
    const apiUrl = config.VITE_API_URL.replace('/api', '');
    if (apiUrl !== config.VITE_API_BASE_URL) {
      warnings.push('VITE_API_URL and VITE_API_BASE_URL appear to be inconsistent');
    }
  }

  // Check for production-specific requirements
  if (config.MODE === 'production') {
    if (config.VITE_ENABLE_DEBUG === 'true') {
      warnings.push('Debug mode is enabled in production - consider disabling for performance');
    }

    // Mock data configuration removed - no longer needed

    if (!config.VITE_ANALYTICS_ID && config.VITE_ENABLE_ANALYTICS === 'true') {
      warnings.push('Analytics is enabled but VITE_ANALYTICS_ID is not configured');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Validates a URL string
 */
const isValidUrl = (urlString: string): boolean => {
  try {
    const url = new URL(urlString);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch {
    return false;
  }
};

/**
 * Displays environment validation results to the console
 */
export const displayValidationResults = (result: ValidationResult): void => {
  if (!result.isValid) {
    console.error('🚨 Environment Configuration Errors:');
    result.errors.forEach(error => console.error(`  - ${error}`));
    
    if (import.meta.env.MODE === 'production') {
      console.error('❌ Application cannot start with configuration errors in production');
      // In a web app, we can't exit the process, but we can show an error to the user
    }
  }

  if (result.warnings.length > 0) {
    console.warn('⚠️ Environment Configuration Warnings:');
    result.warnings.forEach(warning => console.warn(`  - ${warning}`));
  }

  // Validation completed
};

/**
 * Performs environment validation and displays results
 * Should be called during application initialization
 */
export const validateAndDisplayEnvironment = (): boolean => {
  const result = validateEnvironmentConfig();
  
  displayValidationResults(result);
  
  return result.isValid;
};

/**
 * Gets environment configuration with fallbacks and type safety
 */
export const getEnvironmentConfig = () => {
  const isDevelopment = import.meta.env.MODE === 'development';

  const config = {
    apiUrl: import.meta.env.VITE_API_URL || (isDevelopment ? 'http://localhost:3001/api' : ''),
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || (isDevelopment ? 'http://localhost:3001' : ''),
    socketUrl: import.meta.env.VITE_SOCKET_URL || (isDevelopment ? 'http://localhost:3001' : ''),
    stripePublicKey: import.meta.env.VITE_STRIPE_PUBLIC_KEY || '',
    appName: import.meta.env.VITE_APP_NAME || 'CTRON Home',
    appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0',
    appEnvironment: import.meta.env.VITE_APP_ENVIRONMENT || (isDevelopment ? 'development' : 'production'),
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '',
    analyticsId: import.meta.env.VITE_ANALYTICS_ID || '',
    enableAnalytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
    enableDebug: import.meta.env.VITE_ENABLE_DEBUG === 'true',
    mode: import.meta.env.MODE || 'development',
    isDevelopment: import.meta.env.MODE === 'development',
    isProduction: import.meta.env.MODE === 'production',
    isStaging: import.meta.env.MODE === 'staging',
  };

  if (config.enableDebug && import.meta.env.DEV) {
    console.log('🔧 Environment Configuration:', {
      ...config,
      stripePublicKey: config.stripePublicKey ? '[SET]' : '[NOT SET]',
      googleMapsApiKey: config.googleMapsApiKey ? '[SET]' : '[NOT SET]',
    });
  }

  return config;
};

export default {
  validateEnvironmentConfig,
  validateAndDisplayEnvironment,
  displayValidationResults,
  getEnvironmentConfig,
};
