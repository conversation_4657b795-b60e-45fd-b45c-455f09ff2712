// src/services/linking.native.ts
// Native platform linking service using expo-linking

import * as Linking from 'expo-linking';

export default {
    // Open URL in native browser or app
    openURL: (url: string) => Linking.openURL(url),

    // Open settings
    openSettings: () => Linking.openSettings(),

    // Check if URL can be opened
    canOpenURL: (url: string) => Linking.canOpenURL(url),

    // Get initial URL (for deep linking)
    getInitialURL: () => Linking.getInitialURL(),

    // Add URL listener for deep linking (returns subscription)
    addEventListener: (listener: (event: { url: string }) => void) =>
        Linking.addEventListener('url', listener),
}; 