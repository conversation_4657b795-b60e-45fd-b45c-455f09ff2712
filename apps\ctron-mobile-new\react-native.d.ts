// This file extends React Native types for custom components or overrides

import 'react-native';

// Add any custom type extensions here
declare module 'react-native' {
  // You can extend existing interfaces
  interface ViewStyle {
    // Add any custom ViewStyle properties if needed
  }

  interface TextStyle {
    // Add any custom TextStyle properties if needed
  }

  interface ImageStyle {
    // Add any custom ImageStyle properties if needed
  }
}

// Add global type definitions
declare global {
  // Add any global types here
  namespace NodeJS {
    interface ProcessEnv {
      // Define environment variables types
      NODE_ENV: 'development' | 'production' | 'test';
      EXPO_PUBLIC_API_BASE_URL: string;
      EXPO_PUBLIC_API_URL: string;
      EXPO_PUBLIC_SOCKET_URL: string;
      EXPO_PUBLIC_STRIPE_PUBLIC_KEY: string;
      EXPO_PUBLIC_AWS_REGION: string;
      EXPO_PUBLIC_AWS_BUCKET_NAME: string;
      EXPO_PUBLIC_S3_BUCKET: string;
      EXPO_PUBLIC_DEV_SERVER_IP: string;
      EXPO_PUBLIC_DEV_SERVER_PORT: string;
      EXPO_PUBLIC_MERCHANT_IDENTIFIER: string;
      EXPO_PUBLIC_API_TIMEOUT: string;
      EXPO_PUBLIC_SOCKET_TIMEOUT: string;
      EXPO_PUBLIC_SOCKET_RECONNECT_ATTEMPTS: string;
      EXPO_PUBLIC_SOCKET_RECONNECT_DELAY: string;
      EXPO_PUBLIC_SOCKET_RECONNECT_DELAY_MAX: string;
      EXPO_PUBLIC_ENABLE_DEBUG: string;
      EXPO_PUBLIC_PROJECT_ID: string;
      EXPO_PUBLIC_APP_NAME: string;
    }
  }
}

// Declare modules for files that don't have type definitions
declare module '*.svg' {
  import { SvgProps } from 'react-native-svg';
  const content: React.FC<SvgProps>;
  export default content;
}

declare module '*.png' {
  const content: any;
  export default content;
}

declare module '*.jpg' {
  const content: any;
  export default content;
}

declare module '*.json' {
  const content: any;
  export default content;
}