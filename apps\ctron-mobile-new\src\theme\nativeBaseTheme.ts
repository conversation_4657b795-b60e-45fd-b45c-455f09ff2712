// CTRON Home - NativeBase Theme Configuration
// Professional service industry theme with CTRON branding

import { extendTheme } from 'native-base';

// CTRON Brand Colors
const ctronColors = {
  primary: {
    50: '#E3F2FD',
    100: '#BBDEFB',
    200: '#90CAF9',
    300: '#64B5F6',
    400: '#42A5F5',
    500: '#2196F3', // Main CTRON Blue
    600: '#1E88E5',
    700: '#1976D2',
    800: '#1565C0',
    900: '#0D47A1',
  },
  secondary: {
    50: '#FFF3E0',
    100: '#FFE0B2',
    200: '#FFCC80',
    300: '#FFB74D',
    400: '#FFA726',
    500: '#FF9800', // CTRON Orange
    600: '#FB8C00',
    700: '#F57C00',
    800: '#EF6C00',
    900: '#E65100',
  },
  success: {
    50: '#E8F5E8',
    100: '#C8E6C9',
    200: '#A5D6A7',
    300: '#81C784',
    400: '#66BB6A',
    500: '#4CAF50', // Success Green
    600: '#43A047',
    700: '#388E3C',
    800: '#2E7D32',
    900: '#1B5E20',
  },
  warning: {
    50: '#FFF8E1',
    100: '#FFECB3',
    200: '#FFE082',
    300: '#FFD54F',
    400: '#FFCA28',
    500: '#FFC107', // Warning Amber
    600: '#FFB300',
    700: '#FFA000',
    800: '#FF8F00',
    900: '#FF6F00',
  },
  error: {
    50: '#FFEBEE',
    100: '#FFCDD2',
    200: '#EF9A9A',
    300: '#E57373',
    400: '#EF5350',
    500: '#F44336', // Error Red
    600: '#E53935',
    700: '#D32F2F',
    800: '#C62828',
    900: '#B71C1C',
  },
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  // Service industry specific colors
  service: {
    plumbing: '#2196F3',
    electrical: '#FF9800',
    hvac: '#4CAF50',
    cleaning: '#9C27B0',
    handyman: '#795548',
    emergency: '#F44336',
  },
  // Status colors for jobs
  status: {
    pending: '#FF9800',
    assigned: '#2196F3',
    inProgress: '#4CAF50',
    completed: '#8BC34A',
    cancelled: '#F44336',
    onHold: '#9E9E9E',
  },
};

// Typography configuration
const typography = {
  letterSpacings: {
    xs: '-0.05em',
    sm: '-0.025em',
    md: '0',
    lg: '0.025em',
    xl: '0.05em',
    '2xl': '0.1em',
  },
  lineHeights: {
    '2xs': '1em',
    xs: '1.125em',
    sm: '1.25em',
    md: '1.375em',
    lg: '1.5em',
    xl: '1.75em',
    '2xl': '2em',
  },
  fontWeights: {
    hairline: 100,
    thin: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },
  fonts: {
    heading: 'System',
    body: 'System',
    mono: 'Courier',
  },
  fontSizes: {
    '2xs': 10,
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
    '6xl': 60,
    '7xl': 72,
    '8xl': 96,
    '9xl': 128,
  },
};

// Component customizations
const components = {
  Button: {
    baseStyle: {
      borderRadius: 'lg',
      _text: {
        fontWeight: 'semibold',
      },
    },
    variants: {
      solid: (props: any) => ({
        bg: `${props.colorScheme}.500`,
        _pressed: {
          bg: `${props.colorScheme}.600`,
        },
        _hover: {
          bg: `${props.colorScheme}.600`,
        },
      }),
      outline: (props: any) => ({
        borderColor: `${props.colorScheme}.500`,
        borderWidth: 2,
        _text: {
          color: `${props.colorScheme}.500`,
        },
        _pressed: {
          bg: `${props.colorScheme}.50`,
        },
      }),
      ghost: (props: any) => ({
        _text: {
          color: `${props.colorScheme}.500`,
        },
        _pressed: {
          bg: `${props.colorScheme}.50`,
        },
      }),
      // Service industry specific variants
      service: {
        bg: 'primary.500',
        borderRadius: 'xl',
        shadow: 2,
        _text: {
          color: 'white',
          fontWeight: 'bold',
        },
        _pressed: {
          bg: 'primary.600',
          shadow: 4,
        },
      },
      emergency: {
        bg: 'error.500',
        borderRadius: 'xl',
        shadow: 3,
        _text: {
          color: 'white',
          fontWeight: 'bold',
        },
        _pressed: {
          bg: 'error.600',
          shadow: 5,
        },
      },
    },
    sizes: {
      xs: {
        px: 2,
        py: 1,
        _text: {
          fontSize: 'xs',
        },
      },
      sm: {
        px: 3,
        py: 2,
        _text: {
          fontSize: 'sm',
        },
      },
      md: {
        px: 4,
        py: 3,
        _text: {
          fontSize: 'md',
        },
      },
      lg: {
        px: 6,
        py: 4,
        _text: {
          fontSize: 'lg',
        },
      },
    },
  },
  Card: {
    baseStyle: {
      bg: 'white',
      borderRadius: 'xl',
      shadow: 2,
      borderWidth: 1,
      borderColor: 'gray.100',
    },
    variants: {
      elevated: {
        shadow: 4,
        bg: 'white',
      },
      outline: {
        borderWidth: 2,
        borderColor: 'gray.200',
        shadow: 0,
      },
      // Service specific card variants
      service: {
        bg: 'white',
        borderRadius: 'xl',
        shadow: 3,
        borderLeftWidth: 4,
        borderLeftColor: 'primary.500',
      },
      technician: {
        bg: 'white',
        borderRadius: 'xl',
        shadow: 2,
        borderWidth: 1,
        borderColor: 'gray.100',
        _hover: {
          shadow: 4,
          borderColor: 'primary.200',
        },
      },
      job: {
        bg: 'white',
        borderRadius: 'lg',
        shadow: 2,
        borderWidth: 1,
        borderColor: 'gray.100',
        mb: 3,
      },
    },
  },
  Input: {
    baseStyle: {
      borderRadius: 'lg',
      borderWidth: 2,
      borderColor: 'gray.200',
      bg: 'white',
      _focus: {
        borderColor: 'primary.500',
        bg: 'white',
      },
      _invalid: {
        borderColor: 'error.500',
      },
    },
    variants: {
      outline: {
        borderWidth: 2,
        borderColor: 'gray.200',
        _focus: {
          borderColor: 'primary.500',
        },
      },
      filled: {
        bg: 'gray.50',
        borderWidth: 0,
        _focus: {
          bg: 'white',
          borderWidth: 2,
          borderColor: 'primary.500',
        },
      },
    },
    sizes: {
      sm: {
        fontSize: 'sm',
        px: 3,
        py: 2,
      },
      md: {
        fontSize: 'md',
        px: 4,
        py: 3,
      },
      lg: {
        fontSize: 'lg',
        px: 5,
        py: 4,
      },
    },
  },
  Badge: {
    baseStyle: {
      borderRadius: 'full',
      px: 2,
      py: 1,
      _text: {
        fontSize: 'xs',
        fontWeight: 'bold',
      },
    },
    variants: {
      solid: (props: any) => ({
        bg: `${props.colorScheme}.500`,
        _text: {
          color: 'white',
        },
      }),
      outline: (props: any) => ({
        borderWidth: 1,
        borderColor: `${props.colorScheme}.500`,
        _text: {
          color: `${props.colorScheme}.500`,
        },
      }),
      // Status specific badges
      status: (props: any) => {
        const statusColors = {
          pending: 'warning',
          assigned: 'info',
          inProgress: 'success',
          completed: 'success',
          cancelled: 'error',
        };
        const colorScheme = statusColors[props.status] || 'gray';
        return {
          bg: `${colorScheme}.500`,
          _text: {
            color: 'white',
            textTransform: 'capitalize',
          },
        };
      },
    },
  },
  Avatar: {
    baseStyle: {
      borderWidth: 2,
      borderColor: 'white',
    },
    sizes: {
      xs: {
        width: 6,
        height: 6,
      },
      sm: {
        width: 8,
        height: 8,
      },
      md: {
        width: 12,
        height: 12,
      },
      lg: {
        width: 16,
        height: 16,
      },
      xl: {
        width: 20,
        height: 20,
      },
      '2xl': {
        width: 24,
        height: 24,
      },
    },
  },
  Progress: {
    baseStyle: {
      borderRadius: 'full',
      bg: 'gray.200',
      _filledTrack: {
        bg: 'primary.500',
      },
    },
    variants: {
      service: {
        bg: 'gray.100',
        _filledTrack: {
          bg: 'primary.500',
        },
      },
    },
  },
  Modal: {
    baseStyle: {
      _backdrop: {
        bg: 'rgba(0, 0, 0, 0.5)',
      },
    },
  },
  ActionSheet: {
    baseStyle: {
      _backdrop: {
        bg: 'rgba(0, 0, 0, 0.5)',
      },
    },
  },
};

// Main theme configuration
export const ctronTheme = extendTheme({
  colors: ctronColors,
  ...typography,
  components,
  config: {
    useSystemColorMode: false,
    initialColorMode: 'light',
  },
  space: {
    px: '1px',
    0: 0,
    0.5: 2,
    1: 4,
    1.5: 6,
    2: 8,
    2.5: 10,
    3: 12,
    3.5: 14,
    4: 16,
    5: 20,
    6: 24,
    7: 28,
    8: 32,
    9: 36,
    10: 40,
    12: 48,
    16: 64,
    20: 80,
    24: 96,
    32: 128,
    40: 160,
    48: 192,
    56: 224,
    64: 256,
    72: 288,
    80: 320,
    96: 384,
  },
  sizes: {
    0: 0,
    px: '1px',
    0.5: 2,
    1: 4,
    1.5: 6,
    2: 8,
    2.5: 10,
    3: 12,
    3.5: 14,
    4: 16,
    5: 20,
    6: 24,
    7: 28,
    8: 32,
    9: 36,
    10: 40,
    12: 48,
    16: 64,
    20: 80,
    24: 96,
    32: 128,
    40: 160,
    48: 192,
    56: 224,
    64: 256,
    72: 288,
    80: 320,
    96: 384,
    full: '100%',
    '3xs': 224,
    '2xs': 256,
    xs: 320,
    sm: 384,
    md: 448,
    lg: 512,
    xl: 576,
    '2xl': 672,
    '3xl': 768,
    '4xl': 896,
    '5xl': 1024,
    '6xl': 1152,
    '7xl': 1280,
    '8xl': 1408,
    container: {
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
    },
  },
  radii: {
    none: 0,
    xs: 2,
    sm: 4,
    md: 6,
    lg: 8,
    xl: 12,
    '2xl': 16,
    '3xl': 24,
    full: 9999,
  },
  shadows: {
    0: 'none',
    1: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.05,
      shadowRadius: 1,
      elevation: 1,
    },
    2: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    3: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 4,
    },
    4: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 8,
    },
    5: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      elevation: 16,
    },
  },
});

export default ctronTheme;