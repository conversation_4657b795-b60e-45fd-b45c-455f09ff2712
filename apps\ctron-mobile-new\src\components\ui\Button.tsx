// CTRON Home Design System - Button Component
// Professional, accessible button implementation

import React from 'react';

import { ViewStyle, TextStyle } from 'react-native';

import { useTheme } from '../../context/ThemeContext';
import { useAccessibility } from '../../hooks/useAccessibility';
// Theme types removed - not used in this component
import { TouchableOpacity, Text, ActivityIndicator, StyleSheet, View } from '../../utils/platformUtils';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'emergency' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  testID?: string;
  // Enhanced accessibility props
  accessibilityLabel?: string;
  accessibilityHint?: string;
  announceOnPress?: string;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  testID,
  accessibilityLabel,
  accessibilityHint,
  announceOnPress,
}) => {
  const { colors, spacing, typography, borderRadius, shadows, sizes } = useTheme();
  const { getButtonAccessibilityProps, announceText, shouldAnimate } = useAccessibility();
  const styles = getStyles(colors, spacing, typography, borderRadius, shadows, sizes);
  const buttonStyles = [
    styles.base,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    disabled && styles.disabledText,
    textStyle,
  ];

  const handlePress = () => {
    if (!disabled && !loading) {
      onPress();

      // Announce action if specified
      if (announceOnPress) {
        announceText(announceOnPress, 'medium');
      }
    }
  };

  // Generate comprehensive accessibility props
  const accessibilityProps = getButtonAccessibilityProps({
    label: accessibilityLabel || title,
    hint: accessibilityHint,
    disabled,
    loading,
    testID,
  });

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="small"
            color={variant === 'primary' || variant === 'emergency' ? colors.white : colors.primary.main}
          />
          <Text style={[textStyles, styles.loadingText]}>Loading...</Text>
        </View>
      );
    }

    if (icon) {
      return (
        <View style={styles.contentContainer}>
          {iconPosition === 'left' && <View style={styles.iconLeft}>{icon}</View>}
          <Text style={textStyles}>{title}</Text>
          {iconPosition === 'right' && <View style={styles.iconRight}>{icon}</View>}
        </View>
      );
    }

    return <Text style={textStyles}>{title}</Text>;
  };

  return (
    <TouchableOpacity
      style={buttonStyles}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={shouldAnimate() ? 0.8 : 1}
      {...accessibilityProps}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const getStyles = (colors: any, spacing: any, typography: any, borderRadius: any, shadows: any, sizes: any) => StyleSheet.create({
  base: {
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    minHeight: sizes.touchTarget,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },

  // Variants
  primary: {
    backgroundColor: colors.primary.main,
    ...shadows.md,
  },

  secondary: {
    backgroundColor: colors.secondarySystemBackground,
    borderWidth: 1,
    borderColor: colors.separator,
    ...shadows.sm,
  },

  emergency: {
    backgroundColor: colors.destructive,
    ...shadows.md,
  },

  ghost: {
    backgroundColor: 'transparent',
  },

  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.separator,
  },

  // Sizes
  sm: {
    minHeight: sizes.buttonHeight.sm,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
  },

  md: {
    minHeight: sizes.buttonHeight.md,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },

  lg: {
    minHeight: sizes.buttonHeight.lg,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
  },

  // Text Styles
  text: {
    ...typography.callout,
    textAlign: 'center',
  },

  primaryText: {
    color: colors.white,
  },

  secondaryText: {
    color: colors.primary.main,
  },

  emergencyText: {
    color: colors.white,
    fontWeight: '700',
  },

  ghostText: {
    color: colors.primary.main,
  },

  outlineText: {
    color: colors.label,
  },

  // Size Text Styles
  smText: {
    ...typography.footnote,
  },

  mdText: {
    ...typography.callout,
  },

  lgText: {
    ...typography.headline,
  },

  // States
  disabled: {
    backgroundColor: colors.gray300,
    borderColor: colors.gray300,
  },

  disabledText: {
    color: colors.gray500,
  },

  fullWidth: {
    width: '100%',
  },

  // Content Layout
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  iconLeft: {
    marginRight: spacing.xs,
  },

  iconRight: {
    marginLeft: spacing.xs,
  },

  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  loadingText: {
    marginLeft: spacing.xs,
  },
});

export default Button;

