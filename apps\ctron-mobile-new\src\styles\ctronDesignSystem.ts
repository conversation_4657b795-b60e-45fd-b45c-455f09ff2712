// CTRON Design System - Based on Reference UI
// Professional, clean, minimal aesthetic with CTRON branding

import { StyleSheet } from '../utils/platformUtils';

// CTRON Brand Colors - Dark Theme Based on Reference Images
export const ctronColors = {
  // Primary brand color
  primary: '#9273EC',
  primaryLight: '#A688F0',
  primaryDark: '#7B5CE6',
  
  // Background colors - Dark theme
  background: '#1A1B2E', // Dark navy background
  backgroundSecondary: '#16213E', // Slightly lighter navy
  backgroundCard: '#0F1419', // Card background
  
  // Accent colors
  accent: '#FF4E4E', // Warning/links color
  accentLight: '#FF6B6B',
  
  // Text colors - Inverted for dark theme
  textPrimary: '#FFFFFF',
  textSecondary: '#B0B3B8',
  textTertiary: '#8A8D93',
  textLight: '#FFFFFF',
  
  // Input colors - Dark theme
  inputBackground: '#16213E',
  inputBorder: '#2A3441',
  inputBorderFocus: '#9273EC',
  inputPlaceholder: '#8A8D93',
  
  // Status colors
  success: '#4CAF50',
  error: '#FF4E4E',
  warning: '#FF9800',
  info: '#2196F3',
  
  // Neutral colors
  gray50: '#FAFAFA',
  gray100: '#F5F5F5',
  gray200: '#EEEEEE',
  gray300: '#E0E0E0',
  gray400: '#BDBDBD',
  gray500: '#9E9E9E',
  gray600: '#757575',
  gray700: '#616161',
  gray800: '#424242',
  gray900: '#212121',
};

// Typography
export const ctronTypography = {
  // Font families
  fontFamily: {
    regular: 'System',
    medium: 'System',
    semiBold: 'System',
    bold: 'System',
  },
  
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  
  // Line heights
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
  
  // Font weights
  fontWeight: {
    normal: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
  },
};

// Spacing - Reduced for compact design
export const ctronSpacing = {
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  '3xl': 32,
};

// Border radius
export const ctronBorderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

// Shadows
export const ctronShadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
};

// Component styles based on reference image
export const ctronStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
    backgroundColor: ctronColors.background,
  },
  
  safeContainer: {
    flex: 1,
    backgroundColor: ctronColors.background,
    paddingHorizontal: ctronSpacing.lg,
  },
  
  // Header styles - Reduced spacing
  header: {
    alignItems: 'center',
    paddingTop: ctronSpacing.lg,
    paddingBottom: ctronSpacing.md,
  },
  
  logo: {
    width: 60,
    height: 60,
    borderRadius: ctronBorderRadius.lg,
    backgroundColor: ctronColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: ctronSpacing.md,
  },
  
  logoText: {
    fontSize: ctronTypography.fontSize['2xl'],
    fontWeight: ctronTypography.fontWeight.bold,
    color: ctronColors.textLight,
  },
  
  brandName: {
    fontSize: ctronTypography.fontSize['3xl'],
    fontWeight: ctronTypography.fontWeight.bold,
    color: ctronColors.textPrimary,
    letterSpacing: 2,
    marginBottom: ctronSpacing.xs,
  },
  
  welcomeTitle: {
    fontSize: ctronTypography.fontSize.xl,
    fontWeight: ctronTypography.fontWeight.semiBold,
    color: ctronColors.textPrimary,
    textAlign: 'center',
    marginBottom: ctronSpacing.sm,
  },
  
  welcomeSubtitle: {
    fontSize: ctronTypography.fontSize.base,
    color: ctronColors.textSecondary,
    textAlign: 'center',
    lineHeight: ctronTypography.lineHeight.relaxed,
    marginBottom: ctronSpacing.sm,
  },
  
  tagline: {
    fontSize: ctronTypography.fontSize.sm,
    fontWeight: ctronTypography.fontWeight.medium,
    color: ctronColors.primary,
    textAlign: 'center',
    letterSpacing: 1,
  },
  
  // Form styles - Reduced spacing
  formContainer: {
    flex: 1,
    paddingTop: ctronSpacing.sm,
  },
  
  inputGroup: {
    marginBottom: ctronSpacing.md,
  },
  
  inputLabel: {
    fontSize: ctronTypography.fontSize.sm,
    fontWeight: ctronTypography.fontWeight.medium,
    color: ctronColors.textPrimary,
    marginBottom: ctronSpacing.sm,
  },
  
  input: {
    height: 56,
    backgroundColor: ctronColors.inputBackground,
    borderWidth: 1,
    borderColor: ctronColors.inputBorder,
    borderRadius: ctronBorderRadius.lg,
    paddingHorizontal: ctronSpacing.md,
    fontSize: ctronTypography.fontSize.base,
    color: ctronColors.textPrimary,
    ...ctronShadows.sm,
  },
  
  inputFocused: {
    borderColor: ctronColors.inputBorderFocus,
    ...ctronShadows.md,
  },
  
  inputError: {
    borderColor: ctronColors.error,
  },
  
  errorText: {
    fontSize: ctronTypography.fontSize.sm,
    color: ctronColors.error,
    marginTop: ctronSpacing.xs,
  },
  
  // Button styles - Reduced spacing
  primaryButton: {
    height: 56,
    backgroundColor: ctronColors.primary,
    borderRadius: ctronBorderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: ctronSpacing.sm,
    ...ctronShadows.md,
  },
  
  primaryButtonText: {
    fontSize: ctronTypography.fontSize.base,
    fontWeight: ctronTypography.fontWeight.semiBold,
    color: ctronColors.textLight,
  },
  
  secondaryButton: {
    height: 56,
    backgroundColor: ctronColors.backgroundSecondary,
    borderWidth: 1,
    borderColor: ctronColors.inputBorder,
    borderRadius: ctronBorderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: ctronSpacing.xs,
  },
  
  secondaryButtonText: {
    fontSize: ctronTypography.fontSize.base,
    fontWeight: ctronTypography.fontWeight.medium,
    color: ctronColors.textPrimary,
  },
  
  linkButton: {
    alignItems: 'center',
    paddingVertical: ctronSpacing.sm,
  },
  
  linkButtonText: {
    fontSize: ctronTypography.fontSize.sm,
    color: ctronColors.accent,
    fontWeight: ctronTypography.fontWeight.medium,
  },
  
  // Utility styles
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  spaceBetween: {
    justifyContent: 'space-between',
  },
  
  textCenter: {
    textAlign: 'center',
  },
  
  // Loading styles
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },

  // Signup Screen specific styles
  keyboardContainer: {
    flex: 1,
  },
  
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: ctronSpacing.lg,
    paddingVertical: ctronSpacing.md,
  },
  
  headerContainer: {
    alignItems: 'center',
    paddingVertical: ctronSpacing.lg,
  },
  
  logoContainer: {
    alignItems: 'center',
    marginBottom: ctronSpacing.md,
  },
  
  brandTagline: {
    fontSize: ctronTypography.fontSize.sm,
    fontWeight: ctronTypography.fontWeight.medium,
    color: ctronColors.primary,
    textAlign: 'center',
    letterSpacing: 1,
    marginBottom: ctronSpacing.lg,
  },
  
  roleSection: {
    marginBottom: ctronSpacing.lg,
  },
  
  sectionTitle: {
    fontSize: ctronTypography.fontSize.base,
    fontWeight: ctronTypography.fontWeight.semiBold,
    color: ctronColors.textPrimary,
    marginBottom: ctronSpacing.md,
  },
  
  roleContainer: {
    flexDirection: 'row',
    backgroundColor: ctronColors.backgroundSecondary,
    borderRadius: ctronBorderRadius.lg,
    overflow: 'hidden',
  },
  
  roleButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: ctronSpacing.md,
    paddingHorizontal: ctronSpacing.sm,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  
  roleButtonActive: {
    backgroundColor: ctronColors.primary,
    borderColor: ctronColors.primary,
  },
  
  roleIcon: {
    fontSize: ctronTypography.fontSize.lg,
    marginRight: ctronSpacing.sm,
  },
  
  roleText: {
    fontSize: ctronTypography.fontSize.base,
    fontWeight: ctronTypography.fontWeight.medium,
    color: ctronColors.textPrimary,
  },
  
  roleTextActive: {
    color: ctronColors.textLight,
  },
  
  passwordContainer: {
    position: 'relative',
  },
  
  passwordInput: {
    paddingRight: 50,
  },
  
  passwordToggle: {
    position: 'absolute',
    right: 16,
    top: 18,
    padding: 4,
  },
  
  passwordToggleText: {
    fontSize: 16,
    color: ctronColors.textSecondary,
  },
  
  buttonDisabled: {
    opacity: 0.7,
  },
  
  buttonLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  buttonLoadingText: {
    fontSize: ctronTypography.fontSize.base,
    fontWeight: ctronTypography.fontWeight.semiBold,
    color: ctronColors.textLight,
    marginLeft: 8,
  },
  
  buttonText: {
    fontSize: ctronTypography.fontSize.base,
    fontWeight: ctronTypography.fontWeight.semiBold,
    color: ctronColors.textLight,
  },
  
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: ctronSpacing.lg,
  },
  
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: ctronColors.gray300,
  },
  
  dividerText: {
    marginHorizontal: ctronSpacing.md,
    color: ctronColors.textSecondary,
    fontSize: ctronTypography.fontSize.sm,
  },
  
  socialButton: {
    height: 56,
    backgroundColor: ctronColors.backgroundSecondary,
    borderWidth: 1,
    borderColor: ctronColors.inputBorder,
    borderRadius: ctronBorderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: ctronSpacing.md,
  },
  
  socialButtonText: {
    fontSize: ctronTypography.fontSize.base,
    fontWeight: ctronTypography.fontWeight.medium,
    color: ctronColors.textPrimary,
  },
  
  errorContainer: {
    backgroundColor: ctronColors.error + '10',
    padding: ctronSpacing.md,
    borderRadius: ctronBorderRadius.md,
    marginTop: ctronSpacing.sm,
    alignItems: 'center',
  },
  
  footer: {
    alignItems: 'center',
    paddingTop: ctronSpacing.lg,
    paddingBottom: ctronSpacing.md,
  },
  
  footerText: {
    color: ctronColors.textSecondary,
    fontSize: ctronTypography.fontSize.sm,
    marginBottom: ctronSpacing.sm,
  },
  
  linkText: {
    fontSize: ctronTypography.fontSize.sm,
    color: ctronColors.accent,
    fontWeight: ctronTypography.fontWeight.medium,
  },

  // Splash Screen styles
  splashContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ctronSpacing.lg,
  },
  splashLogoContainer: {
    alignItems: 'center',
    marginBottom: ctronSpacing['3xl'],
  },
  splashLogo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: ctronColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...ctronShadows.md,
  },
  splashLogoText: {
    fontSize: 32,
    fontWeight: ctronTypography.fontWeight.bold,
    color: ctronColors.textLight,
    letterSpacing: 2,
  },
  splashContent: {
    alignItems: 'center',
    marginBottom: ctronSpacing['3xl'],
  },
  splashTitle: {
    fontSize: ctronTypography.fontSize['3xl'],
    fontWeight: ctronTypography.fontWeight.bold,
    color: ctronColors.textPrimary,
    marginBottom: ctronSpacing.md,
    textAlign: 'center',
  },
  splashSubtitle: {
    fontSize: ctronTypography.fontSize.base,
    color: ctronColors.textSecondary,
    textAlign: 'center',
    lineHeight: ctronTypography.lineHeight.normal,
    marginBottom: ctronSpacing.sm,
    paddingHorizontal: ctronSpacing.md,
  },
  splashTagline: {
    fontSize: ctronTypography.fontSize.sm,
    color: ctronColors.primary,
    fontWeight: ctronTypography.fontWeight.semiBold,
    textAlign: 'center',
    letterSpacing: 1,
  },
  splashFooter: {
    position: 'absolute',
    bottom: ctronSpacing['3xl'],
    alignItems: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: ctronColors.primary,
    marginHorizontal: 4,
  },
  loadingDotDelay1: {
    opacity: 0.7,
  },
  loadingDotDelay2: {
    opacity: 0.4,
  },
});

// Animation configurations
export const ctronAnimations = {
  spring: {
    tension: 300,
    friction: 10,
  },
  timing: {
    duration: 300,
  },
  easing: {
    easeInOut: 'ease-in-out',
    easeOut: 'ease-out',
  },
};

export default {
  colors: ctronColors,
  typography: ctronTypography,
  spacing: ctronSpacing,
  borderRadius: ctronBorderRadius,
  shadows: ctronShadows,
  styles: ctronStyles,
  animations: ctronAnimations,
};