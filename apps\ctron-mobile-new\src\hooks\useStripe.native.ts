/**
 * Native-specific Stripe hook implementation
 * 
 * This module provides the native implementation of the Stripe hook,
 * which uses the @stripe/stripe-react-native library.
 */
import { useStripe as useStripeNative } from '@stripe/stripe-react-native';

import { StripeInterface } from '../types/stripe';

/**
 * Hook that provides access to Stripe functionality in native environment
 * @returns The Stripe instance for native platforms (iOS/Android)
 */
export const useStripe = (): StripeInterface => {
  const stripeNative = useStripeNative();
  
  // Return the native implementation with proper error handling
  return {
    ...stripeNative,
    // Override methods as needed to ensure consistent error handling
    initPaymentSheet: async (options) => {
      try {
        const result = await stripeNative.initPaymentSheet(options);
        return { error: result.error ? new Error(result.error.message) : null };
      } catch (error) {
        console.error('Native Stripe initPaymentSheet error:', error);
        return { error: error instanceof Error ? error : new Error('Unknown Stripe error') };
      }
    },
    presentPaymentSheet: async () => {
      try {
        const result = await stripeNative.presentPaymentSheet();
        return { error: result.error ? new Error(result.error.message) : null };
      } catch (error) {
        console.error('Native Stripe presentPaymentSheet error:', error);
        return { error: error instanceof Error ? error : new Error('Unknown Stripe error') };
      }
    }
  };
};