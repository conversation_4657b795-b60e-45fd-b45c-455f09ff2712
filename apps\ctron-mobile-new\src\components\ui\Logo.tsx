// CTRON Home Logo Component
// Professional logo design for home services app

import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { View, StyleSheet } from '../../utils/platformUtils';

interface LogoProps {
  size?: number;
  color?: string;
  backgroundColor?: string;
}

const Logo: React.FC<LogoProps> = ({
  size = 120,
  color = '#ffffff',
  backgroundColor = '#6366F1'
}) => {
  const iconSize = size * 0.6;

  return (
    <View style={[
      styles.container,
      {
        width: size,
        height: size,
        borderRadius: size / 2,
        backgroundColor,
      }
    ]}>
      <Svg
        width={iconSize}
        height={iconSize}
        viewBox="0 0 24 24"
        fill="none"
      >
        {/* Simple home icon */}
        <Path
          d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill={color}
          fillOpacity="0.1"
        />
        <Path
          d="M9 22V12H15V22"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
});

export default Logo;