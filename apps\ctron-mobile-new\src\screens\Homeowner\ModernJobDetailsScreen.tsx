// CTRON Home - Modern Job Details Screen
// Enhanced job details view with improved chat integration and modern UI

import { useNavigation, useRoute } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RouteProp } from '@react-navigation/native';
import React, { useState, useEffect, useMemo, useRef } from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import type { ViewStyle } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';

import { JobAPI } from '../../api/job.api';
import type { Job } from '../../types/job';
import { ChatAPI } from '../../api/chat.api';
import { Button, Card, StatusBadge } from '../../design-system';
import { Header } from '../../components/ui/Header';
import { useTheme } from '../../context/ThemeContext';
import type { HomeownerStackParamList } from '../../navigation/types';
import { 
  Alert, 
  StyleSheet, 
  Text, 
  TouchableOpacity, 
  View, 
  ScrollView, 
  ActivityIndicator,
  Animated,
  Dimensions,
  StatusBar,
  RefreshControl
} from '../../utils/platformUtils';
import { Linking } from '../../utils/platformUtils';

type JobDetailsScreenNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'JobDetails'>;
type JobDetailsScreenRouteProp = RouteProp<HomeownerStackParamList, 'JobDetails'>;

interface JobUpdate {
  id: string;
  timestamp: string;
  type: 'status_change' | 'technician_assigned' | 'message' | 'payment' | 'completion';
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
}

const { width: screenWidth } = Dimensions.get('window');

export default function ModernJobDetailsScreen() {
  const navigation = useNavigation<JobDetailsScreenNavigationProp>();
  const route = useRoute<JobDetailsScreenRouteProp>();
  const { colors, spacing, typography, borderRadius } = useTheme();
  const { jobId } = route.params;

  // State
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [startingChat, setStartingChat] = useState(false);
  const [jobUpdates, setJobUpdates] = useState<JobUpdate[]>([]);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const scrollY = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadJobDetails();
    
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  useEffect(() => {
    // Header opacity based on scroll
    const listener = scrollY.addListener(({ value }: { value: number }) => {
      const opacity = Math.min(value / 100, 1);
      headerOpacity.setValue(opacity);
    });

    return () => scrollY.removeListener(listener);
  }, []);

  const loadJobDetails = async () => {
    try {
      const jobData = await JobAPI.getJobById(jobId);
      setJob(jobData);
      generateJobUpdates(jobData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load job details');
    } finally {
      setLoading(false);
    }
  };

  const generateJobUpdates = (jobData: Job) => {
    const updates: JobUpdate[] = [
      {
        id: '1',
        timestamp: jobData.createdAt,
        type: 'status_change',
        title: 'Job Created',
        description: 'Your service request has been submitted',
        icon: 'add-circle',
        color: colors.semantic.info
      }
    ];

    if (jobData.technician) {
      updates.push({
        id: '2',
        timestamp: jobData.updatedAt,
        type: 'technician_assigned',
        title: 'Technician Assigned',
        description: `${jobData.technician.user.fullName} has been assigned to your job`,
        icon: 'person-add',
        color: colors.primary.main
      });
    }

    if (jobData.status === 'IN_PROGRESS') {
      updates.push({
        id: '3',
        timestamp: jobData.updatedAt,
        type: 'status_change',
        title: 'Work Started',
        description: 'Technician has started working on your job',
        icon: 'play-circle',
        color: colors.semantic.warning
      });
    }

    if (jobData.status === 'COMPLETED') {
      updates.push({
        id: '4',
        timestamp: jobData.updatedAt,
        type: 'completion',
        title: 'Job Completed',
        description: 'Your service has been completed successfully',
        icon: 'checkmark-circle',
        color: colors.semantic.success
      });
    }

    setJobUpdates(updates.reverse());
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadJobDetails();
    setRefreshing(false);
  };

  const handleStartChat = async () => {
    if (!job?.technician) {
      Alert.alert('Info', 'No technician assigned yet');
      return;
    }

    setStartingChat(true);
    try {
      const response = await ChatAPI.createOrGetChat(jobId, job.technician.id);
      navigation.navigate('Chat', { chatId: response.id, jobTitle: job.title });
    } catch (error) {
      Alert.alert('Error', 'Failed to start chat');
    } finally {
      setStartingChat(false);
    }
  };

  const handleCallTechnician = () => {
    if (!job?.technician?.user?.phone) {
      Alert.alert('Info', 'Technician phone number not available');
      return;
    }

    Alert.alert(
      'Call Technician',
      `Call ${job.technician.user.fullName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Call', 
          onPress: () => Linking.openURL(`tel:${job.technician?.user?.phone}`) 
        }
      ]
    );
  };

  const handleEditJob = () => {
    if (job && ['pending', 'assigned'].includes(job.status)) {
      navigation.navigate('EditJob', { jobId });
    } else {
      Alert.alert('Info', 'Job cannot be edited in current status');
    }
  };

  const handlePayment = () => {
    if (job?.status === 'COMPLETED') {
      navigation.navigate('Payment', { jobId });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return colors.warning.main;
      case 'ACCEPTED': return colors.primary.main;
      case 'IN_PROGRESS': return colors.info.main;
      case 'COMPLETED': return colors.success.main;
      case 'CANCELLED': return colors.error.main;
      default: return colors.text.secondary;
    }
  };

  const getStatusIcon = (status: string): keyof typeof Ionicons.glyphMap => {
    switch (status) {
      case 'PENDING': return 'time-outline';
      case 'ACCEPTED': return 'person-outline';
      case 'IN_PROGRESS': return 'construct-outline';
      case 'COMPLETED': return 'checkmark-circle-outline';
      case 'CANCELLED': return 'close-circle-outline';
      default: return 'help-circle-outline';
    }
  };

  const JobUpdateItem = ({ update, isLast }: { update: JobUpdate; isLast: boolean }) => (
    <View style={styles.updateItem}>
      <View style={styles.updateTimeline}>
        <View style={[styles.updateIcon, { backgroundColor: update.color + '20' }]}>
          <Ionicons name={update.icon} size={16} color={update.color} />
        </View>
        {!isLast && <View style={styles.updateLine} />}
      </View>
      <View style={styles.updateContent}>
        <Text style={styles.updateTitle}>{update.title}</Text>
        <Text style={styles.updateDescription}>{update.description}</Text>
        <Text style={styles.updateTime}>
          {new Date(update.timestamp).toLocaleDateString()} at {new Date(update.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
    </View>
  );

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    gradientBackground: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: 300,
    },
    header: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      zIndex: 10,
      paddingTop: StatusBar.currentHeight || 44,
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.md,
    },
    headerBackground: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: colors.background.primary,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
    },
    moreButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    scrollView: {
      flex: 1,
    },
    content: {
      paddingTop: 120,
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.xl,
    },
    heroCard: {
      marginBottom: spacing.xl,
    },
    heroCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.xl,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.15)',
    },
    heroCardContent: {
      padding: spacing.xl,
    },
    jobHeader: {
      marginBottom: spacing.lg,
    },
    jobTitle: {
      fontSize: typography.fontSize.xxl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    jobMeta: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
      marginRight: spacing.md,
    },
    statusIcon: {
      marginRight: spacing.xs,
    },
    statusText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.primary,
      textTransform: 'capitalize',
    },
    jobId: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    jobDescription: {
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      lineHeight: 22,
      marginBottom: spacing.lg,
    },
    jobDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    detailItem: {
      flex: 1,
      alignItems: 'center',
    },
    detailIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    detailLabel: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    detailValue: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.primary,
      textAlign: 'center',
    },
    technicianCard: {
      marginBottom: spacing.xl,
    },
    technicianCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    technicianCardContent: {
      padding: spacing.lg,
    },
    technicianHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    technicianAvatar: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: colors.primary.main,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    technicianInitial: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    technicianInfo: {
      flex: 1,
    },
    technicianName: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    technicianSpecialization: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginBottom: spacing.sm,
    },
    technicianRating: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    ratingText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.primary,
      marginLeft: spacing.xs,
    },
    technicianActions: {
      flexDirection: 'row',
      gap: spacing.md,
    },
    actionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      paddingVertical: spacing.md,
      borderRadius: borderRadius.md,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.15)',
    },
    actionButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.primary,
      marginLeft: spacing.xs,
    },
    updatesCard: {
      marginBottom: spacing.xl,
    },
    updatesCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    updatesCardContent: {
      padding: spacing.lg,
    },
    sectionTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.lg,
    },
    updateItem: {
      flexDirection: 'row',
      marginBottom: spacing.lg,
    },
    updateTimeline: {
      alignItems: 'center',
      marginRight: spacing.md,
    },
    updateIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    updateLine: {
      width: 2,
      flex: 1,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      marginTop: spacing.sm,
    },
    updateContent: {
      flex: 1,
    },
    updateTitle: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    updateDescription: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
      lineHeight: 20,
    },
    updateTime: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
    },
    actionsCard: {
      marginBottom: spacing.xl,
    },
    actionsCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    actionsCardContent: {
      padding: spacing.lg,
    },
    actionsList: {
      gap: spacing.md,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: 120,
    },
    loadingText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginTop: spacing.md,
    },
    noTechnicianCard: {
      marginBottom: spacing.xl,
    },
    noTechnicianCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    noTechnicianCardContent: {
      padding: spacing.lg,
      alignItems: 'center',
    },
    noTechnicianIcon: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    noTechnicianTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
      textAlign: 'center',
    },
    noTechnicianDescription: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: 22,
    },
  }), [colors, spacing, typography, borderRadius]);

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.gradientBackground}>
          <LinearGradient
            colors={['#667eea', '#764ba2', '#f093fb']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary.main} />
          <Text style={styles.loadingText}>Loading job details...</Text>
        </View>
      </View>
    );
  }

  if (!job) {
    return (
      <View style={styles.container}>
        <Header 
          title="Job Details" 
          leftAction={{
            icon: <Text>←</Text>,
            onPress: () => navigation.goBack(),
            accessibilityLabel: "Go back"
          }} 
        />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Job not found</Text>
        </View>
      </View>
    );
  }

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      <View style={styles.gradientBackground}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>
      
      {/* Animated Header */}
      <View style={styles.header}>
        <Animated.View style={[styles.headerBackground, { opacity: headerOpacity }]} />
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={20} color={colors.text.primary} />
          </TouchableOpacity>
          <Animated.Text style={[styles.headerTitle, { opacity: headerOpacity }]}>
            Job Details
          </Animated.Text>
          <TouchableOpacity style={styles.moreButton} onPress={handleEditJob}>
            <Ionicons name="ellipsis-horizontal" size={20} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <Animated.ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary.main}
          />
        }
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        {/* Hero Card */}
        <View style={styles.heroCard}>
          <View style={styles.heroCardGlass as ViewStyle}>
              <BlurView intensity={20} />
            </View>
          <View style={styles.heroCardContent}>
            <View style={styles.jobHeader}>
              <Text style={styles.jobTitle}>{job.issue}</Text>
              <View style={styles.jobMeta}>
                <View style={styles.statusContainer}>
                  <Ionicons 
                    name={getStatusIcon(job.status)} 
                    size={16} 
                    color={getStatusColor(job.status)}
                    style={styles.statusIcon}
                  />
                  <Text style={styles.statusText}>{job.status.replace('_', ' ')}</Text>
                </View>
                <Text style={styles.jobId}>#{job.id.slice(-8)}</Text>
              </View>
              {job.description && (
                <Text style={styles.jobDescription}>{job.description}</Text>
              )}
            </View>
            
            <View style={styles.jobDetails}>
              <View style={styles.detailItem}>
                <View style={styles.detailIcon}>
                  <Ionicons name="calendar-outline" size={20} color={colors.primary.main} />
                </View>
                <Text style={styles.detailLabel}>Scheduled</Text>
                <Text style={styles.detailValue}>
                  {new Date(job.scheduledAt).toLocaleDateString()}
                </Text>
              </View>
              
              <View style={styles.detailItem}>
                <View style={styles.detailIcon}>
                  <Ionicons name="time-outline" size={20} color={colors.primary.main} />
                </View>
                <Text style={styles.detailLabel}>Time</Text>
                <Text style={styles.detailValue}>
                  {new Date(job.scheduledAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </View>
              
              <View style={styles.detailItem}>
                <View style={styles.detailIcon}>
                  <Ionicons name="alert-circle-outline" size={20} color={colors.primary.main} />
                </View>
                <Text style={styles.detailLabel}>Priority</Text>
                <Text style={styles.detailValue}>
                  {job.priority || 'medium'}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Technician Card */}
        {job.technician ? (
          <View style={styles.technicianCard}>
            <View style={styles.technicianCardGlass as ViewStyle}>
                  <BlurView intensity={15} />
                </View>
            <View style={styles.technicianCardContent}>
              <View style={styles.technicianHeader}>
                <View style={styles.technicianAvatar}>
                  <Text style={styles.technicianInitial}>
                    {job.technician.user.fullName.charAt(0)}
                  </Text>
                </View>
                <View style={styles.technicianInfo}>
                  <Text style={styles.technicianName}>{job.technician.user.fullName}</Text>
                  <Text style={styles.technicianSpecialization}>
                    {job.technician.specialization || 'General Technician'}
                  </Text>
                  <View style={styles.technicianRating}>
                    <Ionicons name="star" size={16} color="#FFD700" />
                    <Text style={styles.ratingText}>4.8 (156 reviews)</Text>
                  </View>
                </View>
              </View>
              
              <View style={styles.technicianActions}>
                <TouchableOpacity style={styles.actionButton} onPress={handleStartChat}>
                  {startingChat ? (
                    <ActivityIndicator size="small" color={colors.text.primary} />
                  ) : (
                    <>
                      <Ionicons name="chatbubble-outline" size={18} color={colors.text.primary} />
                      <Text style={styles.actionButtonText}>Chat</Text>
                    </>
                  )}
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.actionButton} onPress={handleCallTechnician}>
                  <Ionicons name="call-outline" size={18} color={colors.text.primary} />
                  <Text style={styles.actionButtonText}>Call</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        ) : (
          <View style={styles.noTechnicianCard}>
            <View style={styles.noTechnicianCardGlass as ViewStyle}>
                <BlurView intensity={15} />
              </View>
            <View style={styles.noTechnicianCardContent}>
              <View style={styles.noTechnicianIcon}>
                <Ionicons name="person-outline" size={24} color={colors.text.secondary} />
              </View>
              <Text style={styles.noTechnicianTitle}>No Technician Assigned</Text>
              <Text style={styles.noTechnicianDescription}>
                We're finding the best technician for your job. You'll be notified once someone is assigned.
              </Text>
            </View>
          </View>
        )}

        {/* Job Updates */}
        <View style={styles.updatesCard}>
          <View style={styles.updatesCardGlass as ViewStyle}>
              <BlurView intensity={15} />
            </View>
          <View style={styles.updatesCardContent}>
            <Text style={styles.sectionTitle}>Job Updates</Text>
            {jobUpdates.map((update, index) => (
              <JobUpdateItem 
                key={update.id} 
                update={update} 
                isLast={index === jobUpdates.length - 1}
              />
            ))}
          </View>
        </View>

        {/* Actions */}
        <View style={styles.actionsCard}>
          <View style={styles.actionsCardGlass as ViewStyle}>
              <BlurView intensity={15} />
            </View>
          <View style={styles.actionsCardContent}>
            <Text style={styles.sectionTitle}>Actions</Text>
            <View style={styles.actionsList}>
              {['pending', 'assigned'].includes(job.status) && (
                <Button
                  title="Edit Job"
                  onPress={handleEditJob}
                  variant="outline"
                  size="lg"
                  icon="create-outline"
                />
              )}
              
              {job.status === 'COMPLETED' && (
                <Button
                  title="Make Payment"
                  onPress={handlePayment}
                  variant="primary"
                  size="lg"
                  icon="card-outline"
                />
              )}
              
              {job.status === 'COMPLETED' && (
                <Button
                  title="Rate & Review"
                  onPress={() => {/* Navigate to rating screen */}}
                  variant="outline"
                  size="lg"
                  icon="star-outline"
                />
              )}
            </View>
          </View>
        </View>
      </Animated.ScrollView>
    </Animated.View>
  );
}