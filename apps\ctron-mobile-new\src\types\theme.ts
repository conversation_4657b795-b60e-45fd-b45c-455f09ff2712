// Theme type definitions for CTRON Home mobile app

export interface ColorShade {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  main: string;
  light: string;
  dark: string;
  contrast: string;
}

export interface SimpleColor {
  main: string;
  light: string;
  dark: string;
}

export interface StatusColors {
  pending: string;
  assigned: string;
  active: string;
  completed: string;
  cancelled: string;
  overdue: string;
  error: string;
}

export interface BackgroundColors {
  primary: string;
  secondary: string;
  tertiary: string;
  surface: string;
  card: string;
  modal: string;
  overlay: string;
  disabled: string;
}

export interface TextColors {
  primary: string;
  secondary: string;
  tertiary: string;
  disabled: string;
  inverse: string;
  link: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

export interface BorderColors {
  light: string;
  medium: string;
  dark: string;
  focus: string;
  error: string;
  success: string;
}

export interface Colors {
  primary: ColorShade;
  secondary: ColorShade;
  status: StatusColors;
  error: SimpleColor;
  warning: SimpleColor;
  info: SimpleColor;
  success: ColorShade;
  gray: ColorShade;
  background: BackgroundColors;
  text: TextColors;
  border: BorderColors;
  white: string;
  black: string;
  transparent: string;
}

export interface Spacing {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
  xxxl: number;
}

export interface Typography {
  h1: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  h2: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  h3: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  h4: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  h5: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  h6: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  body1: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  body2: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  caption: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  button: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
  overline: {
    fontSize: number;
    fontWeight: string;
    lineHeight: number;
  };
}

export interface BorderRadius {
  none: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  full: number;
}

export interface Shadows {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

export interface Sizes {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

export interface Theme {
  colors: Colors;
  spacing: Spacing;
  typography: Typography;
  borderRadius: BorderRadius;
  shadows: Shadows;
  sizes: Sizes;
}

export type ThemeColors = Colors;
export type ThemeSpacing = Spacing;
export type ThemeTypography = Typography;
export type ThemeBorderRadius = BorderRadius;
export type ThemeShadows = Shadows;
export type ThemeSizes = Sizes;