// CTRON Login Screen - Clean, Professional Design
// Based on reference UI with CTRON branding

import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useRef } from 'react';

import AuthAPI from '../../api/auth.api';
import { BackgroundElements } from '../../components/auth/BackgroundElements';
import { EnhancedButton } from '../../components/auth/EnhancedButton';
import { EnhancedInput } from '../../components/auth/EnhancedInput';
import SafeLinearGradient from '../../components/SafeLinearGradient';
import Logo from '../../components/ui/Logo';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import type { AuthStackParamList } from '../../navigation/AuthStack';
import { View, Text, ScrollView, Dimensions, StatusBar, KeyboardAvoidingView, Platform, SafeAreaView, TouchableOpacity, Animated, Easing, StyleSheet } from '../../utils/platformUtils';





export default function LoginScreen() {
  const { login } = useAuth();
  const navigation = useNavigation<StackNavigationProp<AuthStackParamList, 'Login'>>();
  const { theme, isDark } = useTheme();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ email?: string; password?: string; general?: string }>({});
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const logoScaleAnim = useRef(new Animated.Value(0.8)).current;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary,
    },
    gradientOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: -2,
    },
    header: {
      alignItems: 'center',
      paddingVertical: theme.spacing.xl,
      position: 'relative',
    },
    headerGlow: {
      position: 'absolute',
      top: -50,
      left: '50%',
      marginLeft: -100,
      width: 200,
      height: 200,
      borderRadius: 100,
      backgroundColor: isDark ? `${theme.colors.primary.main}15` : `${theme.colors.primary.light}20`,
      opacity: 0.6,
    },

    brandName: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.text.primary,
      marginBottom: theme.spacing.xs,
    },
    welcomeTitle: {
      fontSize: 16,
      color: theme.colors.text.secondary,
      textAlign: 'center',
    },
    tagline: {
      fontSize: 16,
      color: theme.colors.text.secondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    form: {
      paddingHorizontal: theme.spacing.lg,
      paddingBottom: theme.spacing.xl,
      position: 'relative',
    },
    formContainer: {
      paddingHorizontal: theme.spacing.md,
    },
    floatingElement: {
      position: 'absolute',
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: `${theme.colors.secondary.main}10`,
    },
    errorText: {
      color: theme.colors.error.main,
      fontSize: 14,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
    },
    signupContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: theme.spacing.lg,
    },
    signupText: {
      color: theme.colors.text.secondary,
      fontSize: 14,
    },
    signupLink: {
      color: theme.colors.primary.main,
      fontSize: 14,
      fontWeight: '600',
      marginLeft: theme.spacing.xs,
    },
  });

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.spring(logoScaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const validateEmail = (value: string) => {
    if (!value.trim()) {
      return 'Email is required';
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return 'Please enter a valid email address';
    }
    return '';
  };
  
  const validatePassword = (value: string) => {
    if (!value) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return '';
  };
  
  const validateForm = () => {
    const emailError = validateEmail(email);
    const passwordError = validatePassword(password);
    
    const newErrors = {
      email: emailError,
      password: passwordError,
    };
    
    setErrors(newErrors);
    return !emailError && !passwordError;
  };
  
  const handleEmailChange = (value: string) => {
    setEmail(value);
    // Real-time validation
    if (errors.email) {
      const emailError = validateEmail(value);
      setErrors(prev => ({ ...prev, email: emailError }));
    }
  };
  
  const handlePasswordChange = (value: string) => {
    setPassword(value);
    // Real-time validation
    if (errors.password) {
      const passwordError = validatePassword(value);
      setErrors(prev => ({ ...prev, password: passwordError }));
    }
  };

  const handleLogin = async () => {
    setErrors({});

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      const res = await AuthAPI.login({ email, password });
      const { accessToken } = res;
      if (!accessToken) throw new Error('No token returned from server');
      await login(accessToken);
    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string } }; message?: string };
      const errorMessage = error.response?.data?.message || error.message || 'Login failed';
      setErrors({ general: errorMessage });
    } finally {
      setLoading(false);
    }
  };

  // Use authStyles directly since it's already a StyleSheet object

  return (
    <SafeAreaView style={styles.container}>
      {/* Enhanced Gradient Background */}
      <SafeLinearGradient
        colors={isDark 
          ? [theme.colors.background.primary, `${theme.colors.primary.main}20`, theme.colors.background.primary]
          : [`${theme.colors.primary.light}10`, theme.colors.background.primary, `${theme.colors.secondary.light}15`]
        }
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientOverlay}
      />
      <BackgroundElements variant="login" />
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor={theme.colors.background.primary} />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={[
            {
              flexGrow: 1,
              paddingHorizontal: theme.spacing.lg,
              justifyContent: 'center',
              minHeight: Dimensions.get('window').height - 100
            }
          ]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header Section */}
          <Animated.View 
            style={[
              styles.header,
              {
                opacity: fadeAnim,
                transform: [
                  { translateY: slideAnim },
                  { scale: logoScaleAnim }
                ]
              }
            ]}
          >
            {/* Header Glow Effect */}
            <View style={styles.headerGlow} />
            
            {/* CTRON Logo */}
            <Logo 
              size={80} 
              color={theme.colors.primary.contrast} 
              backgroundColor={theme.colors.primary.main} 
            />
            <Text style={[styles.brandName, { marginBottom: theme.spacing.sm }]}>CTRON Home</Text>
            <Text style={[styles.tagline, { marginBottom: theme.spacing.xl }]}>Your trusted home services partner</Text>
          </Animated.View>

          {/* Form Section */}
          <Animated.View 
            style={[
              { paddingHorizontal: theme.spacing.sm },
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}
          >
            {/* Email Input */}
            <EnhancedInput
              label="Email"
              value={email}
              onChangeText={handleEmailChange}
              error={errors.email}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholder="<EMAIL>"
              accessibilityLabel="Email address"
              accessibilityHint="Enter your email address"
            />

            {/* Password Input */}
            <EnhancedInput
              label="Password"
              value={password}
              onChangeText={handlePasswordChange}
              error={errors.password}
              secureTextEntry={true}
              showPasswordToggle={true}
              placeholder="Enter your password"
              accessibilityLabel="Password"
              accessibilityHint="Enter your password"
            />

            {/* Login Button */}
            <EnhancedButton
              title="Login"
              onPress={handleLogin}
              loading={loading}
              loadingText="Signing in..."
              variant="primary"
              size="large"
              accessibilityLabel="Sign in to your account"
              style={{ marginTop: theme.spacing.lg }}
            />

            {/* Error Display */}
            {errors.general && (
              <View style={{ marginTop: theme.spacing.md, padding: theme.spacing.md, backgroundColor: theme.colors.error.light, borderRadius: theme.borderRadius.sm }}>
                <Text style={styles.errorText}>{errors.general}</Text>
              </View>
            )}
          </Animated.View>

          {/* Footer */}
          <Animated.View 
            style={[
              { alignItems: 'center', marginTop: theme.spacing.lg, paddingBottom: theme.spacing.lg },
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}
          >
            <Text style={styles.signupText}>Don't have an account?</Text>
            <TouchableOpacity
              style={{ marginTop: theme.spacing.xs }}
              onPress={() => navigation.navigate('Signup')}
              accessibilityLabel="Create new account"
            >
              <Text style={styles.signupLink}>Sign Up</Text>
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}


