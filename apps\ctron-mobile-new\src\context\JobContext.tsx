// src/context/JobContext.tsx

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
} from 'react';

import { JobAPI } from '../api/job.api';        // ✅ Import only the API
import type { Job } from '../types/job';        // ✅ Use unified shared type

import { useAuth } from './AuthContext';

type JobContextType = {
  myJobs: Job[];
  assignedJobs: Job[];
  fetchMyJobs: () => Promise<void>;
  fetchAssignedJobs: () => Promise<void>;
  refreshJobs: () => Promise<void>;
  loading: boolean;
};

const JobContext = createContext<JobContextType | undefined>(undefined);

export const JobProvider = ({ children }: { children: ReactNode }) => {
  const { token, user } = useAuth();
  const [myJobs, setMyJobs] = useState<Job[]>([]);
  const [assignedJobs, setAssignedJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // Debug logging for JobProvider
  if (__DEV__) {
    console.log('🔧 JobProvider rendering - User:', user ? (user as { id?: string; userId?: string }).id || (user as { id?: string; userId?: string }).userId : 'null', 'Token:', token ? 'Present' : 'Missing');
  }

  const fetchMyJobs = useCallback(async () => {
    try {
      const data = await JobAPI.getUserJobs();
      console.log('📥 My Jobs fetched:', data.length);
      setMyJobs(data || []);
    } catch (error: unknown) {
      const apiError = error as { response?: { data?: unknown; status?: number }; message?: string };
      console.error('❌ Error fetching my jobs:', error);
      console.error('❌ Error response:', apiError.response?.data);
      console.error('❌ Error status:', apiError.response?.status);
      console.error('❌ Error message:', apiError.message);
      setMyJobs([]);
    }
  }, [setMyJobs]);

  const fetchAssignedJobs = useCallback(async () => {
    try {
      const data = await JobAPI.getTechnicianJobs();
      console.log('🔧 Assigned Jobs fetched:', data.length);
      setAssignedJobs(data || []);
    } catch (error) {
      console.error('❌ Error fetching assigned jobs:', error);
      setAssignedJobs([]);
    }
  }, [setAssignedJobs]);

  const refreshJobs = useCallback(async () => {
    if (!token || !user) {
      setMyJobs([]);
      setAssignedJobs([]);
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      if (user.role === 'TECHNICIAN') {
        await fetchAssignedJobs();
      } else {
        await fetchMyJobs();
      }
    } finally {
      setLoading(false);
    }
  }, [token, user, fetchAssignedJobs, fetchMyJobs, setMyJobs, setAssignedJobs, setLoading]);

  // ✅ Auto-refresh jobs when user or token changes
  useEffect(() => {
    refreshJobs();
  }, [refreshJobs]);

  return (
    <JobContext.Provider
      value={{
        myJobs,
        assignedJobs,
        fetchMyJobs,
        fetchAssignedJobs,
        refreshJobs,
        loading,
      }}
    >
      {children}
    </JobContext.Provider>
  );
};

export const useJobs = (): JobContextType => {
  const context = useContext(JobContext);
  if (!context) {
    throw new Error('❌ useJobs must be used within a JobProvider');
  }
  return context;
};
