// src/index.ts

import { app, server } from './server';
import { env } from './config/env';
import { logger } from './utils/logger';
const PORT = env.PORT || 3001;
const HOST = env.HOST || '0.0.0.0';

// Start server
const startServer = async () => {
  try {
    // Run comprehensive startup validation
    const { validateSystemStartup, displayValidationResults } = await import('./utils/startupValidator');
    const validationResult = await validateSystemStartup();
    
    displayValidationResults(validationResult);
    
    if (!validationResult.isValid) {
      logger.error('❌ System validation failed. Server will not start.');
      process.exit(1);
    }

    server.listen(PORT, HOST, () => {
      logger.info(`🚀 Server is running on http://localhost:${PORT}`);
      if (HOST !== 'localhost' && HOST !== '127.0.0.1') {
        logger.info(`🌐 Network access available on all interfaces (${HOST}:${PORT})`);
        logger.info(`🔗 API available at: http://${HOST}:${PORT}/api`);
        logger.info(`🔌 Socket.IO available at: http://${HOST}:${PORT}/socket.io/`);
        logger.info(`📱 Mobile app can connect to: http://${HOST}:${PORT}`);
      }
      logger.info('✅ All systems operational');
    });
  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

server.on('error', (error: any) => {
  logger.error('❌ Server error:', error);
});

process.on('SIGINT', () => {
  logger.info('\n🛑 Server shutting down...');
  server.close(() => {
    logger.info('✅ Server closed');
    process.exit(0);
  });
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  logger.error('❌ Uncaught Exception:', error);
  process.exit(1);
});
