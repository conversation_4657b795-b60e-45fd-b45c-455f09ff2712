// apps/web/src/pages/admin/AssistantPage.tsx

import React, { useState, useEffect, useRef } from 'react';
import { Send, Bot, User, Lightbulb, History, Star } from 'lucide-react';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { PageContainer } from '../../components/ui/PageContainer';
import { useAuth } from '../../hooks/useAuth';
import { aiAPI } from '../../api/ai.api';

interface AIMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  confidence?: number;
  sources?: string[];
  suggestions?: string[];
}

interface QueryTemplate {
  id: string;
  text: string;
  category: string;
}

export const AssistantPage: React.FC = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [templates, setTemplates] = useState<QueryTemplate[]>([]);
  const [showTemplates, setShowTemplates] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadTemplates();
    // Add welcome message
    setMessages([
      {
        id: '1',
        type: 'assistant',
        content: `Hello! I'm CTRON Assistant. I can help you with platform management, analytics, user insights, and operational questions. What would you like to know?`,
        timestamp: new Date(),
        confidence: 1.0,
      },
    ]);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadTemplates = async () => {
    try {
      const response = await aiAPI.getTemplates();
      const templateData = response.data.templates.map((text: string, index: number) => ({
        id: `template-${index}`,
        text,
        category: 'Admin',
      }));
      setTemplates(templateData);
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  };

  const handleSendMessage = async (query?: string) => {
    const messageText = query || inputValue.trim();
    if (!messageText || isLoading) return;

    const userMessage: AIMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: messageText,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setShowTemplates(false);

    try {
      const response = await aiAPI.processQuery({
        query: messageText,
        context: `Admin user: ${user?.name}`,
      });

      const assistantMessage: AIMessage = {
        id: `assistant-${Date.now()}`,
        type: 'assistant',
        content: response.data.response,
        timestamp: new Date(),
        confidence: response.data.confidence,
        sources: response.data.sources,
        suggestions: response.data.suggestions,
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      const errorMessage: AIMessage = {
        id: `error-${Date.now()}`,
        type: 'assistant',
        content: 'I apologize, but I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
        confidence: 0,
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTemplateClick = (template: QueryTemplate) => {
    handleSendMessage(template.text);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <PageContainer padding={false}>
      <div className="flex flex-col h-[calc(100vh-8rem)] md:h-[calc(100vh-2rem)] bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg overflow-hidden">
      {/* Enhanced Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 px-4 md:px-6 py-3 md:py-4 shadow-sm flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 md:gap-3 flex-1 min-w-0">
            <div className="p-2 md:p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg flex-shrink-0">
              <Bot className="w-5 h-5 md:w-6 md:h-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <h1 className="text-lg md:text-xl font-bold text-gray-900 truncate">CTRON Assistant</h1>
              <p className="text-xs md:text-sm text-gray-600 hidden sm:block">AI-powered admin support • GPT-4</p>
            </div>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            <div className="flex items-center gap-1 px-2 py-1 md:px-3 md:py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="hidden xs:inline">Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-3 md:p-6 space-y-3 md:space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-2 md:gap-3 ${
              message.type === 'user' ? 'justify-end' : 'justify-start'
            }`}
          >
            {message.type === 'assistant' && (
              <div className="flex-shrink-0 w-6 h-6 md:w-8 md:h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Bot className="w-3 h-3 md:w-4 md:h-4 text-blue-600" />
              </div>
            )}

            <div
              className={`max-w-[85%] md:max-w-2xl ${
                message.type === 'user'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl rounded-br-md shadow-lg'
                  : 'bg-white/90 backdrop-blur-sm border border-gray-200/50 rounded-2xl rounded-bl-md shadow-md hover:shadow-lg transition-shadow'
              } p-3 md:p-5`}
            >
              <div className="prose prose-sm max-w-none">
                <p className={message.type === 'user' ? 'text-white' : 'text-gray-900'}>
                  {message.content}
                </p>
              </div>
              
              {/* Assistant message metadata */}
              {message.type === 'assistant' && (
                <div className="mt-3 space-y-2">
                  {/* Confidence and sources */}
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <span>{formatTime(message.timestamp)}</span>
                    {message.confidence && (
                      <>
                        <span>•</span>
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3" />
                          <span>{Math.round(message.confidence * 100)}% confidence</span>
                        </div>
                      </>
                    )}
                  </div>
                  
                  {/* Sources */}
                  {message.sources && message.sources.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {message.sources.map((source, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {source}
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  {/* Suggestions */}
                  {message.suggestions && message.suggestions.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500 mb-1 flex items-center gap-1">
                        <Lightbulb className="w-3 h-3" />
                        Suggestions:
                      </p>
                      <div className="space-y-1">
                        {message.suggestions.map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => handleSendMessage(suggestion)}
                            className="block text-left text-xs text-blue-600 hover:text-blue-800 hover:underline"
                          >
                            • {suggestion}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
              
              {/* User message timestamp */}
              {message.type === 'user' && (
                <div className="mt-2 text-xs text-blue-100">
                  {formatTime(message.timestamp)}
                </div>
              )}
            </div>
            
            {message.type === 'user' && (
              <div className="flex-shrink-0 w-6 h-6 md:w-8 md:h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <User className="w-3 h-3 md:w-4 md:h-4 text-gray-600" />
              </div>
            )}
          </div>
        ))}
        
        {/* Loading indicator */}
        {isLoading && (
          <div className="flex gap-2 md:gap-3 justify-start">
            <div className="flex-shrink-0 w-6 h-6 md:w-8 md:h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <Bot className="w-3 h-3 md:w-4 md:h-4 text-blue-600" />
            </div>
            <div className="bg-white border border-gray-200 rounded-lg rounded-bl-sm p-3 md:p-4 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-xs md:text-sm text-gray-500">Thinking...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Query Templates */}
      {showTemplates && templates.length > 0 && (
        <div className="px-3 md:px-6 py-3 md:py-4 bg-white border-t border-gray-200 flex-shrink-0">
          <div className="flex items-center gap-2 mb-3">
            <Lightbulb className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Quick Questions</span>
          </div>
          <div className="grid grid-cols-1 gap-2">
            {templates.slice(0, 4).map((template) => (
              <button
                key={template.id}
                onClick={() => handleTemplateClick(template)}
                className="text-left p-3 text-sm text-gray-600 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors min-h-touch"
              >
                {template.text}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="bg-white border-t border-gray-200 p-3 md:p-6 flex-shrink-0">
        <div className="flex gap-2 md:gap-3">
          <div className="flex-1">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything..."
              disabled={isLoading}
              className="w-full text-sm md:text-base"
              size="md"
            />
          </div>
          <Button
            onClick={() => handleSendMessage()}
            disabled={!inputValue.trim() || isLoading}
            className="px-3 md:px-4 flex-shrink-0"
            size="md"
          >
            <Send className="w-4 h-4" />
            <span className="sr-only">Send message</span>
          </Button>
        </div>
        <p className="text-xs text-gray-500 mt-2 hidden sm:block">
          Press Enter to send, Shift+Enter for new line
        </p>
      </div>
      </div>
    </PageContainer>
  );
};
