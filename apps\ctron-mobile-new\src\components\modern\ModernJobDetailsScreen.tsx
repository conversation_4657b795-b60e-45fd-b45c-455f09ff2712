// CTRON Home - Modern Job Details Screen with NativeBase
// Enhanced job details view with professional UI components

import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import type { NativeStackNavigationProp, NativeStackScreenProps } from '@react-navigation/native-stack';
import {
  Box,
  VStack,
  HStack,
  Text,
  ScrollView,
  Button,
  Avatar,
  Badge,
  Icon,

  useToast,
  Progress,

  Select,
  AlertDialog,
} from 'native-base';
import React, { useState, useEffect } from 'react';

import { ChatAPI } from '../../api/chat.api';
import { JobAPI } from '../../api/job.api';
import type { HomeownerStackParamList } from '../../navigation/types';
import type { Job } from '../../types/job';

type JobDetailsScreenProps = NativeStackScreenProps<HomeownerStackParamList, 'JobDetails'>;
type JobDetailsNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'JobDetails'>;

const ModernJobDetailsScreenNB: React.FC = () => {
  const navigation = useNavigation<JobDetailsNavigationProp>();
  const route = useRoute<JobDetailsScreenProps['route']>();
  const { jobId } = route.params;
  const toast = useToast();


  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [,] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [,] = useState({ rating: 5, comment: '' });

  useEffect(() => {
    loadJobDetails();
  }, [jobId]);

  const loadJobDetails = async () => {
    try {
      setLoading(true);
      const jobData = await JobAPI.getJobById(jobId);
      setJob(jobData);
    } catch (error) {
      toast.show({
        title: 'Error',
        description: 'Failed to load job details',
        status: 'error',
      });
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleCancelJob = async () => {
    if (!job || !cancelReason.trim()) return;

    try {
      await JobAPI.cancelJob(job.id, cancelReason);
      toast.show({
        title: 'Job Cancelled',
        description: 'Your job has been cancelled successfully',
        status: 'success',
      });
      setShowCancelDialog(false);
      loadJobDetails();
    } catch (error) {
      toast.show({
        title: 'Error',
        description: 'Failed to cancel job',
        status: 'error',
      });
    }
  };

  const handleContactTechnician = async () => {
    if (!job?.technician) return;

    try {
      const chat = await ChatAPI.createOrGetChat(job.id);
      navigation.navigate('Chat', {
        chatId: chat.id,
        jobTitle: job.title || job.issue,
        recipientId: job.technician.id,
      });
    } catch (error) {
      toast.show({
        title: 'Error',
        description: 'Failed to start chat',
        status: 'error',
      });
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          color: 'warning',
          text: 'Pending',
          icon: 'schedule',
          description: 'Waiting for technician assignment',
        };
      case 'ACCEPTED':
        return {
          color: 'info',
          text: 'Assigned',
          icon: 'person',
          description: 'Technician has been assigned',
        };
      case 'IN_PROGRESS':
        return {
          color: 'primary',
          text: 'In Progress',
          icon: 'build',
          description: 'Work is currently in progress',
        };
      case 'COMPLETED':
        return {
          color: 'success',
          text: 'Completed',
          icon: 'check-circle',
          description: 'Job has been completed',
        };
      case 'CANCELLED':
        return {
          color: 'error',
          text: 'Cancelled',
          icon: 'cancel',
          description: 'Job has been cancelled',
        };
      default:
        return {
          color: 'gray',
          text: 'Unknown',
          icon: 'help',
          description: 'Status unknown',
        };
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Box flex={1} bg="gray.50" safeArea>
        <VStack space={4} p={4}>
          {/* Loading skeleton would go here */}
          <Text>Loading job details...</Text>
        </VStack>
      </Box>
    );
  }

  if (!job) {
    return (
      <Box flex={1} bg="gray.50" safeArea justifyContent="center" alignItems="center">
        <Text fontSize="lg" color="gray.600">
          Job not found
        </Text>
      </Box>
    );
  }

  const statusConfig = getStatusConfig(job.status);

  return (
    <Box flex={1} bg="gray.50" safeArea>
      {/* Header */}
      <HStack
        justifyContent="space-between"
        alignItems="center"
        px={4}
        py={3}
        bg="white"
        shadow={1}
      >
        <Button
          variant="ghost"
          leftIcon={<Icon as={MaterialIcons} name="arrow-back" size="sm" />}
          onPress={() => navigation.goBack()}
        >
          Back
        </Button>
        <Text fontSize="lg" fontWeight="semibold">
          Job Details
        </Text>
        <Button
          variant="ghost"
          onPress={() => navigation.navigate('EditJob', { jobId: job.id })}
        >
          Edit
        </Button>
      </HStack>

      <ScrollView flex={1} px={4} py={4}>
        <VStack space={6}>
          {/* Status Card */}
          <Box bg="white" rounded="xl" shadow={2} p={4}>
            <VStack space={4}>
              <HStack justifyContent="space-between" alignItems="center">
                <VStack flex={1}>
                  <Text fontSize="xl" fontWeight="bold" color="gray.800">
                    {job.title || job.issue}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    Job ID: #{job.id.slice(-8)}
                  </Text>
                </VStack>
                <Badge colorScheme={statusConfig.color} variant="solid" size="lg">
                  {statusConfig.text}
                </Badge>
              </HStack>

              <HStack space={3} alignItems="center">
                <Icon
                  as={MaterialIcons}
                  name={statusConfig.icon}
                  color={`${statusConfig.color}.500`}
                  size="md"
                />
                <Text fontSize="md" color="gray.600">
                  {statusConfig.description}
                </Text>
              </HStack>

              {job.status === 'IN_PROGRESS' && (
                <VStack space={2}>
                  <HStack justifyContent="space-between">
                    <Text fontSize="sm" color="gray.600">
                      Progress
                    </Text>
                    <Text fontSize="sm" fontWeight="semibold" color="primary.600">
                      65%
                    </Text>
                  </HStack>
                  <Progress value={65} colorScheme="primary" size="md" />
                </VStack>
              )}
            </VStack>
          </Box>

          {/* Job Details */}
          <Box bg="white" rounded="xl" shadow={2} p={4}>
            <VStack space={4}>
              <Text fontSize="lg" fontWeight="semibold" color="gray.800">
                Job Information
              </Text>

              <VStack space={3}>
                <HStack space={3}>
                  <Icon as={MaterialIcons} name="description" color="gray.400" size="sm" />
                  <VStack flex={1}>
                    <Text fontSize="sm" color="gray.500">
                      Description
                    </Text>
                    <Text fontSize="md" color="gray.700">
                      {job.description || job.issue}
                    </Text>
                  </VStack>
                </HStack>

                <HStack space={3}>
                  <Icon as={MaterialIcons} name="schedule" color="gray.400" size="sm" />
                  <VStack flex={1}>
                    <Text fontSize="sm" color="gray.500">
                      Scheduled Date
                    </Text>
                    <Text fontSize="md" color="gray.700">
                      {formatDate(job.scheduledAt || job.createdAt)}
                    </Text>
                  </VStack>
                </HStack>

                <HStack space={3}>
                  <Icon as={MaterialIcons} name="location-on" color="gray.400" size="sm" />
                  <VStack flex={1}>
                    <Text fontSize="sm" color="gray.500">
                      Address
                    </Text>
                    <Text fontSize="md" color="gray.700">
                      {job.address || 'Address not specified'}
                    </Text>
                  </VStack>
                </HStack>

                <HStack space={3}>
                  <Icon as={MaterialIcons} name="build" color="gray.400" size="sm" />
                  <VStack flex={1}>
                    <Text fontSize="sm" color="gray.500">
                      Service Type
                    </Text>
                    <Text fontSize="md" color="gray.700">
                      {job.serviceType || 'General Service'}
                    </Text>
                  </VStack>
                </HStack>
              </VStack>
            </VStack>
          </Box>

          {/* Technician Info */}
          {job.technician && (
            <Box bg="white" rounded="xl" shadow={2} p={4}>
              <VStack space={4}>
                <Text fontSize="lg" fontWeight="semibold" color="gray.800">
                  Assigned Technician
                </Text>

                <HStack space={4} alignItems="center">
                  <Avatar
                    size="lg"
                    bg="primary.500"
                    source={job.technician.profileImage ? { uri: job.technician.profileImage } : undefined}
                  >
                    {job.technician.user.fullName.charAt(0)}
                  </Avatar>
                  <VStack flex={1} space={1}>
                    <Text fontSize="lg" fontWeight="semibold" color="gray.800">
                      {job.technician.user.fullName}
                    </Text>
                    <Text fontSize="md" color="gray.600">
                      {job.technician.specialization}
                    </Text>
                    <HStack space={2} alignItems="center">
                      <Icon as={MaterialIcons} name="star" color="yellow.400" size="sm" />
                      <Text fontSize="sm" color="gray.600">
                        {job.technician.rating || 4.5} rating
                      </Text>
                    </HStack>
                  </VStack>
                </HStack>

                <HStack space={3}>
                  <Button
                    flex={1}
                    colorScheme="primary"
                    leftIcon={<Icon as={MaterialIcons} name="chat" size="sm" />}
                    onPress={handleContactTechnician}
                  >
                    Message
                  </Button>
                  <Button
                    flex={1}
                    variant="outline"
                    colorScheme="primary"
                    leftIcon={<Icon as={MaterialIcons} name="phone" size="sm" />}
                    onPress={() => {
                      // Handle phone call
                      toast.show({
                        title: 'Calling',
                        description: `Calling ${job.technician?.user.fullName}`,
                        status: 'info',
                      });
                    }}
                  >
                    Call
                  </Button>
                </HStack>
              </VStack>
            </Box>
          )}

          {/* Payment Info */}
          {job.payment && (
            <Box bg="white" rounded="xl" shadow={2} p={4}>
              <VStack space={4}>
                <Text fontSize="lg" fontWeight="semibold" color="gray.800">
                  Payment Information
                </Text>

                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="md" color="gray.600">
                    Total Amount
                  </Text>
                  <Text fontSize="xl" fontWeight="bold" color="primary.600">
                    £{job.payment.amount}
                  </Text>
                </HStack>

                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="md" color="gray.600">
                    Payment Status
                  </Text>
                  <Badge
                    colorScheme={job.payment.isReleased ? 'success' : 'warning'}
                    variant="solid"
                  >
                    {job.payment.isReleased ? 'Paid' : 'Pending'}
                  </Badge>
                </HStack>

                {job.status === 'COMPLETED' && !job.payment.isReleased && (
                  <Button
                    colorScheme="primary"
                    onPress={() => navigation.navigate('Payment', { jobId: job.id })}
                  >
                    Complete Payment
                  </Button>
                )}
              </VStack>
            </Box>
          )}

          {/* Action Buttons */}
          <VStack space={3}>
            {job.status === 'COMPLETED' && !job.review && (
              <Button
                colorScheme="success"
                size="lg"
                leftIcon={<Icon as={MaterialIcons} name="star" size="sm" />}
                onPress={() => setShowReviewModal(true)}
              >
                Leave Review
              </Button>
            )}

            {['PENDING', 'ACCEPTED'].includes(job.status) && (
              <Button
                colorScheme="error"
                variant="outline"
                size="lg"
                leftIcon={<Icon as={MaterialIcons} name="cancel" size="sm" />}
                onPress={() => setShowCancelDialog(true)}
              >
                Cancel Job
              </Button>
            )}
          </VStack>

          {/* Bottom spacing */}
          <Box h={4} />
        </VStack>
      </ScrollView>

      {/* Cancel Job Dialog */}
      <AlertDialog
        isOpen={showCancelDialog}
        onClose={() => setShowCancelDialog(false)}
      >
        <AlertDialog.Content>
          <AlertDialog.CloseButton />
          <AlertDialog.Header>Cancel Job</AlertDialog.Header>
          <AlertDialog.Body>
            <VStack space={4}>
              <Text>
                Are you sure you want to cancel this job? Please provide a reason:
              </Text>
              <Select
                placeholder="Select reason"
                onValueChange={setCancelReason}
              >
                <Select.Item label="Changed my mind" value="changed_mind" />
                <Select.Item label="Found another service" value="found_another" />
                <Select.Item label="Emergency resolved" value="emergency_resolved" />
                <Select.Item label="Other" value="other" />
              </Select>
            </VStack>
          </AlertDialog.Body>
          <AlertDialog.Footer>
            <Button.Group space={2}>
              <Button
                variant="unstyled"
                colorScheme="coolGray"
                onPress={() => setShowCancelDialog(false)}
              >
                Cancel
              </Button>
              <Button
                colorScheme="error"
                onPress={handleCancelJob}
                isDisabled={!cancelReason}
              >
                Confirm Cancel
              </Button>
            </Button.Group>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog>
    </Box>
  );
};

export default ModernJobDetailsScreenNB;