// Enhanced implementation of resolveAssetSource to fix the expo-asset module warning

import { Asset } from 'expo-asset';

/**
 * This is a comprehensive replacement for the resolveAssetSource functionality from expo-asset
 * that was causing the warning about not being listed in exports.
 * 
 * It handles various asset source formats including:
 * - require('./image.png') sources (numbers)
 * - Objects with uri property
 * - String URIs
 * - Asset objects from expo-asset
 * - null/undefined values
 */

// Type definitions
interface PackagerAsset {
  __packager_asset: boolean;
  width?: number;
  height?: number;
  scale?: number;
  uri?: string;
}

interface ImageSource {
  uri: string;
  width?: number;
  height?: number;
  scale?: number;
}

interface ResolvedAsset {
  uri: string;
  width: number | null;
  height: number | null;
  scale: number;
}

type AssetSource = number | string | ImageSource | Asset | PackagerAsset | any | null | undefined;

// Cache for resolved assets to improve performance
const assetCache = new Map<string, ResolvedAsset>();

/**
 * Get scale factor based on filename (e.g., <EMAIL> returns 2)
 */
function getScaleFromFilename(filename: string | undefined): number {
  if (!filename) return 1;
  const match = filename.match(/@(\d+(\.\d+)?)x/);
  return match ? parseFloat(match[1]) : 1;
}

/**
 * Get image dimensions from a source if available
 */
function getDimensions(source: AssetSource): { width: number | null; height: number | null } {
  if (!source) return { width: null, height: null };
  
  // For React Native bundled assets
  if (typeof source === 'object' && '__packager_asset' in source && source.__packager_asset) {
    const packagerAsset = source as PackagerAsset;
    return {
      width: packagerAsset.width || null,
      height: packagerAsset.height || null,
    };
  }
  
  // For objects with dimensions
  if (typeof source === 'object' && source && 'width' in source && 'height' in source) {
    return {
      width: source.width || null,
      height: source.height || null,
    };
  }
  
  return { width: null, height: null };
}

/**
 * Resolve an asset source to get its URI and dimensions
 */
export default function resolveAssetSource(source: AssetSource): ResolvedAsset | null {
  if (!source) {
    return null;
  }
  
  // Check cache first
  const cacheKey = typeof source === 'object' ? JSON.stringify(source) : String(source);
  if (assetCache.has(cacheKey)) {
    return assetCache.get(cacheKey) || null;
  }
  
  let result = null;
  
  // Handle objects with uri property (standard React Native image source objects)
  if (typeof source === 'object') {
    if ('uri' in source && source.uri) {
      const imageSource = source as ImageSource;
      result = {
        uri: imageSource.uri,
        width: imageSource.width || null,
        height: imageSource.height || null,
        scale: imageSource.scale || getScaleFromFilename(imageSource.uri) || 1,
      };
    } 
    // Handle Expo Asset objects
    else if ('downloadAsync' in source && 'localUri' in source) {
      const asset = source as Asset;
      result = {
        uri: asset.localUri || asset.uri || '',
        width: asset.width || null,
        height: asset.height || null,
        scale: 1,
      };
    }
  }
  
  // Handle require('./image.png') sources
  else if (typeof source === 'number') {
    // For React Native, we can access the __packager_asset for additional info
    const dimensions = getDimensions(source);
    
    // For native platforms
    const packagerAsset = source as any;
    result = {
        uri: String(source),
        ...dimensions,
        scale: packagerAsset.__packager_asset ? packagerAsset.scale || 1 : 1,
      };
  }
  
  // Handle string URIs
  else if (typeof source === 'string') {
    result = {
      uri: source,
      width: null,
      height: null,
      scale: getScaleFromFilename(source) || 1,
    };
  }
  
  // Cache the result
  if (result) {
    assetCache.set(cacheKey, result);
  }
  
  return result;
}