import React, { useEffect, useState, useCallback, useRef } from 'react';

import {
  announce,
  isScreenReaderEnabled,
  isReduceMotionEnabled,
  createAccessibilityProps,
  createInputA11yProps,
  createButtonA11yProps,
  createCardA11yProps,
  AccessibilityProps
} from '../utils/accessibility';

// AccessibilityInfo for native platforms
// eslint-disable-next-line @typescript-eslint/no-var-requires
const AccessibilityInfo = require('react-native').AccessibilityInfo;

interface UseAccessibilityOptions {
  announceOnMount?: string;
  announceOnUnmount?: string;
}

/**
 * Hook for managing accessibility features in components
 */
export const useAccessibility = (options: UseAccessibilityOptions = {}) => {
  const { announceOnMount, announceOnUnmount } = options;
  const [screenReaderEnabled, setScreenReaderEnabled] = useState(false);
  const [reduceMotionEnabled, setReduceMotionEnabled] = useState(false);
  const focusTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Initialize accessibility state
  useEffect(() => {
    const initializeAccessibility = async () => {
      setScreenReaderEnabled(isScreenReaderEnabled());
      setReduceMotionEnabled(isReduceMotionEnabled());
    };

    initializeAccessibility();

    // Set up listeners for accessibility changes
    const handleScreenReaderChange = (enabled: boolean) => {
      setScreenReaderEnabled(enabled);
    };

    const handleReduceMotionChange = (enabled: boolean) => {
      setReduceMotionEnabled(enabled);
    };

    if (AccessibilityInfo) {
      AccessibilityInfo.addEventListener('screenReaderChanged', handleScreenReaderChange);
      AccessibilityInfo.addEventListener('reduceMotionChanged', handleReduceMotionChange);

      return () => {
        // Check if removeEventListener exists before calling it
        if (AccessibilityInfo.removeEventListener) {
          AccessibilityInfo.removeEventListener('screenReaderChanged', handleScreenReaderChange);
          AccessibilityInfo.removeEventListener('reduceMotionChanged', handleReduceMotionChange);
        }
      };
    }
    return undefined;
  }, []);

  // Handle mount/unmount announcements
  useEffect(() => {
    if (announceOnMount) {
      // Delay announcement to ensure component is fully rendered
      const timer = setTimeout(() => {
        announce(announceOnMount, 'medium');
      }, 500);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [announceOnMount]);

  useEffect(() => {
    return () => {
      if (announceOnUnmount) {
        announce(announceOnUnmount, 'low');
      }
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current);
      }
    };
    // Ensure all code paths return a value
    return undefined;
  }, [announceOnUnmount]);

  /**
   * Announce text to screen readers
   */
  const announceText = useCallback((text: string, priority: 'low' | 'medium' | 'high' = 'medium') => {
    announce(text, priority);
  }, []);

  /**
   * Announce with delay (useful for state changes)
   */
  const announceDelayed = useCallback((text: string, delay: number = 500, priority: 'low' | 'medium' | 'high' = 'medium') => {
    setTimeout(() => {
      announce(text, priority);
    }, delay);
  }, []);

  /**
   * Focus management for accessibility
   */
  const setAccessibilityFocus = useCallback((ref: React.RefObject<{ focus?: () => void }>) => {
    if (ref.current && AccessibilityInfo && AccessibilityInfo.setAccessibilityFocus) {
      AccessibilityInfo.setAccessibilityFocus(ref.current);
    }
  }, []);

  /**
   * Get animation duration based on reduce motion preference
   */
  const getAnimationDuration = useCallback((normalDuration: number, reducedDuration: number = 0) => {
    return reduceMotionEnabled ? reducedDuration : normalDuration;
  }, [reduceMotionEnabled]);

  /**
   * Check if animations should be enabled
   */
  const shouldAnimate = useCallback(() => {
    return !reduceMotionEnabled;
  }, [reduceMotionEnabled]);

  /**
   * Generate accessibility props for form inputs
   */
  const getInputAccessibilityProps = useCallback((config: {
    label: string;
    value?: string;
    placeholder?: string;
    error?: string;
    required?: boolean;
    testID?: string;
  }) => {
    return createInputA11yProps(config);
  }, []);

  /**
   * Generate accessibility props for buttons
   */
  const getButtonAccessibilityProps = useCallback((config: {
    label: string;
    hint?: string;
    disabled?: boolean;
    loading?: boolean;
    pressed?: boolean;
    testID?: string;
  }) => {
    return createButtonA11yProps(config);
  }, []);

  /**
   * Generate accessibility props for cards/list items
   */
  const getCardAccessibilityProps = useCallback((config: {
    title: string;
    subtitle?: string;
    description?: string;
    actionHint?: string;
    testID?: string;
  }) => {
    return createCardA11yProps(config);
  }, []);

  /**
   * Generate general accessibility props
   */
  const getAccessibilityProps = useCallback((config: {
    label: string;
    hint?: string;
    role?: AccessibilityProps['accessibilityRole'];
    state?: AccessibilityProps['accessibilityState'];
    value?: AccessibilityProps['accessibilityValue'];
    actions?: AccessibilityProps['accessibilityActions'];
    testID?: string;
  }) => {
    return createAccessibilityProps(config);
  }, []);

  /**
   * Handle accessibility action events
   */
  const handleAccessibilityAction = useCallback((
    event: { nativeEvent: { actionName: string } },
    actions: Record<string, () => void>
  ) => {
    const actionName = event.nativeEvent.actionName;
    if (actions[actionName]) {
      actions[actionName]();
    }
  }, []);

  return {
    screenReaderEnabled,
    reduceMotionEnabled,
    announceText,
    announceDelayed,
    setAccessibilityFocus,
    getAnimationDuration,
    shouldAnimate,
    getInputAccessibilityProps,
    getButtonAccessibilityProps,
    getCardAccessibilityProps,
    getAccessibilityProps,
    handleAccessibilityAction,
  };
};

export default useAccessibility;
