# CTRON Home Mobile App

A React Native mobile application for the CTRON Home services platform.

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- Yarn package manager
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation

```bash
# Install dependencies
yarn install

# Start the development server
yarn start
```

### Development Scripts

```bash
# Start Expo development server
yarn start

# Run on iOS simulator
yarn ios

# Run on Android emulator
yarn android

# Code quality
yarn lint          # Fix linting issues
yarn lint:check    # Check for linting issues
yarn format        # Format code with Prettier
yarn format:check  # Check code formatting

# Testing
yarn test          # Run tests
yarn test:watch    # Run tests in watch mode
yarn test:coverage # Run tests with coverage

# TypeScript
yarn typecheck     # Check TypeScript types
```

## 🏗️ Project Structure

```
src/
├── api/           # API client and services
├── components/    # Reusable UI components
├── config/        # App configuration
├── context/       # React contexts
├── hooks/         # Custom React hooks
├── navigation/    # Navigation configuration
├── screens/       # Screen components
├── services/      # Business logic services
├── styles/        # Global styles
├── theme/         # Theme configuration
├── types/         # TypeScript type definitions
└── utils/         # Utility functions
```

## 🛠️ Tech Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **Navigation**: React Navigation
- **State Management**: React Context
- **Testing**: Jest
- **Code Quality**: ESLint + Prettier

## 📱 Features

- User authentication (login/signup)
- Home services booking
- Real-time notifications
- Dark/light theme support
- Responsive design
- Accessibility support

## 🔧 Development Guidelines

### Code Style

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Use functional components with hooks
- Implement proper error handling
- Write tests for critical functionality

### Git Workflow

1. Create feature branches from `main`
2. Make small, focused commits
3. Run linting and tests before committing
4. Create pull requests for code review

## 📄 License

This project is proprietary and confidential.