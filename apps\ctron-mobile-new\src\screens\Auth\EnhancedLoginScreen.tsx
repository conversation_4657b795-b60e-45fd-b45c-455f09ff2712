// Enhanced CTRON Login Screen - Demonstrating UI Improvements
// This is an example implementation showing the enhanced components in action

import React, { useState, useRef, useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';

import { 
  View, 
  Text, 
  ScrollView, 
  Dimensions, 
  StatusBar, 
  KeyboardAvoidingView, 
  Platform, 
  SafeAreaView,
  Animated,
  Easing
} from '../../utils/platformUtils';
import { ctronStyles, ctronColors, ctronSpacing } from '../../styles/ctronDesignSystem';
import AuthAPI from '../../api/auth.api';
import { useAuth } from '../../context/AuthContext';
import type { AuthStackParamList } from '../../navigation/AuthStack';

// Enhanced components
import EnhancedInput from '../../components/auth/EnhancedInput';
import EnhancedButton from '../../components/auth/EnhancedButton';

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

export default function EnhancedLoginScreen() {
  const { login } = useAuth();
  const navigation = useNavigation<StackNavigationProp<AuthStackParamList, 'Login'>>();

  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [touched, setTouched] = useState<{ email: boolean; password: boolean }>({
    email: false,
    password: false,
  });

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const logoScaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.spring(logoScaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Validation functions
  const validateEmail = (value: string): string => {
    if (!value.trim()) {
      return 'Email is required';
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return 'Please enter a valid email address';
    }
    return '';
  };
  
  const validatePassword = (value: string): string => {
    if (!value) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return '';
  };
  
  const validateForm = (): boolean => {
    const emailError = validateEmail(email);
    const passwordError = validatePassword(password);
    
    const newErrors: FormErrors = {
      email: emailError,
      password: passwordError,
    };
    
    setErrors(newErrors);
    return !emailError && !passwordError;
  };
  
  // Real-time validation handlers
  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (touched.email) {
      const emailError = validateEmail(value);
      setErrors(prev => ({ ...prev, email: emailError }));
    }
  };
  
  const handlePasswordChange = (value: string) => {
    setPassword(value);
    if (touched.password) {
      const passwordError = validatePassword(value);
      setErrors(prev => ({ ...prev, password: passwordError }));
    }
  };

  const handleEmailBlur = () => {
    setTouched(prev => ({ ...prev, email: true }));
    const emailError = validateEmail(email);
    setErrors(prev => ({ ...prev, email: emailError }));
  };

  const handlePasswordBlur = () => {
    setTouched(prev => ({ ...prev, password: true }));
    const passwordError = validatePassword(password);
    setErrors(prev => ({ ...prev, password: passwordError }));
  };

  const handleLogin = async () => {
    setErrors({});

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      const res = await AuthAPI.login({ email, password });
      const { accessToken } = res;
      if (!accessToken) throw new Error('No token returned from server');
      await login(accessToken);
    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string } }; message?: string };
      const errorMessage = error.response?.data?.message || error.message || 'Login failed';
      setErrors({ general: errorMessage });
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const handleSignUp = () => {
    navigation.navigate('Signup');
  };

  return (
    <SafeAreaView style={ctronStyles.container}>
      <StatusBar barStyle="light-content" backgroundColor={ctronColors.background} />
      <KeyboardAvoidingView
        style={ctronStyles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={{ 
            flexGrow: 1, 
            justifyContent: 'center',
            minHeight: Dimensions.get('window').height - 100
          }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Animated.View 
            style={[
              ctronStyles.safeContainer, 
              { 
                paddingHorizontal: ctronSpacing.lg,
                justifyContent: 'center',
                flex: 1,
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }
            ]}
          >
            {/* Enhanced Header Section */}
            <View style={{ alignItems: 'center', paddingVertical: ctronSpacing.xl }}>
              {/* Enhanced CTRON Logo with Animation */}
              <Animated.View 
                style={[
                  ctronStyles.logo,
                  {
                    transform: [{ scale: logoScaleAnim }],
                    shadowColor: ctronColors.primary,
                    shadowOffset: { width: 0, height: 8 },
                    shadowOpacity: 0.3,
                    shadowRadius: 16,
                    elevation: 8,
                  }
                ]}
              >
                <Text style={ctronStyles.logoText}>C</Text>
              </Animated.View>
              
              <Text style={[
                ctronStyles.brandName, 
                { 
                  marginBottom: ctronSpacing.lg,
                  textShadowColor: 'rgba(146, 115, 236, 0.3)',
                  textShadowOffset: { width: 0, height: 2 },
                  textShadowRadius: 4,
                }
              ]}>
                CTRON
              </Text>
              
              <Text style={[
                ctronStyles.welcomeTitle, 
                { marginBottom: ctronSpacing.xl }
              ]}>
                Welcome Back
              </Text>
              
              <Text style={[
                ctronStyles.welcomeSubtitle,
                { marginBottom: ctronSpacing.lg }
              ]}>
                Sign in to continue to your account
              </Text>
            </View>

            {/* Enhanced Form Section */}
            <View style={{ paddingHorizontal: ctronSpacing.sm }}>
              {/* Enhanced Email Input */}
              <EnhancedInput
                label="Email Address"
                value={email}
                onChangeText={handleEmailChange}
                onBlur={handleEmailBlur}
                error={errors.email}
                keyboardType="email-address"
                autoCapitalize="none"
                placeholder="Enter your email"
                accessibilityLabel="Email address input"
                accessibilityHint="Enter your email address to sign in"
              />

              {/* Enhanced Password Input */}
              <EnhancedInput
                label="Password"
                value={password}
                onChangeText={handlePasswordChange}
                onBlur={handlePasswordBlur}
                error={errors.password}
                secureTextEntry={true}
                showPasswordToggle={true}
                placeholder="Enter your password"
                accessibilityLabel="Password input"
                accessibilityHint="Enter your password to sign in"
              />

              {/* Forgot Password Link */}
              <View style={{ alignItems: 'flex-end', marginBottom: ctronSpacing.lg }}>
                <EnhancedButton
                  title="Forgot Password?"
                  onPress={handleForgotPassword}
                  variant="outline"
                  size="small"
                  accessibilityLabel="Forgot password"
                  accessibilityHint="Tap to reset your password"
                  style={{ borderWidth: 0, backgroundColor: 'transparent' }}
                />
              </View>

              {/* Enhanced Login Button */}
              <EnhancedButton
                title="Sign In"
                onPress={handleLogin}
                loading={loading}
                loadingText="Signing in..."
                variant="primary"
                size="large"
                icon="🔐"
                accessibilityLabel="Sign in to your account"
                accessibilityHint="Tap to sign in with your credentials"
                style={{ marginBottom: ctronSpacing.lg }}
              />

              {/* General Error Display */}
              {errors.general && (
                <Animated.View 
                  style={[
                    ctronStyles.errorContainer,
                    {
                      backgroundColor: 'rgba(255, 78, 78, 0.1)',
                      borderRadius: 8,
                      padding: ctronSpacing.md,
                      marginBottom: ctronSpacing.lg,
                      borderLeftWidth: 4,
                      borderLeftColor: ctronColors.error,
                    }
                  ]}
                >
                  <Text 
                    style={[
                      ctronStyles.errorText,
                      { textAlign: 'center' }
                    ]}
                    accessibilityRole="alert"
                    accessibilityLiveRegion="polite"
                  >
                    ⚠️ {errors.general}
                  </Text>
                </Animated.View>
              )}

              {/* Divider */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginVertical: ctronSpacing.lg,
              }}>
                <View style={{
                  flex: 1,
                  height: 1,
                  backgroundColor: ctronColors.inputBorder,
                }} />
                <Text style={{
                  marginHorizontal: ctronSpacing.md,
                  color: ctronColors.textSecondary,
                  fontSize: 14,
                }}>
                  or
                </Text>
                <View style={{
                  flex: 1,
                  height: 1,
                  backgroundColor: ctronColors.inputBorder,
                }} />
              </View>

              {/* Social Login Buttons */}
              <View style={{ marginBottom: ctronSpacing.lg }}>
                <EnhancedButton
                  title="Continue with Google"
                  onPress={() => console.log('Google login')}
                  variant="secondary"
                  size="medium"
                  icon="🔍"
                  accessibilityLabel="Sign in with Google"
                  style={{ marginBottom: ctronSpacing.md }}
                />
                
                <EnhancedButton
                  title="Continue with Apple"
                  onPress={() => console.log('Apple login')}
                  variant="secondary"
                  size="medium"
                  icon="🍎"
                  accessibilityLabel="Sign in with Apple"
                />
              </View>

              {/* Sign Up Link */}
              <View style={[
                ctronStyles.row, 
                ctronStyles.centerContent, 
                { marginTop: ctronSpacing.lg, paddingBottom: ctronSpacing.md }
              ]}>
                <Text style={{ 
                  color: ctronColors.textSecondary, 
                  fontSize: 14 
                }}>
                  Don't have an account? 
                </Text>
                <EnhancedButton
                  title="Sign Up"
                  onPress={handleSignUp}
                  variant="outline"
                  size="small"
                  accessibilityLabel="Create new account"
                  accessibilityHint="Tap to create a new account"
                  style={{ 
                    borderWidth: 0, 
                    backgroundColor: 'transparent',
                    height: 'auto',
                    paddingVertical: 4,
                  }}
                />
              </View>
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

// Additional styles for enhanced components
const enhancedStyles = {
  socialButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: ctronColors.backgroundSecondary,
    borderWidth: 1,
    borderColor: ctronColors.inputBorder,
    borderRadius: 12,
    paddingVertical: ctronSpacing.md,
    marginBottom: ctronSpacing.sm,
  },
  socialIcon: {
    fontSize: 18,
    marginRight: ctronSpacing.sm,
  },
  socialText: {
    color: ctronColors.textPrimary,
    fontSize: 16,
    fontWeight: '500' as const,
  },
};