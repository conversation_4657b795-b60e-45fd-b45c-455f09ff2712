generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String    @id @default(uuid())
  email       String    @unique
  fullName    String
  password    String
  phone       String?
  role        Role
  lastLoginAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 🔁 Relations
  technician       Technician?
  jobs             Job[]             @relation("JobUser")
  reviews          Review[]
  payments         Payment[]
  chatParticipants ChatParticipant[]
  sentMessages     Message[]         @relation("MessageSender")
  readMessages     MessageRead[]     @relation("MessageReadBy")
  pushTokens       PushToken[]
  notifications    Notification[]
  refreshTokens    RefreshToken[]
}

model Technician {
  id             String   @id @default(uuid())
  userId         String   @unique
  specialization String
  isAvailable    Boolean  @default(true)
  rating         Float?
  kycStatus           String   @default("PENDING")

  // ✅ New fields for qualification verification
  qualificationFile   String?     // URL to uploaded certificate (e.g. Cloudinary or S3)
  qualificationStatus String      @default("PENDING") // Can be: 'PENDING', 'APPROVED', 'REJECTED'

  latitude       Float?
  longitude      Float?
  createdAt      DateTime @default(now())

  // 🔁 Relations
  user    User     @relation(fields: [userId], references: [id])
  jobs    Job[]    @relation("JobTechnician")
  reviews Review[]
}

model Job {
  id                String     @id @default(uuid())
  issue             String
  description       String?
  priority          String?    @default("medium")
  latitude          Float?
  longitude         Float?
  photoUrl          String?
  proofImageKey     String?
  status            JobStatus  @default(PENDING)
  scheduledAt       DateTime
  createdAt         DateTime   @default(now())
  completedAt       DateTime?
  confirmedAt       DateTime?
  cancelledAt       DateTime?
  cancellationReason String?

  // 🔁 Relations
  userId          String
  user            User        @relation("JobUser", fields: [userId], references: [id])

  technicianId    String?
  technician      Technician? @relation("JobTechnician", fields: [technicianId], references: [id])

  review          Review?
  payment         Payment?
  chat            Chat?
}

model Payment {
  id                    String   @id @default(uuid())
  jobId                 String   @unique
  userId                String   // ✅ moved up for clarity
  amount                Float
  currency              String   @default("GBP")
  stripePaymentIntentId String   @unique
  isReleased            Boolean  @default(false)
  releasedAt            DateTime?
  isFrozen              Boolean  @default(false)
  freezeReason          String?
  createdAt             DateTime @default(now())

  // 🔁 Relations
  job   Job  @relation(fields: [jobId], references: [id])
  user  User @relation(fields: [userId], references: [id])
}

model Review {
  id            String     @id @default(uuid())
  userId        String
  user          User       @relation(fields: [userId], references: [id])

  jobId         String     @unique
  job           Job        @relation(fields: [jobId], references: [id])

  rating        Int
  comment       String?
  createdAt     DateTime   @default(now())

  technicianId  String?
  technician    Technician? @relation(fields: [technicianId], references: [id])
}

model Chat {
  id           String            @id @default(uuid())
  jobId        String            @unique
  status       ChatStatus        @default(ACTIVE)
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt

  // 🔁 Relations
  job          Job               @relation(fields: [jobId], references: [id])
  participants ChatParticipant[]
  messages     Message[]
}

model ChatParticipant {
  id       String   @id @default(uuid())
  chatId   String
  userId   String
  role     Role
  joinedAt DateTime @default(now())

  // 🔁 Relations
  chat     Chat     @relation(fields: [chatId], references: [id])
  user     User     @relation(fields: [userId], references: [id])

  @@unique([chatId, userId])
}

model Message {
  id          String   @id @default(uuid())
  chatId      String
  senderId    String
  content     String
  attachments String?
  read        Boolean  @default(false)
  createdAt   DateTime @default(now())

  // 🔁 Relations
  chat        Chat     @relation(fields: [chatId], references: [id])
  sender      User     @relation("MessageSender", fields: [senderId], references: [id])
  readBy      MessageRead[]
}

model MessageRead {
  id        String   @id @default(uuid())
  messageId String
  userId    String
  readAt    DateTime @default(now())
  createdAt DateTime @default(now())

  // 🔁 Relations
  message   Message  @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user      User     @relation("MessageReadBy", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([messageId, userId])
  @@index([messageId])
  @@index([userId])
}

model Setting {
  id               String   @id @default(cuid())
  gracePeriodHours Int      @default(24)
  stripeTestMode   Boolean  @default(false)
  statusMessage    String   @default("System operational")
  updatedAt        DateTime @updatedAt
}

model PushToken {
  id         String   @id @default(uuid())
  userId     String
  token      String   @unique
  isActive   Boolean  @default(true)
  deviceInfo String?  // JSON string with device details
  lastUsed   DateTime @default(now())
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // 🔁 Relations
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
}

model Notification {
  id        String            @id @default(uuid())
  userId    String
  title     String
  body      String
  data      String?           // JSON string with additional data
  type      NotificationType  @default(PUSH)
  status    NotificationStatus @default(PENDING)
  isRead    Boolean           @default(false)
  readAt    DateTime?
  sentAt    DateTime?
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  // 🔁 Relations
  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
}

model RefreshToken {
  id        String   @id @default(uuid())
  userId    String
  token     String   @unique // Hashed refresh token
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 🔁 Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
  @@index([expiresAt])
}

enum Role {
  HOMEOWNER
  TECHNICIAN
}

enum JobStatus {
  PENDING
  ACCEPTED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum ChatStatus {
  ACTIVE
  CLOSED
  ARCHIVED
}

enum NotificationType {
  PUSH
  EMAIL
  SMS
}

enum NotificationStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
}
