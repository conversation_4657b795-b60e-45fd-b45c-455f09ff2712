// TypeError Prevention Utilities
// Comprehensive utilities to prevent common TypeError issues

/**
 * Safe array operations to prevent TypeError on undefined/null arrays
 */
export const safeArray = {
  /**
   * Safely filter an array, returns empty array if input is null/undefined
   */
  filter: <T>(array: T[] | null | undefined, predicate: (item: T) => boolean): T[] => {
    if (!Array.isArray(array)) return [];
    try {
      return array.filter(predicate);
    } catch (error) {
      // Safe array filter error
      return [];
    }
  },

  /**
   * Safely map an array, returns empty array if input is null/undefined
   */
  map: <T, U>(array: T[] | null | undefined, mapper: (item: T) => U): U[] => {
    if (!Array.isArray(array)) return [];
    try {
      return array.map(mapper);
    } catch (error) {
      // Safe array map error
      return [];
    }
  },

  /**
   * Safely sort an array, returns empty array if input is null/undefined
   */
  sort: <T>(array: T[] | null | undefined, compareFn?: (a: T, b: T) => number): T[] => {
    if (!Array.isArray(array)) return [];
    try {
      return [...array].sort(compareFn);
    } catch (error) {
      // Safe array sort error
      return [];
    }
  },

  /**
   * Safely get array length, returns 0 if input is null/undefined
   */
  length: (array: unknown[] | null | undefined): number => {
    return Array.isArray(array) ? array.length : 0;
  },

  /**
   * Safely slice an array, returns empty array if input is null/undefined
   */
  slice: <T>(array: T[] | null | undefined, start?: number, end?: number): T[] => {
    if (!Array.isArray(array)) return [];
    try {
      return array.slice(start, end);
    } catch (error) {
      // Safe array slice error
      return [];
    }
  }
};

/**
 * Safe string operations to prevent TypeError on undefined/null strings
 */
export const safeString = {
  /**
   * Safely slice a string, returns fallback if input is null/undefined
   */
  slice: (str: string | null | undefined, start: number, end?: number, fallback = ''): string => {
    if (typeof str !== 'string') return fallback;
    try {
      return str.slice(start, end);
    } catch (error) {
      // Safe string slice error
      return fallback;
    }
  },

  /**
   * Safely split a string, returns empty array if input is null/undefined
   */
  split: (str: string | null | undefined, separator: string): string[] => {
    if (typeof str !== 'string') return [];
    try {
      return str.split(separator);
    } catch (error) {
      // Safe string split error
      return [];
    }
  },

  /**
   * Safely get string length, returns 0 if input is null/undefined
   */
  length: (str: string | null | undefined): number => {
    return typeof str === 'string' ? str.length : 0;
  },

  /**
   * Safely convert to lowercase, returns fallback if input is null/undefined
   */
  toLowerCase: (str: string | null | undefined, fallback = ''): string => {
    if (typeof str !== 'string') return fallback;
    try {
      return str.toLowerCase();
    } catch (error) {
      // Safe string toLowerCase error
      return fallback;
    }
  }
};

/**
 * Safe object operations to prevent TypeError on undefined/null objects
 */
export const safeObject = {
  /**
   * Safely get object property with fallback
   */
  get: <T>(obj: unknown, path: string, fallback: T): T => {
    if (!obj || typeof obj !== 'object') return fallback;
    try {
      const keys = path.split('.');
      let result = obj;
      for (const key of keys) {
        if (result == null || typeof result !== 'object') return fallback;
        result = (result as any)[key];
      }
      return result !== undefined ? result as T : fallback;
    } catch (error) {
      // Safe object get error
      return fallback;
    }
  },

  /**
   * Safely check if object has property
   */
  has: (obj: unknown, property: string): boolean => {
    if (!obj || typeof obj !== 'object') return false;
    try {
      return Object.prototype.hasOwnProperty.call(obj, property);
    } catch (error) {
      // Safe object has error
      return false;
    }
  },

  /**
   * Safely get object keys, returns empty array if input is null/undefined
   */
  keys: (obj: unknown): string[] => {
    if (!obj || typeof obj !== 'object') return [];
    try {
      return Object.keys(obj);
    } catch (error) {
      // Safe object keys error
      return [];
    }
  }
};

/**
 * Safe function execution to prevent TypeError on undefined functions
 */
export const safeFunction = {
  /**
   * Safely call a function, returns fallback if function is null/undefined
   */
  call: <T>(fn: ((...args: unknown[]) => unknown) | null | undefined, fallback: T, ...args: unknown[]): T => {
    if (typeof fn !== 'function') return fallback;
    try {
      return fn(...args) as T;
    } catch (error) {
      // Safe function call error
      return fallback;
    }
  },

  /**
   * Safely call an async function, returns fallback promise if function is null/undefined
   */
  callAsync: async <T>(fn: ((...args: unknown[]) => unknown) | null | undefined, fallback: T, ...args: unknown[]): Promise<T> => {
    if (typeof fn !== 'function') return Promise.resolve(fallback);
    try {
      const result = await fn(...args);
      return result as T;
    } catch (error) {
      // Safe async function call error
      return fallback;
    }
  }
};

/**
 * Type guard utilities
 */
export const typeGuards = {
  isString: (value: unknown): value is string => typeof value === 'string',
  isNumber: (value: unknown): value is number => typeof value === 'number' && !isNaN(value),
  isArray: (value: unknown): value is unknown[] => Array.isArray(value),
  isObject: (value: unknown): value is object => value !== null && typeof value === 'object' && !Array.isArray(value),
  isFunction: (value: unknown): value is ((...args: unknown[]) => unknown) => typeof value === 'function',
  isNotNull: <T>(value: T | null | undefined): value is T => value != null,
};

/**
 * Enhanced error handling for TypeError prevention
 */
export const errorSafe = {
  /**
   * Wrap any operation in try-catch and return fallback on error
   */
  wrap: <T>(operation: () => T, fallback: T): T => {
    try {
      return operation();
    } catch (error) {
      if (error instanceof TypeError) {
        // TypeError prevented
      } else {
        // Error prevented
      }
      return fallback;
    }
  },

  /**
   * Wrap async operation in try-catch and return fallback on error
   */
  wrapAsync: async <T>(operation: () => Promise<T>, fallback: T): Promise<T> => {
    try {
      return await operation();
    } catch (error) {
      if (error instanceof TypeError) {
        // Async TypeError prevented
      } else {
        // Async error prevented
      }
      return fallback;
    }
  }
};

// Export all utilities as a single object for convenience
export const TypeErrorPrevention = {
  safeArray,
  safeString,
  safeObject,
  safeFunction,
  typeGuards,
  errorSafe
};

export default TypeErrorPrevention;
