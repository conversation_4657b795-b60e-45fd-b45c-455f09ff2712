// src/controllers/user.controller.ts

import { Request, Response, RequestHandler } from 'express';
import { AuthenticatedRequest } from '../middleware/auth.middleware';
import { prisma } from '../config/db';

export const UserController = {
  savePushToken: async (req: Request, res: Response): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;
      const { pushToken } = req.body;

      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      if (!pushToken) {
        res.status(400).json({ message: 'Push token is required' });
        return;
      }

      // Store the push token in the PushToken table instead
      await prisma.pushToken.create({
        data: {
          token: pushToken,
          userId: user.userId,
          isActive: true,
          deviceInfo: JSON.stringify({ type: 'MOBILE', platform: req.headers['user-agent'] || 'unknown' }),
        },
      });

      res.status(200).json({ success: true });
    } catch (error) {
      console.error('❌ Error saving push token:', error);
      res.status(500).json({ message: 'Failed to save push token' });
    }
  },
  getProfile: async (req: Request, res: Response): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;

      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      const profile = await prisma.user.findUnique({
        where: { id: user.userId },
        select: { id: true, fullName: true, email: true, phone: true, role: true },
      });

      res.status(200).json({ profile });
    } catch (error) {
      console.error('❌ Error fetching profile:', error);
      res.status(500).json({ message: 'Failed to retrieve profile' });
    }
  },

  updateProfile: async (req: Request, res: Response): Promise<void> => {
    try {
      const { user } = req as AuthenticatedRequest;
      const { fullName, phone } = req.body;

      if (!user) {
        res.status(401).json({ message: 'Unauthorized' });
        return;
      }

      const updated = await prisma.user.update({
        where: { id: user.userId },
        data: { fullName, phone },
      });

      res.status(200).json({ user: updated });
    } catch (error) {
      console.error('❌ Error updating profile:', error);
      res.status(500).json({ message: 'Failed to update profile' });
    }
  },
};
