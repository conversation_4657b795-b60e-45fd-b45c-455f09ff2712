// CTRON Home - Request Validation Middleware
// Validates request data using Zod schemas

import { Request, Response, NextFunction } from 'express';
import { ZodSchema, ZodError } from 'zod';
import { logger } from '../utils/logger';
import { sendValidationError } from '../utils/apiResponse';

/**
 * Middleware factory for validating requests with Zod schemas
 */
export function validateRequest(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Validate the request data
      const validatedData = schema.parse({
        body: req.body,
        query: req.query,
        params: req.params
      });

      // Replace request data with validated data
      if (validatedData.body) {
        req.body = validatedData.body;
      }
      if (validatedData.query) {
        // Use Object.assign to safely update query parameters
        Object.assign(req.query, validatedData.query);
      }
      if (validatedData.params) {
        Object.assign(req.params, validatedData.params);
      }

      next();
    } catch (error) {
      if (error instanceof ZodError) {
        logger.warn('Request validation failed:', {
          path: req.path,
          method: req.method,
          errors: error.errors
        });

        sendValidationError(
          res,
          'Validation failed',
          error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          })),
          (req as any).requestId
        );
        return;
      }

      logger.error('Validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal validation error'
      });
      return;
    }
  };
}

/**
 * Middleware for validating only request body
 */
export function validateBody(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validatedBody = schema.parse(req.body);
      req.body = validatedBody;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        logger.warn('Body validation failed:', {
          path: req.path,
          method: req.method,
          errors: error.errors
        });

        sendValidationError(
          res,
          'Body validation failed',
          error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          })),
          (req as any).requestId
        );
        return;
      }

      logger.error('Body validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal validation error'
      });
      return;
    }
  };
}

/**
 * Middleware for validating only query parameters
 */
export function validateQuery(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validatedQuery = schema.parse(req.query);
      req.query = validatedQuery;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        logger.warn('Query validation failed:', {
          path: req.path,
          method: req.method,
          errors: error.errors
        });

        res.status(400).json({
          success: false,
          message: 'Query validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))
        });
        return;
      }

      logger.error('Query validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal validation error'
      });
      return;
    }
  };
}

/**
 * Middleware for validating only route parameters
 */
export function validateParams(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validatedParams = schema.parse(req.params);
      req.params = validatedParams;
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        logger.warn('Params validation failed:', {
          path: req.path,
          method: req.method,
          errors: error.errors
        });

        res.status(400).json({
          success: false,
          message: 'Parameters validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))
        });
        return;
      }

      logger.error('Params validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal validation error'
      });
      return;
    }
  };
}
