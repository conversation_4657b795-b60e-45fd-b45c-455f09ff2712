// CTRON Home - Modern Edit Job Screen
// Enhanced job editing interface with improved form design and validation

import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import type { RouteProp } from '@react-navigation/native';
import { useNavigation, useRoute } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState, useEffect, useMemo, useRef } from 'react';

import { JobAPI, JobCreatePayload } from '../../api/job.api';
import { Header } from '../../components/ui/Header';
import { useTheme } from '../../context/ThemeContext';
import { Button, Card, Input } from '../../design-system';
import type { HomeownerStackParamList } from '../../navigation/types';
import type { Job } from '../../types/job';
import { 
  Alert, 
  StyleSheet, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  View, 
  ScrollView, 
  ActivityIndicator,
  Animated,
  Dimensions,
  StatusBar,
  Platform,
  KeyboardAvoidingView
} from '../../utils/platformUtils';

type JobUpdatePayload = {
  issue?: string;
  description?: string;
  scheduledAt?: string;
  urgency?: string;
  address?: string;
};

type EditJobScreenNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'EditJob'>;
type EditJobScreenRouteProp = RouteProp<HomeownerStackParamList, 'EditJob'>;

interface FormData {
  issue: string;
  description: string;
  scheduledAt: Date;
  urgency: string;
  address: string;
}

interface FormErrors {
  issue?: string;
  description?: string;
  scheduledAt?: string;
  urgency?: string;
  address?: string;
}

const { width: screenWidth } = Dimensions.get('window');

interface UrgencyLevel {
  id: string;
  label: string;
  description: string;
  color: string;
  icon: keyof typeof Ionicons.glyphMap;
}

const urgencyLevels: UrgencyLevel[] = [
  { id: 'low', label: 'Low', description: 'Within a week', color: '#8E8E93', icon: 'time-outline' },
  { id: 'medium', label: 'Medium', description: 'Within 2-3 days', color: '#FF9500', icon: 'alert-circle-outline' },
  { id: 'high', label: 'High', description: 'Within 24 hours', color: '#FF3B30', icon: 'warning-outline' },
  { id: 'urgent', label: 'Emergency', description: 'ASAP', color: '#FF3B30', icon: 'flash-outline' }
];

export default function ModernEditJobScreen() {
  const navigation = useNavigation<EditJobScreenNavigationProp>();
  const route = useRoute<EditJobScreenRouteProp>();
  const { colors, spacing, typography, borderRadius } = useTheme();
  const { jobId } = route.params;

  // State
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    issue: '',
    description: '',
    scheduledAt: new Date(),
    urgency: 'medium',
    address: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadJobDetails();
    
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const loadJobDetails = async () => {
    try {
      const jobData = await JobAPI.getJobById(jobId);
      setJob(jobData);
      setFormData({
        issue: jobData.issue,
        description: jobData.description || '',
        scheduledAt: new Date(jobData.scheduledAt),
        urgency: jobData.priority || 'medium',
        address: jobData.address || ''
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to load job details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.issue.trim()) {
      newErrors.issue = 'Issue title is required';
    } else if (formData.issue.trim().length < 5) {
      newErrors.issue = 'Issue title must be at least 5 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }

    if (formData.scheduledAt < new Date()) {
      newErrors.scheduledAt = 'Scheduled time must be in the future';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      // Shake animation for errors
      Animated.sequence([
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 0, duration: 100, useNativeDriver: true })
      ]).start();
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setSaving(true);
    try {
      const updateData = {
        issue: formData.issue,
        description: formData.description,
        scheduledAt: formData.scheduledAt.toISOString(),
        priority: formData.urgency as 'low' | 'medium' | 'high' | 'urgent'
      };

      await JobAPI.updateJob(jobId, updateData);
      Alert.alert(
        'Success!', 
        'Your job has been updated successfully.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update the job. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      Alert.alert(
        'Discard Changes?',
        'You have unsaved changes. Are you sure you want to go back?',
        [
          { text: 'Keep Editing', style: 'cancel' },
          { text: 'Discard', style: 'destructive', onPress: () => navigation.goBack() }
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  const updateFormData = (field: keyof FormData, value: string | Date) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const UrgencyCard = ({ urgencyLevel, isSelected, onPress }: {
    urgencyLevel: typeof urgencyLevels[0];
    isSelected: boolean;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      style={[styles.urgencyCard, isSelected && styles.selectedUrgencyCard]}
      onPress={onPress}
    >
      <View style={styles.urgencyCardGlass}>
              <BlurView intensity={15} />
            </View>
      <View style={styles.urgencyCardContent}>
        <View style={[styles.urgencyIcon, { backgroundColor: urgencyLevel.color + '20' }]}>
          <Ionicons name={urgencyLevel.icon} size={18} color={urgencyLevel.color} />
        </View>
        <View style={styles.urgencyContent}>
          <Text style={[styles.urgencyLabel, isSelected && styles.selectedUrgencyLabel]}>
            {urgencyLevel.label}
          </Text>
          <Text style={styles.urgencyDescription}>{urgencyLevel.description}</Text>
        </View>
        {isSelected && (
          <View style={styles.selectedIndicator}>
            <Ionicons name="checkmark-circle" size={18} color={colors.primary.main} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    gradientBackground: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: 250,
    },
    header: {
      paddingTop: StatusBar.currentHeight || 44,
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.md,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    saveButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      backgroundColor: colors.primary.main,
      borderRadius: borderRadius.md,
    },
    saveButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.inverse,
    },
    keyboardView: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.xl,
    },
    formCard: {
      marginBottom: spacing.xl,
    },
    formCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.xl,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.15)',
    },
    formCardContent: {
      padding: spacing.xl,
    },
    formTitle: {
      fontSize: typography.fontSize.xxl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    formSubtitle: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginBottom: spacing.xl,
      lineHeight: 22,
    },
    inputContainer: {
      marginBottom: spacing.xl,
    },
    inputLabel: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    requiredIndicator: {
      color: colors.destructive,
    },
    inputWrapper: {
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderRadius: borderRadius.md,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    inputWrapperError: {
      borderColor: colors.destructive,
      backgroundColor: colors.destructive + '10',
    },
    inputWrapperFocused: {
      borderColor: colors.primary.main,
      backgroundColor: 'rgba(255, 255, 255, 0.12)',
    },
    input: {
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      minHeight: 48,
    },
    textArea: {
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      minHeight: 100,
      textAlignVertical: 'top',
    },
    inputIcon: {
      position: 'absolute',
      right: spacing.md,
      top: '50%',
      marginTop: -10,
    },
    errorText: {
      fontSize: typography.fontSize.sm,
      color: colors.destructive,
      marginTop: spacing.xs,
      marginLeft: spacing.sm,
    },
    characterCount: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      textAlign: 'right',
      marginTop: spacing.xs,
    },
    urgencyContainer: {
      marginBottom: spacing.xl,
    },
    urgencyGrid: {
      gap: spacing.sm,
    },
    urgencyCard: {
      marginBottom: spacing.sm,
    },
    selectedUrgencyCard: {
      transform: [{ scale: 1.02 }],
    },
    urgencyCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.md,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    urgencyCardContent: {
      padding: spacing.md,
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
    },
    urgencyIcon: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    urgencyContent: {
      flex: 1,
    },
    urgencyLabel: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    selectedUrgencyLabel: {
      color: colors.primary.main,
    },
    urgencyDescription: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    selectedIndicator: {
      position: 'absolute',
      top: spacing.sm,
      right: spacing.sm,
    },
    dateTimeContainer: {
      marginBottom: spacing.xl,
    },
    dateTimeRow: {
      flexDirection: 'row',
      gap: spacing.md,
    },
    dateTimeButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderRadius: borderRadius.md,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
      minHeight: 48,
    },
    dateTimeButtonError: {
      borderColor: colors.destructive,
      backgroundColor: colors.destructive + '10',
    },
    dateTimeIcon: {
      marginRight: spacing.sm,
    },
    dateTimeText: {
      flex: 1,
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
    },
    dateTimePlaceholder: {
      color: colors.text.secondary,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginTop: spacing.md,
    },
    footer: {
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.lg,
      paddingTop: spacing.md,
      backgroundColor: colors.background.primary,
      borderTopWidth: 1,
      borderTopColor: 'rgba(255, 255, 255, 0.1)',
    },
    footerButtons: {
      flexDirection: 'row',
      gap: spacing.md,
    },
    cancelButton: {
      flex: 1,
    },
    saveButtonFooter: {
      flex: 2,
    },
    warningCard: {
      marginBottom: spacing.xl,
    },
    warningCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 149, 0, 0.1)',
      borderWidth: 1,
      borderColor: 'rgba(255, 149, 0, 0.3)',
    },
    warningCardContent: {
      padding: spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
    },
    warningIcon: {
      marginRight: spacing.md,
    },
    warningContent: {
      flex: 1,
    },
    warningTitle: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: '#FF9500',
      marginBottom: spacing.xs,
    },
    warningText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      lineHeight: 20,
    },
  }), [colors, spacing, typography, borderRadius]);

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.gradientBackground}>
          <LinearGradient
            colors={['#667eea', '#764ba2', '#f093fb']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary.main} />
          <Text style={styles.loadingText}>Loading job details...</Text>
        </View>
      </View>
    );
  }

  if (!job) {
    return (
      <View style={styles.container}>
        <Header 
          title="Edit Job" 
          leftAction={{
            icon: <Text>←</Text>,
            onPress: () => navigation.goBack(),
            accessibilityLabel: "Go back"
          }} 
        />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Job not found</Text>
        </View>
      </View>
    );
  }

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }, { translateX: shakeAnim }]
        }
      ]}
    >
      <View style={styles.gradientBackground}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={handleCancel}>
            <Ionicons name="arrow-back" size={20} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Job</Text>
          <TouchableOpacity 
            style={styles.saveButton} 
            onPress={handleSave}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color={colors.text.inverse} />
            ) : (
              <Text style={styles.saveButtonText}>Save</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>

      <KeyboardAvoidingView 
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.content}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Warning Card */}
          {job.status !== 'PENDING' && (
            <View style={styles.warningCard}>
              <View style={styles.warningCardGlass}>
              <BlurView intensity={15} />
            </View>
              <View style={styles.warningCardContent}>
                <Ionicons 
                  name="warning-outline" 
                  size={24} 
                  color="#FF9500" 
                  style={styles.warningIcon}
                />
                <View style={styles.warningContent}>
                  <Text style={styles.warningTitle}>Limited Editing</Text>
                  <Text style={styles.warningText}>
                    Some changes may not be possible as the job is already {job.status.replace('_', ' ')}.
                  </Text>
                </View>
              </View>
            </View>
          )}

          {/* Form Card */}
          <View style={styles.formCard}>
            <View style={styles.formCardGlass}>
            <BlurView intensity={20} />
          </View>
            <View style={styles.formCardContent}>
              <Text style={styles.formTitle}>Job Details</Text>
              <Text style={styles.formSubtitle}>
                Update your job information. Make sure all details are accurate.
              </Text>

              {/* Issue Title */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                  Issue Title <Text style={styles.requiredIndicator}>*</Text>
                </Text>
                <View style={[
                  styles.inputWrapper,
                  errors.issue && styles.inputWrapperError
                ]}>
                  <TextInput
                    style={styles.input}
                    placeholder="Brief description of the issue"
                    placeholderTextColor={colors.text.secondary}
                    value={formData.issue}
                    onChangeText={(text: string) => updateFormData('issue', text)}
                    maxLength={100}
                  />
                </View>
                {errors.issue && (
                  <Text style={styles.errorText}>{errors.issue}</Text>
                )}
                <Text style={styles.characterCount}>
                  {formData.issue.length}/100
                </Text>
              </View>

              {/* Description */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                  Detailed Description <Text style={styles.requiredIndicator}>*</Text>
                </Text>
                <View style={[
                  styles.inputWrapper,
                  errors.description && styles.inputWrapperError
                ]}>
                  <TextInput
                    style={styles.textArea}
                    placeholder="Provide detailed information about the issue, including any relevant context or previous attempts to fix it..."
                    placeholderTextColor={colors.text.secondary}
                    value={formData.description}
                    onChangeText={(text: string) => updateFormData('description', text)}
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                    maxLength={500}
                  />
                </View>
                {errors.description && (
                  <Text style={styles.errorText}>{errors.description}</Text>
                )}
                <Text style={styles.characterCount}>
                  {formData.description.length}/500
                </Text>
              </View>

              {/* Address */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                  Service Address <Text style={styles.requiredIndicator}>*</Text>
                </Text>
                <View style={[
                  styles.inputWrapper,
                  errors.address && styles.inputWrapperError
                ]}>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter the full address where service is needed"
                    placeholderTextColor={colors.text.secondary}
                    value={formData.address}
                    onChangeText={(text: string) => updateFormData('address', text)}
                  />
                  <Ionicons 
                    name="location-outline" 
                    size={20} 
                    color={colors.text.secondary}
                    style={styles.inputIcon}
                  />
                </View>
                {errors.address && (
                  <Text style={styles.errorText}>{errors.address}</Text>
                )}
              </View>

              {/* Date & Time */}
              <View style={styles.dateTimeContainer}>
                <Text style={styles.inputLabel}>
                  Preferred Date & Time <Text style={styles.requiredIndicator}>*</Text>
                </Text>
                <View style={styles.dateTimeRow}>
                  <TouchableOpacity
                    style={[
                      styles.dateTimeButton,
                      errors.scheduledAt && styles.dateTimeButtonError
                    ]}
                    onPress={() => setShowDatePicker(true)}
                  >
                    <Ionicons 
                      name="calendar-outline" 
                      size={20} 
                      color={colors.text.secondary}
                      style={styles.dateTimeIcon}
                    />
                    <Text style={styles.dateTimeText}>
                      {formData.scheduledAt.toLocaleDateString()}
                    </Text>
                    <Ionicons name="chevron-down" size={16} color={colors.text.secondary} />
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.dateTimeButton,
                      errors.scheduledAt && styles.dateTimeButtonError
                    ]}
                    onPress={() => setShowTimePicker(true)}
                  >
                    <Ionicons 
                      name="time-outline" 
                      size={20} 
                      color={colors.text.secondary}
                      style={styles.dateTimeIcon}
                    />
                    <Text style={styles.dateTimeText}>
                      {formData.scheduledAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Text>
                    <Ionicons name="chevron-down" size={16} color={colors.text.secondary} />
                  </TouchableOpacity>
                </View>
                {errors.scheduledAt && (
                  <Text style={styles.errorText}>{errors.scheduledAt}</Text>
                )}
              </View>

              {/* Urgency Level */}
              <View style={styles.urgencyContainer}>
                <Text style={styles.inputLabel}>Urgency Level</Text>
                <View style={styles.urgencyGrid}>
                  {urgencyLevels.map((level) => (
                    <UrgencyCard
                      key={level.id}
                      urgencyLevel={level}
                      isSelected={formData.urgency === level.id}
                      onPress={() => updateFormData('urgency', level.id)}
                    />
                  ))}
                </View>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.footerButtons}>
            <Button
              title="Cancel"
              onPress={handleCancel}
              variant="outline"
              size="lg"
              style={styles.cancelButton}
            />
            <Button
              title="Save Changes"
              onPress={handleSave}
              variant="primary"
              size="lg"
              loading={saving}
              disabled={!hasChanges}
              style={styles.saveButtonFooter}
            />
          </View>
        </View>
      </KeyboardAvoidingView>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={formData.scheduledAt}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          minimumDate={new Date()}
          onChange={(event, selectedDate) => {
            setShowDatePicker(false);
            if (selectedDate) {
              const newDate = new Date(formData.scheduledAt);
              newDate.setFullYear(selectedDate.getFullYear());
              newDate.setMonth(selectedDate.getMonth());
              newDate.setDate(selectedDate.getDate());
              updateFormData('scheduledAt', newDate);
            }
          }}
        />
      )}

      {/* Time Picker */}
      {showTimePicker && (
        <DateTimePicker
          value={formData.scheduledAt}
          mode="time"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={(event, selectedTime) => {
            setShowTimePicker(false);
            if (selectedTime) {
              const newDate = new Date(formData.scheduledAt);
              newDate.setHours(selectedTime.getHours());
              newDate.setMinutes(selectedTime.getMinutes());
              updateFormData('scheduledAt', newDate);
            }
          }}
        />
      )}
    </Animated.View>
  );
}