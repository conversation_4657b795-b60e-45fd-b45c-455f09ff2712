import { prisma } from '../config/db';
import { Job, JobStatus } from '@prisma/client';
import { JobWithFullRelations } from '../types/database';

export const JobService = {
  async createJob(data: {
    userId: string;
    technicianId: string;
    issue: string;
    scheduledAt: Date;
    photoUrl?: string;
  }): Promise<Job> {
    return prisma.job.create({
      data: {
        userId: data.userId,
        technicianId: data.technicianId,
        issue: data.issue,
        scheduledAt: data.scheduledAt,
        photoUrl: data.photoUrl,
        status: JobStatus.PENDING,
      },
    });
  },

  async getUserJobs(userId: string): Promise<Job[]> {
    return prisma.job.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async getTechnicianJobs(technicianId: string): Promise<Job[]> {
    return prisma.job.findMany({
      where: { technicianId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async getJobById(id: string): Promise<JobWithFullRelations | null> {
    return prisma.job.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true,
            phone: true,
            role: true,
          },
        },
        technician: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                phone: true,
                role: true,
              },
            },
          },
        },
        payment: true,
        review: true,
        chat: {
          include: {
            messages: {
              include: {
                sender: {
                  select: {
                    id: true,
                    fullName: true,
                    email: true,
                    role: true,
                  },
                },
              },
            },
          },
        },
      },
    });
  },

  async updateJobStatus(id: string, status: JobStatus): Promise<Job> {
    return prisma.job.update({
      where: { id },
      data: { status },
    });
  },

  async deleteJob(id: string): Promise<Job> {
    return prisma.job.delete({
      where: { id },
    });
  },
};
