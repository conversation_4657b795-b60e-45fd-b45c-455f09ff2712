/// <reference types="vite/client" />

interface ImportMetaEnv {
  // API Configuration
  readonly VITE_API_URL: string;
  readonly VITE_API_BASE_URL: string;
  readonly VITE_SOCKET_URL: string;
  
  // Stripe Configuration
  readonly VITE_STRIPE_PUBLIC_KEY: string;
  
  // App Configuration
  readonly VITE_APP_NAME: string;
  readonly VITE_APP_VERSION: string;
  readonly VITE_APP_ENVIRONMENT: string;
  
  // External Services
  readonly VITE_GOOGLE_MAPS_API_KEY: string;
  readonly VITE_ANALYTICS_ID: string;
  
  // Feature Flags
  readonly VITE_ENABLE_ANALYTICS: string;
  readonly VITE_ENABLE_DEBUG: string;
  
  // Upload Configuration
  readonly VITE_MAX_FILE_SIZE: string;
  readonly VITE_ALLOWED_FILE_TYPES: string;
  
  // Timeouts and Limits
  readonly VITE_API_TIMEOUT: string;
  readonly VITE_SOCKET_TIMEOUT: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
