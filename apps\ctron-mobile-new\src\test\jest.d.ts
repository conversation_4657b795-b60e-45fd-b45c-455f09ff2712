// Jest type declarations

declare namespace jest {
  interface Matchers<R> {
    toBeInTheDocument(): R;
    toHaveTextContent(text: string | RegExp): R;
    toBeVisible(): R;
    toBeDisabled(): R;
    toHaveStyle(style: Record<string, any>): R;
  }
}

// Global Jest functions
declare const jest: {
  fn: <T extends (...args: any[]) => any>(implementation?: T) => jest.MockedFunction<T>;
  mock: (moduleName: string, factory?: () => any, options?: { virtual?: boolean }) => void;
  unmock: (moduleName: string) => void;
  clearAllMocks: () => void;
  resetAllMocks: () => void;
  restoreAllMocks: () => void;
  setTimeout: (timeout: number) => void;
  requireActual: <T = any>(moduleName: string) => T;
  requireMock: <T = any>(moduleName: string) => T;
};

declare const describe: {
  (name: string, fn: () => void): void;
  only: (name: string, fn: () => void) => void;
  skip: (name: string, fn: () => void) => void;
};

declare const it: {
  (name: string, fn?: () => void | Promise<void>, timeout?: number): void;
  only: (name: string, fn?: () => void | Promise<void>, timeout?: number) => void;
  skip: (name: string, fn?: () => void | Promise<void>, timeout?: number) => void;
};

declare const test: typeof it;

declare const expect: {
  <T = any>(actual: T): jest.Matchers<void> & {
    not: jest.Matchers<void>;
  };
  assertions: (num: number) => void;
  hasAssertions: () => void;
};

declare const beforeAll: (fn: () => void | Promise<void>, timeout?: number) => void;
declare const beforeEach: (fn: () => void | Promise<void>, timeout?: number) => void;
declare const afterAll: (fn: () => void | Promise<void>, timeout?: number) => void;
declare const afterEach: (fn: () => void | Promise<void>, timeout?: number) => void;