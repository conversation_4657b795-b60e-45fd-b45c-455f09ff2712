// src/context/AuthContext.tsx
import { CommonActions } from '@react-navigation/native';
import { jwtDecode } from 'jwt-decode';
import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import Toast from 'react-native-toast-message';

import { navigationRef } from '../navigation/navigationRef';
import { SocketService } from '../services/socket.service';
import { setAuthToken } from '../utils/auth.utils';
import { Platform } from '../utils/platformUtils';
import * as SecureStore from '../utils/secureStore';

export interface UserPayload {
  userId: string;
  fullName: string;
  email: string;
  phone?: string;
  address?: string;
  role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
  exp: number;
  iat: number;
  technicianProfile?: { id: string };
}

interface AuthContextType {
  user: UserPayload | null;
  token: string | null;
  loading: boolean;
  login: (token: string) => Promise<void>;
  logout: (showMessage?: boolean) => Promise<void>;
  updateProfile: (profileData: Partial<UserPayload>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);
export { AuthContext };

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserPayload | null>(null);
  const [token, setTokenState] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);





  const logout = useCallback(async (showMessage = true) => {
    console.log('🔐 AuthProvider: Logging out...');
    setUser(null);
    setTokenState(null);
    setAuthToken(null);
    SocketService.disconnect();

    await SecureStore.deleteItemAsync('token');

    if (navigationRef.isReady()) {
      navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'Auth' }],
        })
      );
    }

    if (showMessage) {
      Toast.show({
        type: 'info',
        text1: 'Logged Out',
        text2: 'You have been successfully logged out.',
      });
    }
  }, []);



  const navigateToRoleDashboard = useCallback((role: UserPayload['role']) => {
    if (!navigationRef.isReady()) {
      console.warn('Navigation ref not ready during role-based navigation.');
      return false;
    }

    let stackName: string | null = null;
    switch (role) {
      case 'HOMEOWNER':
        stackName = 'HomeownerStack';
        break;
      case 'TECHNICIAN':
        stackName = 'TechnicianStack';
        break;
      case 'ADMIN':
        stackName = 'AdminStack';
        break;
      default:
        console.warn('Unknown role:', role);
        return false;
    }

    const currentRoute = navigationRef.getCurrentRoute();
    if (currentRoute && currentRoute.name === stackName) {
      console.log(`Already on ${stackName}. No navigation needed.`);
      return true;
    }

    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: stackName }],
      })
    );
    return true;
  }, []);

  const bootstrap = useCallback(async () => {
    console.log('🔐 AuthProvider: Bootstrap function called, Platform:', Platform.OS);
    try {
      const storedToken = await SecureStore.getItemAsync('token');
      if (!storedToken) {
        setLoading(false);
        return;
      }

      const decoded = jwtDecode<UserPayload>(storedToken);
      console.log('🧾 Decoded token payload (bootstrap):', decoded);

      if (decoded.exp * 1000 < Date.now()) {
        console.warn('⚠️ Token expired. Clearing...');
        await SecureStore.deleteItemAsync('token');
        setLoading(false);
        return;
      }

      setUser(decoded);
      setTokenState(storedToken);
      setAuthToken(storedToken);

      const success = navigateToRoleDashboard(decoded.role);
      if (!success) {
        console.warn('🚫 Role mismatch detected. Logging out...');
        await logout(true);
      }
    } catch (error) {
      console.error('🔴 Auth bootstrap failed:', error);
      await SecureStore.deleteItemAsync('token');
    } finally {
      setLoading(false);
    }
  }, [logout, navigateToRoleDashboard]);

  useEffect(() => {
    console.log('🔐 AuthProvider: Starting bootstrap...');
    bootstrap();
  }, [bootstrap]);

  const login = useCallback(async (newToken: string) => {
    try {
      const decoded = jwtDecode<UserPayload>(newToken);
      setUser(decoded);
      setTokenState(newToken);
      setAuthToken(newToken);

      await SecureStore.setItemAsync('token', newToken);

      navigateToRoleDashboard(decoded.role);
    } catch (error) {
      console.error('🔴 Login failed:', error);
      Toast.show({
        type: 'error',
        text1: 'Login Failed',
        text2: 'Invalid credentials or token.',
      });
      await logout();
    }
  }, [logout, navigateToRoleDashboard]);

  const updateProfile = useCallback(async (profileData: Partial<UserPayload>) => {
    try {
      if (!user) return;
      
      // Update local user state
      const updatedUser = { ...user, ...profileData };
      setUser(updatedUser);
      
      // In a real implementation, you would also update the backend
      // await api.put('/api/users/profile', profileData);
      
      Toast.show({
        type: 'success',
        text1: 'Profile Updated',
        text2: 'Your profile has been updated successfully.',
      });
    } catch (error) {
      console.error('🔴 Profile update failed:', error);
      Toast.show({
        type: 'error',
        text1: 'Update Failed',
        text2: 'Failed to update profile. Please try again.',
      });
    }
  }, [user]);

  return (
    <AuthContext.Provider value={{ user, token, loading, login, logout, updateProfile }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('❌ useAuth must be used inside an <AuthProvider>');
  }
  return context;
};
