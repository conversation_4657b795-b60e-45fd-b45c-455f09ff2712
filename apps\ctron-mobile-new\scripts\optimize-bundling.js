/**
 * This script optimizes bundling for Expo SDK 52
 * It applies various optimizations to improve build performance
 */

const fs = require('fs');
const path = require('path');

const chalk = require('chalk');

console.log(chalk.blue('Optimizing bundling for Expo SDK 52...'));

// Paths to configuration files
const metroConfigPath = path.join(__dirname, '..', 'metro.config.js');
const metroRcPath = path.join(__dirname, '..', '.metrorc.js');
const babelConfigPath = path.join(__dirname, '..', 'babel.config.js');
const webpackConfigPath = path.join(__dirname, '..', 'webpack.config.js');

// Check if files exist
const metroConfigExists = fs.existsSync(metroConfigPath);
const metroRcExists = fs.existsSync(metroRcPath);
const babelConfigExists = fs.existsSync(babelConfigPath);
const webpackConfigExists = fs.existsSync(webpackConfigPath);

console.log(chalk.cyan('Checking configuration files:'));
console.log(chalk.white(`  - metro.config.js: ${metroConfigExists ? 'Found' : 'Not found'}`));
console.log(chalk.white(`  - .metrorc.js: ${metroRcExists ? 'Found' : 'Not found'}`));
console.log(chalk.white(`  - babel.config.js: ${babelConfigExists ? 'Found' : 'Not found'}`));
console.log(chalk.white(`  - webpack.config.js: ${webpackConfigExists ? 'Found' : 'Not found'}`));

// Create .metro-cache directory if it doesn't exist
const metroCacheDir = path.join(__dirname, '..', '.metro-cache');
if (!fs.existsSync(metroCacheDir)) {
  console.log(chalk.yellow(`Creating .metro-cache directory`));
  fs.mkdirSync(metroCacheDir, { recursive: true });
  console.log(chalk.green(`Created .metro-cache directory`));
}

// Create clean-metro-cache.js script if it doesn't exist
const cleanMetroCachePath = path.join(__dirname, '..', 'clean-metro-cache.js');
if (!fs.existsSync(cleanMetroCachePath)) {
  console.log(chalk.yellow(`Creating clean-metro-cache.js script`));
  const cleanMetroCacheContent = `/**
 * This script cleans the Metro bundler cache
 */

const fs = require('fs');
const path = require('path');
const rimraf = require('rimraf');
const chalk = require('chalk');

console.log(chalk.blue('Cleaning Metro cache...'));

// Paths to cache directories
const cacheDirectories = [
  path.join(__dirname, '.metro-cache'),
  path.join(__dirname, 'node_modules', '.cache'),
  path.join(require('os').homedir(), '.metro')
];

// Clean each cache directory
cacheDirectories.forEach(cacheDir => {
  if (fs.existsSync(cacheDir)) {
    console.log(chalk.yellow(\`Removing \${cacheDir}\`));
    rimraf.sync(cacheDir);
    console.log(chalk.green(\`Removed \${cacheDir}\`));
  } else {
    console.log(chalk.white(\`Directory not found: \${cacheDir}\`));
  }
});

console.log(chalk.green('Metro cache cleaned successfully!'));
`;
  fs.writeFileSync(cleanMetroCachePath, cleanMetroCacheContent);
  console.log(chalk.green(`Created clean-metro-cache.js script`));
}

// Create src/shims directory if it doesn't exist
const shimsDir = path.join(__dirname, '..', 'src', 'shims');
if (!fs.existsSync(shimsDir)) {
  console.log(chalk.yellow(`Creating src/shims directory`));
  fs.mkdirSync(shimsDir, { recursive: true });
  console.log(chalk.green(`Created src/shims directory`));
}

// Create empty-module.js shim if it doesn't exist
const emptyModulePath = path.join(shimsDir, 'empty-module.js');
if (!fs.existsSync(emptyModulePath)) {
  console.log(chalk.yellow(`Creating empty-module.js shim`));
  fs.writeFileSync(emptyModulePath, `// Empty module shim for web compatibility\nexport default {};\n`);
  console.log(chalk.green(`Created empty-module.js shim`));
}

// Create TurboModuleRegistry.web.js shim if it doesn't exist
const turboModuleRegistryPath = path.join(shimsDir, 'TurboModuleRegistry.web.js');
if (!fs.existsSync(turboModuleRegistryPath)) {
  console.log(chalk.yellow(`Creating TurboModuleRegistry.web.js shim`));
  fs.writeFileSync(turboModuleRegistryPath, `// TurboModuleRegistry shim for web compatibility\nexport function get(name) {\n  return null;\n}\n`);
  console.log(chalk.green(`Created TurboModuleRegistry.web.js shim`));
}

// Create config directory if it doesn't exist
const configDir = path.join(__dirname, '..', 'config');
if (!fs.existsSync(configDir)) {
  console.log(chalk.yellow(`Creating config directory`));
  fs.mkdirSync(configDir, { recursive: true });
  console.log(chalk.green(`Created config directory`));
}

// Create native-modules.json if it doesn't exist
const nativeModulesPath = path.join(configDir, 'native-modules.json');
if (!fs.existsSync(nativeModulesPath)) {
  console.log(chalk.yellow(`Creating native-modules.json`));
  const nativeModulesContent = {
    modules: [
      "@react-native-community/datetimepicker",
      "expo-secure-store",
      "expo-notifications",
      "expo-location",
      "expo-image-picker",
      "expo-document-picker",
      "expo-background-task",
      "expo-task-manager",
      "react-native/Libraries/TurboModule/TurboModuleRegistry",
      "react-native/Libraries/Animated/NativeAnimatedModule",
      "react-native/Libraries/Animated/NativeAnimatedHelper"
    ]
  };
  fs.writeFileSync(nativeModulesPath, JSON.stringify(nativeModulesContent, null, 2));
  console.log(chalk.green(`Created native-modules.json`));
}

console.log(chalk.green('Bundling optimization completed successfully!'));
console.log(chalk.blue('\nRecommendations:'));
console.log(chalk.white('1. Run "yarn cache:clean" to clear the cache before building'));
console.log(chalk.white('2. Use "yarn start:optimized" for faster development'));
console.log(chalk.white('3. Use "yarn web:optimized" for faster web development'));