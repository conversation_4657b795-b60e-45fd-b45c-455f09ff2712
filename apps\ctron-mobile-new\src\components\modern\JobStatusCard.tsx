// CTRON Home - Modern Job Status Card Component
// Professional job tracking card using NativeBase

import { MaterialIcons } from '@expo/vector-icons';
import {
  Box,
  VStack,
  HStack,
  Text,
  Avatar,
  Badge,
  Button,
  Icon,
  Progress,
  Pressable,
  useTheme,
} from 'native-base';
import React from 'react';

export interface JobStatusCardProps {
  job: {
    id: string;
    title: string;
    description: string;
    status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
    priority: 'low' | 'medium' | 'high' | 'emergency';
    scheduledDate: string;
    estimatedDuration: string;
    address: string;
    price: number;
    technician?: {
      id: string;
      name: string;
      avatar?: string;
      phone: string;
      rating: number;
    };
    progress?: number; // 0-100
  };
  onPress?: () => void;
  onContactPress?: () => void;
  onTrackPress?: () => void;
  variant?: 'default' | 'compact' | 'detailed';
}

export const JobStatusCard: React.FC<JobStatusCardProps> = ({
  job,
  onPress,
  onContactPress,
  onTrackPress,
  variant = 'default',
}) => {


  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          color: 'warning',
          text: 'Pending',
          icon: 'schedule',
          bgColor: 'warning.50',
        };
      case 'assigned':
        return {
          color: 'info',
          text: 'Assigned',
          icon: 'person',
          bgColor: 'info.50',
        };
      case 'in_progress':
        return {
          color: 'primary',
          text: 'In Progress',
          icon: 'build',
          bgColor: 'primary.50',
        };
      case 'completed':
        return {
          color: 'success',
          text: 'Completed',
          icon: 'check-circle',
          bgColor: 'success.50',
        };
      case 'cancelled':
        return {
          color: 'error',
          text: 'Cancelled',
          icon: 'cancel',
          bgColor: 'error.50',
        };
      default:
        return {
          color: 'gray',
          text: 'Unknown',
          icon: 'help',
          bgColor: 'gray.50',
        };
    }
  };

  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case 'emergency':
        return { color: 'error', text: 'Emergency', pulse: true };
      case 'high':
        return { color: 'warning', text: 'High' };
      case 'medium':
        return { color: 'info', text: 'Medium' };
      case 'low':
        return { color: 'gray', text: 'Low' };
      default:
        return { color: 'gray', text: 'Normal' };
    }
  };

  const statusConfig = getStatusConfig(job.status);


  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (variant === 'compact') {
    return (
      <Pressable onPress={onPress}>
        <Box
          bg="white"
          rounded="lg"
          shadow={1}
          p={3}
          mb={2}
          borderLeftWidth={4}
          borderLeftColor={`${statusConfig.color}.500`}
          _pressed={{
            bg: statusConfig.bgColor,
          }}
        >
          <HStack space={3} alignItems="center">
            <Icon
              as={MaterialIcons}
              name={statusConfig.icon}
              color={`${statusConfig.color}.500`}
              size="md"
            />
            <VStack flex={1} space={1}>
              <Text fontSize="md" fontWeight="semibold" numberOfLines={1}>
                {job.title}
              </Text>
              <Text fontSize="sm" color="gray.500">
                {formatDate(job.scheduledDate)}
              </Text>
            </VStack>
            <VStack alignItems="flex-end" space={1}>
              <Badge colorScheme={statusConfig.color} variant="solid" size="sm">
                {statusConfig.text}
              </Badge>
              <Text fontSize="sm" fontWeight="bold" color="gray.700">
                £{job.price}
              </Text>
            </VStack>
          </HStack>
        </Box>
      </Pressable>
    );
  }

  if (variant === 'detailed') {
    return (
      <Pressable onPress={onPress}>
        <Box
          bg="white"
          rounded="2xl"
          shadow={3}
          p={5}
          mb={4}
          borderWidth={1}
          borderColor="gray.100"
          _pressed={{
            bg: statusConfig.bgColor,
            shadow: 4,
          }}
        >
          <VStack space={4}>
            {/* Header */}
            <HStack justifyContent="space-between" alignItems="flex-start">
              <VStack flex={1} space={1}>
                <HStack alignItems="center" space={2}>
                  <Text fontSize="xl" fontWeight="bold" numberOfLines={1}>
                    {job.title}
                  </Text>
                  {job.priority === 'emergency' && (
                    <Badge colorScheme="error" variant="solid" size="sm">
                      URGENT
                    </Badge>
                  )}
                </HStack>
                <Text fontSize="md" color="gray.600" numberOfLines={2}>
                  {job.description}
                </Text>
              </VStack>
              <Badge colorScheme={statusConfig.color} variant="solid" size="lg">
                {statusConfig.text}
              </Badge>
            </HStack>

            {/* Progress Bar (for in-progress jobs) */}
            {job.status === 'in_progress' && job.progress !== undefined && (
              <VStack space={2}>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="gray.600">
                    Progress
                  </Text>
                  <Text fontSize="sm" fontWeight="semibold" color="primary.600">
                    {job.progress}%
                  </Text>
                </HStack>
                <Progress
                  value={job.progress}
                  colorScheme="primary"
                  size="md"
                  rounded="full"
                />
              </VStack>
            )}

            {/* Job Details */}
            <VStack space={3}>
              <HStack space={4}>
                <HStack space={2} alignItems="center" flex={1}>
                  <Icon as={MaterialIcons} name="schedule" color="gray.400" size="sm" />
                  <VStack>
                    <Text fontSize="xs" color="gray.500">
                      Scheduled
                    </Text>
                    <Text fontSize="sm" fontWeight="medium">
                      {formatDate(job.scheduledDate)}
                    </Text>
                  </VStack>
                </HStack>
                <HStack space={2} alignItems="center" flex={1}>
                  <Icon as={MaterialIcons} name="timer" color="gray.400" size="sm" />
                  <VStack>
                    <Text fontSize="xs" color="gray.500">
                      Duration
                    </Text>
                    <Text fontSize="sm" fontWeight="medium">
                      {job.estimatedDuration}
                    </Text>
                  </VStack>
                </HStack>
              </HStack>

              <HStack space={2} alignItems="flex-start">
                <Icon as={MaterialIcons} name="location-on" color="gray.400" size="sm" mt={0.5} />
                <VStack flex={1}>
                  <Text fontSize="xs" color="gray.500">
                    Address
                  </Text>
                  <Text fontSize="sm" fontWeight="medium" numberOfLines={2}>
                    {job.address}
                  </Text>
                </VStack>
              </HStack>
            </VStack>

            {/* Technician Info */}
            {job.technician && (
              <Box bg="gray.50" rounded="lg" p={3}>
                <HStack space={3} alignItems="center">
                  <Avatar
                    size="md"
                    source={job.technician.avatar ? { uri: job.technician.avatar } : undefined}
                    bg="primary.500"
                  >
                    {job.technician.name.charAt(0)}
                  </Avatar>
                  <VStack flex={1} space={1}>
                    <Text fontSize="md" fontWeight="semibold">
                      {job.technician.name}
                    </Text>
                    <HStack space={2} alignItems="center">
                      <Icon as={MaterialIcons} name="star" color="yellow.400" size="xs" />
                      <Text fontSize="sm" color="gray.600">
                        {job.technician.rating}
                      </Text>
                      <Text fontSize="sm" color="gray.400">
                        • {job.technician.phone}
                      </Text>
                    </HStack>
                  </VStack>
                  <Button
                    size="sm"
                    variant="outline"
                    colorScheme="primary"
                    onPress={onContactPress}
                  >
                    Contact
                  </Button>
                </HStack>
              </Box>
            )}

            {/* Price and Actions */}
            <HStack justifyContent="space-between" alignItems="center">
              <VStack>
                <Text fontSize="sm" color="gray.500">
                  Total Cost
                </Text>
                <Text fontSize="2xl" fontWeight="bold" color="primary.600">
                  £{job.price}
                </Text>
              </VStack>
              <Button colorScheme="primary" size="md" onPress={onTrackPress}>
                Track Job
              </Button>
            </HStack>
          </VStack>
        </Box>
      </Pressable>
    );
  }

  // Default variant
  return (
    <Pressable onPress={onPress}>
      <Box
        bg="white"
        rounded="xl"
        shadow={2}
        p={4}
        mb={3}
        borderLeftWidth={4}
        borderLeftColor={`${statusConfig.color}.500`}
        _pressed={{
          bg: statusConfig.bgColor,
          shadow: 3,
        }}
      >
        <VStack space={3}>
          {/* Header */}
          <HStack justifyContent="space-between" alignItems="flex-start">
            <VStack flex={1} space={1}>
              <HStack alignItems="center" space={2}>
                <Text fontSize="lg" fontWeight="semibold" numberOfLines={1}>
                  {job.title}
                </Text>
                {job.priority === 'emergency' && (
                  <Badge colorScheme="error" variant="solid" size="sm">
                    URGENT
                  </Badge>
                )}
              </HStack>
              <Text fontSize="sm" color="gray.600" numberOfLines={1}>
                {job.description}
              </Text>
            </VStack>
            <Badge colorScheme={statusConfig.color} variant="solid">
              {statusConfig.text}
            </Badge>
          </HStack>

          {/* Job Info */}
          <HStack justifyContent="space-between" alignItems="center">
            <VStack space={1}>
              <HStack space={2} alignItems="center">
                <Icon as={MaterialIcons} name="schedule" color="gray.400" size="sm" />
                <Text fontSize="sm" color="gray.600">
                  {formatDate(job.scheduledDate)}
                </Text>
              </HStack>
              <HStack space={2} alignItems="center">
                <Icon as={MaterialIcons} name="location-on" color="gray.400" size="sm" />
                <Text fontSize="sm" color="gray.600" numberOfLines={1} flex={1}>
                  {job.address}
                </Text>
              </HStack>
            </VStack>
            <Text fontSize="lg" fontWeight="bold" color="primary.600">
              £{job.price}
            </Text>
          </HStack>

          {/* Technician (if assigned) */}
          {job.technician && (
            <HStack space={3} alignItems="center" bg="gray.50" rounded="lg" p={2}>
              <Avatar
                size="sm"
                source={job.technician.avatar ? { uri: job.technician.avatar } : undefined}
                bg="primary.500"
              >
                {job.technician.name.charAt(0)}
              </Avatar>
              <VStack flex={1}>
                <Text fontSize="sm" fontWeight="medium">
                  {job.technician.name}
                </Text>
                <HStack space={1} alignItems="center">
                  <Icon as={MaterialIcons} name="star" color="yellow.400" size="xs" />
                  <Text fontSize="xs" color="gray.600">
                    {job.technician.rating}
                  </Text>
                </HStack>
              </VStack>
              <Button size="xs" variant="outline" colorScheme="primary" onPress={onContactPress}>
                Contact
              </Button>
            </HStack>
          )}
        </VStack>
      </Box>
    </Pressable>
  );
};

export default JobStatusCard;