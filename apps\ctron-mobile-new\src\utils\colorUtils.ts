import { colors } from '../styles/theme';


interface ColorObject {
  main?: string;
  primary?: string;
  secondary?: string;
  [key: string]: string | undefined;
}


export function getColorValue(color: string | ColorObject): string {
  if (typeof color === 'string') {
    return color;
  }

  const colorKeys = Object.keys(color);
  for (const key of ['main', '500', 'primary', 'default', ...colorKeys]) {
    const value = color[key];
    if (typeof value === 'string' && value.startsWith('#')) {
      return value;
    }
  }

  return '#000000';
}

/**
 * Get secondary color value
 */
export function getSecondaryColor(): string {
  return getColorValue(colors.secondary);
}

/**
 * Get error color value
 */
export function getErrorColor(): string {
  return (colors.error.main as string) || getColorValue(colors.status?.error) || '#EF4444';
}

/**
 * Get success color value
 */
export function getSuccessColor(): string {
  return getColorValue(colors.success);
}

/**
 * Get warning color value
 */
export function getWarningColor(): string {
  return getColorValue(colors.warning);
}

/**
 * Get info color value
 */
export function getInfoColor(): string {
  return getColorValue(colors.info);
}

/**
 * Get text color value
 */
export function getTextColor(variant: 'primary' | 'secondary' | 'tertiary' | 'inverse' = 'primary'): string {
  return colors.text[variant] || '#000000';
}

/**
 * Get background color value
 */
export function getBackgroundColor(variant: 'primary' | 'secondary' | 'tertiary' = 'primary'): string {
  return colors.background[variant] || '#FFFFFF';
}

/**
 * Get border color value
 */
export function getBorderColor(variant: 'light' | 'medium' | 'dark' = 'light'): string {
  return colors.border[variant] || '#E5E7EB';
}

/**
 * Get status color based on status string
 * @param status The status string
 * @returns The color for the given status
 */
export const getStatusColor = (status: string): string => {
  const statusLower = status.toLowerCase();

  switch (statusLower) {
    case 'pending':
      return colors.status?.pending || '#F59E0B';
    case 'assigned':
      return colors.status?.assigned || '#3B82F6';
    case 'active':
    case 'in_progress':
        return colors.status?.active || '#EF4444';
      case 'completed':
        return colors.status?.completed || '#10B981';
      case 'cancelled':
        return colors.status?.cancelled || '#6B7280';
      case 'overdue':
        return colors.status?.overdue || '#DC2626';
      case 'error':
        return getErrorColor();
      default:
        return colors.gray?.[500] || '#6B7280';
    }
  }

/**
 * Get priority color based on priority string
 */
export function getPriorityColor(priority: string): string {
  const priorityLower = priority.toLowerCase();

  switch (priorityLower) {
    case 'high':
    case 'urgent':
      return getErrorColor();
    case 'medium':
      return getWarningColor();
    case 'low':
      return getSuccessColor();
    default:
      return colors.gray?.[500] || '#6B7280';
  }
}

/**
 * Get availability color for technicians
 */
export function getAvailabilityColor(isAvailable: boolean): string {
  return isAvailable ? getSuccessColor() : getErrorColor();
}

/**
 * Convert hex color to rgba
 */
export function hexToRgba(hex: string, alpha: number = 1): string {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return hex;

  const r = parseInt(result[1] || '0', 16);
  const g = parseInt(result[2] || '0', 16);
  const b = parseInt(result[3] || '0', 16);

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

/**
 * Lighten a color by a percentage
 */
export function lightenColor(color: string, percent: number): string {
  const colorValue = getColorValue(color);
  const num = parseInt(colorValue.replace('#', ''), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;

  return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}

/**
 * Darken a color by a percentage
 */
export function darkenColor(color: string, percent: number): string {
  return lightenColor(color, -percent);
}
