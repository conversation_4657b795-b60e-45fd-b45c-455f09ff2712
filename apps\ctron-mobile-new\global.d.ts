// This file is used to define global types that are available throughout the app.
// Global type declarations for React Native, Web, and Node.js environments

// React Native global variables
declare let __DEV__: boolean;

declare global {
  const __webpack_require__: {
    c: Record<string, unknown>;
    [key: string]: unknown;
  };
  const global: typeof globalThis;
  const gc: (() => void) | undefined;
  const TurboModuleRegistry: {
    get: (name: string) => unknown;
    getEnforcing: (name: string) => unknown;
  };

  interface Window {
    __DEV__: boolean;
    __webpack_require__: unknown;
    webpackChunkName: string;
    webpackMode: string;
    webpackPrefetch: boolean;
    webpackPreload: boolean;
    require?: unknown;
    [key: string]: unknown;
  }

  namespace NodeJS {
    interface Global {
      __DEV__: boolean;
      __webpack_require__: unknown;
      TurboModule: unknown;
      gc: (() => void) | undefined;
      TurboModuleRegistry?: unknown;
      [key: string]: unknown;
    }
  }
}

// Define TurboModule interfaces
declare module 'react-native/Libraries/TurboModule/RCTExport' {
  export interface TurboModule {
    [key: string]: unknown;
  }
}

interface TurboModule {
  [key: string]: unknown;
}

interface TurboModuleRegistry {
  get: (name: string) => TurboModule | null;
  getEnforcing: (name: string) => TurboModule;
  [key: string]: unknown;
}

// Augment the global object type
declare interface GlobalThis {
  TurboModuleRegistry?: TurboModuleRegistry;
  // Add index signature to allow dynamic properties
  [key: string]: unknown;
}

// Expo automatically provides EXPO_PUBLIC_ environment variables
// No need to declare them as they are available globally via process.env