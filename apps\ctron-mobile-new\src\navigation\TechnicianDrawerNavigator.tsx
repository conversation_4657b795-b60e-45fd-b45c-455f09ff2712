// src/navigation/TechnicianDrawerNavigator.tsx

import { Ionicons } from '@expo/vector-icons';
import {
  createDrawerNavigator,
  DrawerContentScrollView,
  DrawerItemList,
  DrawerContentComponentProps,
} from '@react-navigation/drawer';

import { useAuth } from '../context/AuthContext';
import { KycScreen } from '../screens/Technician/KycScreen';
import ProfileScreen from '../screens/Technician/ProfileScreen';
import SettingsScreen from '../screens/Technician/SettingsScreen';
import TechnicianDashboard from '../screens/Technician/TechnicianDashboard';
import { View, Text, StyleSheet, Image } from '../utils/platformUtils';


const Drawer = createDrawerNavigator();

function CustomDrawerContent(props: DrawerContentComponentProps) {
  const { user } = useAuth();
  const firstName = user?.fullName?.trim().split(' ')[0] || 'Technician';

  return (
    <DrawerContentScrollView {...props}>
      <View style={styles.drawerHeader}>
        <Image
          source={{ uri: 'https://ui-avatars.com/api/?name=Technician&background=007AFF&color=fff' }}
          style={styles.avatar}
        />
        <Text style={styles.name}>{firstName}</Text>
        <Text style={styles.email}>{user?.email}</Text>
      </View>
      <DrawerItemList {...props} />
    </DrawerContentScrollView>
  );
}

export default function TechnicianDrawerNavigator() {
  const { user } = useAuth();
  const firstName = user?.fullName?.split(' ')[0] || 'Technician';

  return (
    <Drawer.Navigator
      initialRouteName="TechnicianDashboard"
      drawerContent={props => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: true,
        drawerType: 'front',
        swipeEdgeWidth: 50,
      }}
    >
      <Drawer.Screen
        name="TechnicianDashboard"
        component={TechnicianDashboard}
        options={{
          headerTitle: () => (
            <Text style={{ fontSize: 18, fontWeight: 'bold' }}>
              Welcome back, {firstName}
              👋</Text>
          ),
          drawerIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="ProfileScreen"
        component={ProfileScreen}
        options={{
          title: 'My Profile',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="SettingsScreen"
        component={SettingsScreen}
        options={{
          title: 'Settings',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="settings-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="KycScreen"
        component={KycScreen}
        options={{
          title: 'KYC Verification',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="shield-checkmark-outline" size={size} color={color} />
          ),
        }}
      />
    </Drawer.Navigator>
  );
}

const styles = StyleSheet.create({
  drawerHeader: {
    backgroundColor: '#f4f6f8',
    paddingVertical: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    marginBottom: 10,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    marginBottom: 10,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  email: {
    fontSize: 14,
    color: '#666',
  },
});

