// CTRON Home - Web Card Component
// Consistent card layouts for web admin

import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  padding?: boolean;
  hover?: boolean;
  onClick?: () => void;
}

export const Card: React.FC<CardProps> = ({
  children,
  className = '',
  padding = true,
  hover = false,
  onClick,
}) => {
  const baseClasses = [
    'bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl shadow-lg',
    hover && 'hover:shadow-xl transition-all duration-300 cursor-pointer',
    padding && 'p-6',
    onClick && 'cursor-pointer',
  ].filter(Boolean).join(' ');

  const Component = onClick ? 'button' : 'div';

  return (
    <Component
      className={`${baseClasses} ${className}`}
      onClick={onClick}
      type={onClick ? 'button' : undefined}
    >
      {children}
    </Component>
  );
};

export default Card;