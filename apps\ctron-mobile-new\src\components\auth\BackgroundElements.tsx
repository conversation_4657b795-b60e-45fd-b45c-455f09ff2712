import React, { useEffect, useRef } from 'react';
import { View, Dimensions, Animated, Easing } from '../../utils/platformUtils';
import { useTheme } from '../../context/ThemeContext';
import SafeLinearGradient from '../SafeLinearGradient';

const { width, height } = Dimensions.get('window');

interface BackgroundElementsProps {
  variant?: 'login' | 'signup' | 'splash';
}

export const BackgroundElements: React.FC<BackgroundElementsProps> = ({ variant = 'login' }) => {
  const { theme, isDark } = useTheme();
  
  // Animation values
  const floatAnim1 = useRef(new Animated.Value(0)).current;
  const floatAnim2 = useRef(new Animated.Value(0)).current;
  const floatAnim3 = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    // Create floating animations
    const createFloatingAnimation = (animValue: any, duration: number, delay: number = 0) => {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(animValue, {
            toValue: 1,
            duration: duration,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: duration,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: true,
          }),
        ])
      );
      
      if (delay > 0) {
        return Animated.sequence([
          Animated.delay(delay),
          animation
        ]);
      }
      return animation;
    };
    
    // Start floating animations
    createFloatingAnimation(floatAnim1, 3000, 0).start();
    createFloatingAnimation(floatAnim2, 4000, 1000).start();
    createFloatingAnimation(floatAnim3, 5000, 2000).start();
    
    // Rotation animation
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 20000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();
  }, []);

  const getBackgroundColor = (opacity: number) => {
    const baseColor = isDark ? theme.colors.primary.main : theme.colors.primary.light;
    return `${baseColor}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`;
  };

  const getSecondaryColor = (opacity: number) => {
    const baseColor = isDark ? theme.colors.secondary.main : theme.colors.secondary.light;
    return `${baseColor}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`;
  };

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: -1,
    }}>
      {/* Animated Gradient Orbs */}
      <Animated.View
        style={{
          position: 'absolute',
          top: height * 0.1,
          right: width * 0.1,
          width: 120,
          height: 120,
          borderRadius: 60,
          transform: [
            {
              translateY: floatAnim1.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -20],
              }),
            },
            {
              rotate: rotateAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
          ],
        }}
      >
        <SafeLinearGradient
          colors={[getBackgroundColor(0.15), getSecondaryColor(0.1)]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{ width: '100%', height: '100%', borderRadius: 60 }}
        />
      </Animated.View>
      
      <Animated.View
        style={{
          position: 'absolute',
          bottom: height * 0.2,
          left: width * 0.05,
          width: 80,
          height: 80,
          borderRadius: 40,
          transform: [
            {
              translateY: floatAnim2.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 15],
              }),
            },
          ],
        }}
      >
        <SafeLinearGradient
          colors={[getSecondaryColor(0.12), getBackgroundColor(0.08)]}
          start={{ x: 1, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={{ width: '100%', height: '100%', borderRadius: 40 }}
        />
      </Animated.View>
      
      <Animated.View
        style={{
          position: 'absolute',
          top: height * 0.6,
          right: width * 0.2,
          width: 60,
          height: 60,
          borderRadius: 30,
          transform: [
            {
              translateX: floatAnim3.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 10],
              }),
            },
            {
              translateY: floatAnim3.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -10],
              }),
            },
          ],
        }}
      >
        <SafeLinearGradient
          colors={[getBackgroundColor(0.1), getSecondaryColor(0.15)]}
          start={{ x: 0.5, y: 0 }}
          end={{ x: 0.5, y: 1 }}
          style={{ width: '100%', height: '100%', borderRadius: 30 }}
        />
      </Animated.View>
      {/* Top-left circle */}
      <View
        style={{
          position: 'absolute',
          top: -100,
          left: -100,
          width: 300,
          height: 300,
          borderRadius: 150,
          backgroundColor: getBackgroundColor(0.1),
        }}
      />
      
      {/* Top-right small circle */}
      <View
        style={{
          position: 'absolute',
          top: 50,
          right: -50,
          width: 150,
          height: 150,
          borderRadius: 75,
          backgroundColor: getSecondaryColor(0.08),
        }}
      />
      
      {/* Middle decorative elements */}
      {variant === 'login' && (
        <>
          <View
            style={{
              position: 'absolute',
              top: height * 0.3,
              left: -30,
              width: 100,
              height: 100,
              borderRadius: 50,
              backgroundColor: getBackgroundColor(0.06),
            }}
          />
          <View
            style={{
              position: 'absolute',
              top: height * 0.5,
              right: -40,
              width: 120,
              height: 120,
              borderRadius: 60,
              backgroundColor: getSecondaryColor(0.05),
            }}
          />
        </>
      )}
      
      {variant === 'signup' && (
        <>
          <View
            style={{
              position: 'absolute',
              top: height * 0.25,
              right: -60,
              width: 140,
              height: 140,
              borderRadius: 70,
              backgroundColor: getBackgroundColor(0.07),
            }}
          />
          <View
            style={{
              position: 'absolute',
              top: height * 0.6,
              left: -50,
              width: 110,
              height: 110,
              borderRadius: 55,
              backgroundColor: getSecondaryColor(0.06),
            }}
          />
        </>
      )}
      
      {variant === 'splash' && (
        <>
          <View
            style={{
              position: 'absolute',
              top: height * 0.2,
              left: width * 0.8,
              width: 80,
              height: 80,
              borderRadius: 40,
              backgroundColor: getBackgroundColor(0.1),
            }}
          />
          <View
            style={{
              position: 'absolute',
              top: height * 0.7,
              left: width * 0.1,
              width: 60,
              height: 60,
              borderRadius: 30,
              backgroundColor: getSecondaryColor(0.08),
            }}
          />
          <View
            style={{
              position: 'absolute',
              top: height * 0.8,
              right: width * 0.2,
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: getBackgroundColor(0.06),
            }}
          />
        </>
      )}
      
      {/* Bottom decorative elements */}
      <View
        style={{
          position: 'absolute',
          bottom: -80,
          right: -80,
          width: 200,
          height: 200,
          borderRadius: 100,
          backgroundColor: getBackgroundColor(0.08),
        }}
      />
      
      <View
        style={{
          position: 'absolute',
          bottom: -50,
          left: -30,
          width: 120,
          height: 120,
          borderRadius: 60,
          backgroundColor: getSecondaryColor(0.06),
        }}
      />
      
      {/* Subtle gradient overlay */}
      <View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: isDark 
            ? 'rgba(0, 0, 0, 0.02)' 
            : 'rgba(255, 255, 255, 0.03)',
        }}
      />
    </View>
  );
};

export default BackgroundElements;