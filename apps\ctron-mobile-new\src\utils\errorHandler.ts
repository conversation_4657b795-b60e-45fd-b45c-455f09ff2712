// CTRON Home - Error Handler Utility
// Centralized error handling for the mobile app

import Toast from 'react-native-toast-message';

import { UI_CONSTANTS } from '../config/constants';

import { debugLogger } from './debugLogger';
import { Alert } from './platformUtils';

export interface AppError {
  message: string;
  code?: string;
  status?: number;
  details?: unknown;
  timestamp: Date;
}

interface APIErrorResponse {
  response?: {
    status: number;
    data?: {
      message?: string;
      errors?: ({ message?: string } | string)[];
    };
  };
  message?: string;
  code?: string;
}

interface NetworkError {
  code: string;
  message: string;
}

interface ValidationError extends APIErrorResponse {
  response: {
    status: 400;
    data: {
      errors: ({ message?: string } | string)[];
    };
  };
}

interface ToastOptions {
  action?: {
    text: string;
    onPress: () => void;
  };
  duration?: number;
}

export class ErrorHandler {
  private static instance: ErrorHandler;

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle API errors with user-friendly messages
   */
  handleApiError(error: APIErrorResponse, context?: string): AppError {
    const appError: AppError = {
      message: this.getErrorMessage(error),
      code: error.code || error.response?.status?.toString(),
      status: error.response?.status,
      details: error.response?.data,
      timestamp: new Date(),
    };

    // Log error for debugging
    debugLogger.error(`API Error${context ? ` in ${context}` : ''}:`, appError);

    // Show user-friendly message
    this.showErrorToast(appError.message);

    return appError;
  }

  /**
   * Handle network errors with retry mechanism
   */
  handleNetworkError(error: NetworkError | APIErrorResponse, context?: string): AppError {
    const appError: AppError = {
      message: 'Network connection failed. Please check your internet connection and try again.',
      code: 'NETWORK_ERROR',
      status: 0,
      details: error,
      timestamp: new Date(),
    };

    debugLogger.error(`Network Error${context ? ` in ${context}` : ''}:`, appError);

    // Enhanced network error handling with retry suggestion
    this.showErrorToast(appError.message, {
      action: {
        text: 'Retry',
        onPress: () => {
          // Trigger a retry mechanism if context supports it
          if (context && this.retryCallbacks.has(context)) {
            this.retryCallbacks.get(context)?.();
          }
        }
      }
    });

    return appError;
  }

  private retryCallbacks = new Map<string, () => void>();

  /**
   * Register a retry callback for a specific context
   */
  registerRetryCallback(context: string, callback: () => void): void {
    this.retryCallbacks.set(context, callback);
  }

  /**
   * Unregister a retry callback
   */
  unregisterRetryCallback(context: string): void {
    this.retryCallbacks.delete(context);
  }

  /**
   * Handle authentication errors
   */
  handleAuthError(error: APIErrorResponse, context?: string): AppError {
    const appError: AppError = {
      message: 'Authentication failed. Please login again.',
      code: 'AUTH_ERROR',
      status: 401,
      details: error,
      timestamp: new Date(),
    };

    debugLogger.error(`Auth Error${context ? ` in ${context}` : ''}:`, appError);
    this.showErrorAlert('Authentication Error', appError.message);

    return appError;
  }

  /**
   * Handle validation errors
   */
  handleValidationError(error: ValidationError | APIErrorResponse, context?: string): AppError {
    const appError: AppError = {
      message: this.getValidationMessage(error),
      code: 'VALIDATION_ERROR',
      status: 400,
      details: error,
      timestamp: new Date(),
    };

    debugLogger.error(`Validation Error${context ? ` in ${context}` : ''}:`, appError);
    this.showErrorToast(appError.message);

    return appError;
  }

  /**
   * Handle unexpected errors
   */
  handleUnexpectedError(error: Error | APIErrorResponse, context?: string): AppError {
    const appError: AppError = {
      message: 'An unexpected error occurred. Please try again.',
      code: 'UNEXPECTED_ERROR',
      details: error,
      timestamp: new Date(),
    };

    debugLogger.error(`Unexpected Error${context ? ` in ${context}` : ''}:`, appError);
    this.showErrorAlert('Error', appError.message);

    return appError;
  }

  /**
   * Extract user-friendly error message
   */
  private getErrorMessage(error: APIErrorResponse): string {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }

    if (error.message) {
      return error.message;
    }

    if (error.response?.status) {
      switch (error.response.status) {
        case 400:
          return 'Invalid request. Please check your input.';
        case 401:
          return 'Authentication required. Please login.';
        case 403:
          return 'Access denied. You do not have permission.';
        case 404:
          return 'Resource not found.';
        case 500:
          return 'Server error. Please try again later.';
        default:
          return 'An error occurred. Please try again.';
      }
    }

    return 'An unexpected error occurred.';
  }

  /**
   * Extract validation error message
   */
  private getValidationMessage(error: ValidationError | APIErrorResponse): string {
    if (error.response?.data?.errors) {
      const errors = error.response.data.errors;
      if (Array.isArray(errors) && errors.length > 0) {
        const firstError = errors[0];
      return typeof firstError === 'string' ? firstError : firstError?.message || 'Validation error';
      }
    }

    return this.getErrorMessage(error);
  }

  /**
   * Show error toast with optional action button
   */
  private showErrorToast(message: string, options?: ToastOptions): void {
    if (options?.action) {
      // Show toast with action button using Alert for better UX
      Alert.alert(
        'Error',
        message,
        [
          { text: 'Dismiss', style: 'cancel' },
          { text: options.action.text, onPress: options.action.onPress }
        ]
      );
    } else {
      // Standard toast
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: message,
        position: 'bottom',
        visibilityTime: options?.duration || UI_CONSTANTS.TOAST_DURATION_MEDIUM,
      });
    }
  }

  /**
   * Show error alert
   */
  private showErrorAlert(title: string, message: string): void {
    Alert.alert(
      title,
      message,
      [{ text: 'OK', style: 'default' }],

    );
  }

  /**
   * Determine error type and handle accordingly
   */
  handleError(error: APIErrorResponse | NetworkError | Error, context?: string): AppError {
    if ('code' in error && (error.code === 'NETWORK_ERROR' || error.message?.includes('Network'))) {
      return this.handleNetworkError(error as NetworkError, context);
    }

    if ('response' in error && error.response?.status === 401) {
      return this.handleAuthError(error, context);
    }

    if ('response' in error && error.response?.status === 400) {
      return this.handleValidationError(error, context);
    }

    if ('response' in error && error.response?.status && error.response.status >= 400 && error.response.status < 500) {
      return this.handleApiError(error, context);
    }

    return this.handleUnexpectedError(error, context);
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Export convenience functions
export const handleApiError = (error: APIErrorResponse, context?: string) => errorHandler.handleApiError(error, context);
export const handleNetworkError = (error: NetworkError | APIErrorResponse, context?: string) => errorHandler.handleNetworkError(error, context);
export const handleAuthError = (error: APIErrorResponse, context?: string) => errorHandler.handleAuthError(error, context);
export const handleValidationError = (error: ValidationError | APIErrorResponse, context?: string) => errorHandler.handleValidationError(error, context);
export const handleUnexpectedError = (error: Error | APIErrorResponse, context?: string) => errorHandler.handleUnexpectedError(error, context);
export const handleError = (error: APIErrorResponse | NetworkError | Error, context?: string) => errorHandler.handleError(error, context);
