#!/bin/bash

# CTRON Home - Codebase Cleanup Script
# This script performs routine cleanup tasks across the entire codebase

echo "🧹 Starting CTRON Home codebase cleanup..."

# Remove build artifacts
echo "📦 Cleaning build artifacts..."
rm -rf backend/dist
rm -rf web/dist
rm -rf ctron-mobile-new/dist
rm -rf ctron-mobile-new/.expo
rm -rf ctron-mobile-new/.metro-cache

# Clean node_modules if requested
if [ "$1" = "--deep" ]; then
    echo "🗑️  Deep clean: removing node_modules..."
    rm -rf backend/node_modules
    rm -rf web/node_modules
    rm -rf ctron-mobile-new/node_modules
    
    echo "📥 Reinstalling dependencies..."
    cd backend && npm install
    cd ../web && npm install
    cd ../ctron-mobile-new && yarn install
    cd ..
fi

# Clean temporary files
echo "🧽 Cleaning temporary files..."
find . -name "*.log" -type f -delete
find . -name ".DS_Store" -type f -delete
find . -name "Thumbs.db" -type f -delete
find . -name "*.tmp" -type f -delete

# Clean TypeScript build info
find . -name "*.tsbuildinfo" -type f -delete

echo "✅ Cleanup completed!"
echo ""
echo "📊 Cleanup Summary:"
echo "   - Build artifacts removed"
echo "   - Temporary files cleaned"
echo "   - TypeScript build cache cleared"

if [ "$1" = "--deep" ]; then
    echo "   - Dependencies reinstalled"
fi

echo ""
echo "💡 Usage:"
echo "   ./scripts/cleanup.sh        # Standard cleanup"
echo "   ./scripts/cleanup.sh --deep # Deep clean with dependency reinstall"