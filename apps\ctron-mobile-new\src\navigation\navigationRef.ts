/**
 * Navigation Reference
 * 
 * This module provides a navigation reference that can be used outside of React components
 * to access navigation functionality. This is useful for navigating from non-React contexts
 * such as Redux actions, API callbacks, or native modules.
 */
import { createNavigationContainerRef } from '@react-navigation/native';

import { RootStackParamList } from '../types/navigation';

/**
 * Global navigation reference that can be used to navigate outside of React components
 * 
 * Usage example:
 * ```
 * // Navigate to a screen
 * if (navigationRef.isReady()) {
 *   navigationRef.navigate('Home');
 * }
 * 
 * // Go back
 * if (navigationRef.isReady() && navigationRef.canGoBack()) {
 *   navigationRef.goBack();
 * }
 * ```
 */
export const navigationRef = createNavigationContainerRef<RootStackParamList>();