// CTRON Home - Modern Payment Screen
// Enhanced payment interface with multiple payment methods and improved UX

import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import type { RouteProp } from '@react-navigation/native';
import { useNavigation, useRoute } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState, useEffect, useMemo, useRef } from 'react';

import type { ViewStyle } from 'react-native';

import { JobAPI } from '../../api/job.api';
import PaymentAPI from '../../api/payment.api';
import { Header } from '../../components/ui/Header';
import { useTheme } from '../../context/ThemeContext';
import { Button, Card } from '../../design-system';
import type { HomeownerStackParamList } from '../../navigation/types';
import type { Job } from '../../types/job';
import { Animated } from '../../utils/platformUtils';
import { 
  Alert, 
  StyleSheet, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  View, 
  ScrollView, 
  ActivityIndicator,
  Dimensions,
  StatusBar,
  KeyboardAvoidingView,
  Platform
} from '../../utils/platformUtils';

type PaymentScreenNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'Payment'>;
type PaymentScreenRouteProp = RouteProp<HomeownerStackParamList, 'Payment'>;

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer';
  name: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  enabled: boolean;
  processingFee?: number;
}

interface CardDetails {
  number: string;
  expiryMonth: string;
  expiryYear: string;
  cvv: string;
  name: string;
}

interface PaymentBreakdown {
  subtotal: number;
  tax: number;
  processingFee: number;
  discount?: number;
  total: number;
}

const { width: screenWidth } = Dimensions.get('window');

const paymentMethods: PaymentMethod[] = [
  {
    id: 'card',
    type: 'card',
    name: 'Credit/Debit Card',
    description: 'Visa, Mastercard, American Express',
    icon: 'card-outline',
    enabled: true,
    processingFee: 0.029
  },
  {
    id: 'paypal',
    type: 'paypal',
    name: 'PayPal',
    description: 'Pay with your PayPal account',
    icon: 'logo-paypal',
    enabled: true,
    processingFee: 0.034
  },
  {
    id: 'apple_pay',
    type: 'apple_pay',
    name: 'Apple Pay',
    description: 'Touch ID or Face ID',
    icon: 'logo-apple',
    enabled: Platform.OS === 'ios',
    processingFee: 0.029
  },
  {
    id: 'google_pay',
    type: 'google_pay',
    name: 'Google Pay',
    description: 'Quick and secure',
    icon: 'logo-google',
    enabled: Platform.OS === 'android',
    processingFee: 0.029
  },
  {
    id: 'bank_transfer',
    type: 'bank_transfer',
    name: 'Bank Transfer',
    description: 'Direct bank transfer (2-3 days)',
    icon: 'business-outline',
    enabled: true,
    processingFee: 0
  }
];

export default function ModernPaymentScreen() {
  const navigation = useNavigation<PaymentScreenNavigationProp>();
  const route = useRoute<PaymentScreenRouteProp>();
  const { colors, spacing, typography, borderRadius } = useTheme();
  const { jobId } = route.params as { jobId: string };

  // State
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [amount, setAmount] = useState('');
  const [tip, setTip] = useState(0);
  const [cardDetails, setCardDetails] = useState<CardDetails>({
    number: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    name: ''
  });
  const [paymentBreakdown, setPaymentBreakdown] = useState<PaymentBreakdown>({
    subtotal: 0,
    tax: 0,
    processingFee: 0,
    total: 0
  });
  const [cardErrors, setCardErrors] = useState<Partial<CardDetails>>({});

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;

  const tipOptions = [0, 10, 15, 20, 25];

  useEffect(() => {
    loadJobDetails();
    
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  useEffect(() => {
    if (amount) {
      calculatePaymentBreakdown();
    }
  }, [amount, tip, selectedMethod]);

  const loadJobDetails = async () => {
    try {
      const jobData = await JobAPI.getJobById(jobId);
      setJob(jobData);
      
      // Set default amount based on job price
      const defaultAmount = (jobData.price ?? 100);
      setAmount(defaultAmount.toString());
      
      // Set default payment method
      setSelectedMethod(paymentMethods.find(m => m.enabled) || paymentMethods[0]);
    } catch (error) {
      Alert.alert('Error', 'Failed to load job details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const calculatePaymentBreakdown = () => {
    const subtotal = parseFloat(amount) || 0;
    const tipAmount = (subtotal * tip) / 100;
    const taxRate = 0.08; // 8% tax
    const tax = (subtotal + tipAmount) * taxRate;
    const processingFee = selectedMethod ? (subtotal + tipAmount + tax) * (selectedMethod.processingFee || 0) : 0;
    const total = subtotal + tipAmount + tax + processingFee;

    setPaymentBreakdown({
      subtotal: subtotal + tipAmount,
      tax,
      processingFee,
      total
    });
  };

  const validateCardDetails = (): boolean => {
    const errors: Partial<CardDetails> = {};

    if (!cardDetails.number.replace(/\s/g, '')) {
      errors.number = 'Card number is required';
    } else if (cardDetails.number.replace(/\s/g, '').length < 13) {
      errors.number = 'Invalid card number';
    }

    if (!cardDetails.expiryMonth) {
      errors.expiryMonth = 'Month required';
    } else if (parseInt(cardDetails.expiryMonth) < 1 || parseInt(cardDetails.expiryMonth) > 12) {
      errors.expiryMonth = 'Invalid month';
    }

    if (!cardDetails.expiryYear) {
      errors.expiryYear = 'Year required';
    } else if (parseInt(cardDetails.expiryYear) < new Date().getFullYear() % 100) {
      errors.expiryYear = 'Card expired';
    }

    if (!cardDetails.cvv) {
      errors.cvv = 'CVV required';
    } else if (cardDetails.cvv.length < 3) {
      errors.cvv = 'Invalid CVV';
    }

    if (!cardDetails.name.trim()) {
      errors.name = 'Cardholder name required';
    }

    setCardErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const formatCardNumber = (text: string) => {
    const cleaned = text.replace(/\s/g, '');
    const formatted = cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
    return formatted;
  };

  const handlePayment = async () => {
    if (!selectedMethod) {
      Alert.alert('Error', 'Please select a payment method');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    if (selectedMethod.type === 'card' && !validateCardDetails()) {
      // Shake animation for errors
      Animated.sequence([
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 0, duration: 100, useNativeDriver: true })
      ]).start();
      return;
    }

    setProcessing(true);
    try {
      const paymentData = {
        jobId,
        amount: paymentBreakdown.total,
        method: selectedMethod.type,
        tip,
        ...(selectedMethod.type === 'card' && { cardDetails })
      };

      if (!job) {
        throw new Error('Job not found');
      }
      
      await PaymentAPI.createPayment(job.id, paymentBreakdown.total);
      
      Alert.alert(
        'Payment Successful!',
        `Your payment of £${paymentBreakdown.total.toFixed(2)} has been processed successfully.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('Home')
          }
        ]
      );
    } catch (error) {
      Alert.alert('Payment Failed', 'There was an error processing your payment. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const PaymentMethodCard = ({ method, isSelected, onPress }: {
    method: PaymentMethod;
    isSelected: boolean;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      style={[styles.paymentMethodCard, isSelected && styles.selectedPaymentMethodCard]}
      onPress={onPress}
      disabled={!method.enabled}
    >
      <View style={styles.paymentMethodGlass}>
        <BlurView intensity={isSelected ? 25 : 15} />
      </View>
      <View style={styles.paymentMethodContent}>
        <View style={[styles.paymentMethodIcon, { backgroundColor: isSelected ? colors.primary.main + '20' : colors.neutral.light + '20' }]}>
          <Ionicons
            name={method.icon}
            size={24}
            color={isSelected ? colors.primary.main : colors.text.secondary}
          />
        </View>
        <View style={styles.paymentMethodInfo}>
          <Text style={[styles.paymentMethodName, isSelected && styles.selectedPaymentMethodName]}>
            {method.name}
          </Text>
          <Text style={styles.paymentMethodDescription}>{method.description}</Text>
          {(method.processingFee || 0) > 0 && (
            <Text style={styles.processingFeeText}>
              {((method.processingFee || 0) * 100).toFixed(1)}% processing fee
            </Text>
          )}
        </View>
        {isSelected && (
          <View style={styles.selectedIndicator}>
            <Ionicons name="checkmark-circle" size={20} color={colors.primary.main} />
          </View>
        )}
      </View>
      {!method.enabled && (
        <View style={styles.disabledOverlay}>
          <Text style={styles.disabledText}>Not Available</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const TipButton = ({ percentage, isSelected, onPress }: {
    percentage: number;
    isSelected: boolean;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      style={[styles.tipButton, isSelected && styles.selectedTipButton]}
      onPress={onPress}
    >
      <Text style={[styles.tipButtonText, isSelected && styles.selectedTipButtonText]}>
        {percentage === 0 ? 'No Tip' : `${percentage}%`}
      </Text>
    </TouchableOpacity>
  );

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    gradientBackground: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: 250,
    },
    header: {
      paddingTop: StatusBar.currentHeight || 44,
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.md,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    headerSpacer: {
      width: 40,
    },
    keyboardView: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.xl,
    },
    summaryCard: {
      marginBottom: spacing.xl,
    },
    summaryCardGlass: {
      borderRadius: borderRadius.xl,
      padding: spacing.xl,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.15)',
    },
    summaryTitle: {
      fontSize: typography.fontSize.xxl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    summarySubtitle: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginBottom: spacing.lg,
    },
    jobInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    jobIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: colors.primary.main + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    jobDetails: {
      flex: 1,
    },
    jobTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    jobId: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    amountContainer: {
      marginBottom: spacing.xl,
    },
    amountLabel: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    amountInputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderRadius: borderRadius.md,
      paddingHorizontal: spacing.md,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    currencySymbol: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.primary.main,
      marginRight: spacing.sm,
    },
    amountInput: {
      flex: 1,
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      paddingVertical: spacing.md,
    },
    tipContainer: {
      marginBottom: spacing.xl,
    },
    tipLabel: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    tipButtons: {
      flexDirection: 'row',
      gap: spacing.sm,
    },
    tipButton: {
      flex: 1,
      paddingVertical: spacing.md,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderRadius: borderRadius.md,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
      alignItems: 'center',
    },
    selectedTipButton: {
      backgroundColor: colors.primary.main + '20',
      borderColor: colors.primary.main,
    },
    tipButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.secondary,
    },
    selectedTipButtonText: {
      color: colors.primary.main,
    },
    paymentMethodsCard: {
      marginBottom: spacing.xl,
    },
    paymentMethodsCardGlass: {
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    sectionTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.lg,
    },
    paymentMethodCard: {
      marginBottom: spacing.md,
    },
    selectedPaymentMethodCard: {
      transform: [{ scale: 1.02 }],
    },
    paymentMethodGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.md,
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.1)',
    },
    paymentMethodContent: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
    },
    paymentMethodIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    paymentMethodInfo: {
      flex: 1,
    },
    paymentMethodName: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    selectedPaymentMethodName: {
      color: colors.primary.main,
    },
    paymentMethodDescription: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    processingFeeText: {
      fontSize: typography.fontSize.xs,
      color: colors.semantic.warning,
      marginTop: spacing.xs,
    },
    selectedIndicator: {
      marginLeft: spacing.sm,
    },
    disabledOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      borderRadius: borderRadius.md,
      justifyContent: 'center',
      alignItems: 'center',
    },
    disabledText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      fontWeight: typography.fontWeight.medium,
    },
    cardDetailsCard: {
      marginBottom: spacing.xl,
    },
    cardDetailsCardGlass: {
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    cardInputContainer: {
      marginBottom: spacing.md,
    },
    cardInputLabel: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    cardInputWrapper: {
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      borderRadius: borderRadius.sm,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.1)',
    },
    cardInputWrapperError: {
      borderColor: colors.error.main,
    },
    cardInput: {
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
    },
    cardInputRow: {
      flexDirection: 'row',
      gap: spacing.md,
    },
    cardInputHalf: {
      flex: 1,
    },
    cardInputThird: {
      flex: 1,
    },
    cardErrorText: {
      fontSize: typography.fontSize.xs,
      color: colors.error.main,
      marginTop: spacing.xs,
    },
    breakdownCard: {
      marginBottom: spacing.xl,
    },
    breakdownCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    breakdownContent: {
      padding: spacing.lg,
      borderRadius: borderRadius.lg,
    },
    breakdownRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    breakdownLabel: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
    },
    breakdownValue: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.medium,
      color: colors.text.primary,
    },
    breakdownDivider: {
      height: 1,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      marginVertical: spacing.md,
    },
    breakdownTotal: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    breakdownTotalLabel: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
    },
    breakdownTotalValue: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.primary.main,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginTop: spacing.md,
    },
    footer: {
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.lg,
      paddingTop: spacing.md,
      backgroundColor: colors.background.primary,
      borderTopWidth: 1,
      borderTopColor: 'rgba(255, 255, 255, 0.1)',
    },
    securityNote: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.md,
    },
    securityIcon: {
      marginRight: spacing.xs,
    },
    securityText: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
    },
  }), [colors, spacing, typography, borderRadius]);

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.gradientBackground}>
          <LinearGradient
            colors={['#667eea', '#764ba2', '#f093fb']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary.main} />
          <Text style={styles.loadingText}>Loading payment details...</Text>
        </View>
      </View>
    );
  }

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }, { translateX: shakeAnim }]
        }
      ]}
    >
      <View style={styles.gradientBackground}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={20} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Payment</Text>
          <View style={styles.headerSpacer} />
        </View>
      </View>

      <KeyboardAvoidingView 
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.content}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Summary Card */}
          <View style={styles.summaryCard}>
            <View style={styles.summaryCardGlass}>
              <BlurView intensity={20} />
            </View>
            <View style={styles.summaryContent}>
              <Text style={styles.summaryTitle}>Payment Summary</Text>
              <Text style={styles.summarySubtitle}>
                Complete your payment for the completed service
              </Text>
              
              {job && (
                <View style={styles.jobInfo}>
                  <View style={styles.jobIcon}>
                    <Ionicons name="construct" size={24} color={colors.primary.main} />
                  </View>
                  <View style={styles.jobDetails}>
                    <Text style={styles.jobTitle}>{job.issue}</Text>
                    <Text style={styles.jobId}>Job #{job?.id?.slice(-8)}</Text>
                  </View>
                </View>
              )}
            </View>
          </View>

          {/* Amount Input */}
          <View style={styles.summaryCard}>
            <View style={styles.summaryCardGlass}>
              <BlurView intensity={15} />
            </View>
            <View style={styles.summaryContent}>
              <View style={styles.amountContainer}>
                <Text style={styles.amountLabel}>Amount</Text>
                <View style={styles.amountInputWrapper}>
                  <Text style={styles.currencySymbol}>£</Text>
                  <TextInput
                    style={styles.amountInput}
                    value={amount}
                    onChangeText={(text: string) => setAmount(text)}
                    placeholder="0.00"
                    placeholderTextColor={colors.text.secondary}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              {/* Tip Selection */}
              <View style={styles.tipContainer}>
                <Text style={styles.tipLabel}>Add Tip (Optional)</Text>
                <View style={styles.tipButtons}>
                  {tipOptions.map((percentage) => (
                    <TipButton
                      key={percentage}
                      percentage={percentage}
                      isSelected={tip === percentage}
                      onPress={() => setTip(percentage)}
                    />
                  ))}
                </View>
              </View>
            </View>
          </View>

          {/* Payment Methods */}
          <View style={styles.paymentMethodsCard}>
            <View style={styles.paymentMethodsCardGlass}>
              <BlurView intensity={15} />
            </View>
            <View style={styles.paymentMethodsContent}>
              <Text style={styles.sectionTitle}>Payment Method</Text>
              {paymentMethods.map((method) => (
                <PaymentMethodCard
                  key={method.id}
                  method={method}
                  isSelected={selectedMethod?.id === method.id}
                  onPress={() => method.enabled && setSelectedMethod(method)}
                />
              ))}
            </View>
          </View>

          {/* Card Details */}
          {selectedMethod?.type === 'card' && (
            <View style={styles.cardDetailsCard}>
              <View style={styles.cardDetailsCardGlass}>
                <BlurView intensity={15} />
              </View>
              <View style={styles.cardDetailsContent}>
                <Text style={styles.sectionTitle}>Card Details</Text>
                
                <View style={styles.cardInputContainer}>
                  <Text style={styles.cardInputLabel}>Card Number</Text>
                  <View style={[styles.cardInputWrapper, cardErrors.number && styles.cardInputWrapperError]}>
                    <TextInput
                      style={styles.cardInput}
                      value={cardDetails.number}
                      onChangeText={(text: string) => setCardDetails(prev => ({ ...prev, number: formatCardNumber(text) }))}
                      placeholder="1234 5678 9012 3456"
                      placeholderTextColor={colors.text.secondary}
                      keyboardType="numeric"
                      maxLength={19}
                    />
                  </View>
                  {cardErrors.number && <Text style={styles.cardErrorText}>{cardErrors.number}</Text>}
                </View>

                <View style={styles.cardInputRow}>
                  <View style={[styles.cardInputContainer, styles.cardInputThird]}>
                    <Text style={styles.cardInputLabel}>Month</Text>
                    <View style={[styles.cardInputWrapper, cardErrors.expiryMonth && styles.cardInputWrapperError]}>
                      <TextInput
                        style={styles.cardInput}
                        value={cardDetails.expiryMonth}
                        onChangeText={(text: string) => setCardDetails(prev => ({ ...prev, expiryMonth: text }))}
                        placeholder="MM"
                        placeholderTextColor={colors.text.secondary}
                        keyboardType="numeric"
                        maxLength={2}
                      />
                    </View>
                    {cardErrors.expiryMonth && <Text style={styles.cardErrorText}>{cardErrors.expiryMonth}</Text>}
                  </View>
                  
                  <View style={[styles.cardInputContainer, styles.cardInputThird]}>
                    <Text style={styles.cardInputLabel}>Year</Text>
                    <View style={[styles.cardInputWrapper, cardErrors.expiryYear && styles.cardInputWrapperError]}>
                      <TextInput
                        style={styles.cardInput}
                        value={cardDetails.expiryYear}
                        onChangeText={(text: string) => setCardDetails(prev => ({ ...prev, expiryYear: text }))}
                        placeholder="YY"
                        placeholderTextColor={colors.text.secondary}
                        keyboardType="numeric"
                        maxLength={2}
                      />
                    </View>
                    {cardErrors.expiryYear && <Text style={styles.cardErrorText}>{cardErrors.expiryYear}</Text>}
                  </View>
                  
                  <View style={[styles.cardInputContainer, styles.cardInputThird]}>
                    <Text style={styles.cardInputLabel}>CVV</Text>
                    <View style={[styles.cardInputWrapper, cardErrors.cvv && styles.cardInputWrapperError]}>
                      <TextInput
                        style={styles.cardInput}
                        value={cardDetails.cvv}
                        onChangeText={(text: string) => setCardDetails(prev => ({ ...prev, cvv: text }))}
                        placeholder="123"
                        placeholderTextColor={colors.text.secondary}
                        keyboardType="numeric"
                        maxLength={4}
                        secureTextEntry
                      />
                    </View>
                    {cardErrors.cvv && <Text style={styles.cardErrorText}>{cardErrors.cvv}</Text>}
                  </View>
                </View>

                <View style={styles.cardInputContainer}>
                  <Text style={styles.cardInputLabel}>Cardholder Name</Text>
                  <View style={[styles.cardInputWrapper, cardErrors.name && styles.cardInputWrapperError]}>
                    <TextInput
                      style={styles.cardInput}
                      value={cardDetails.name}
                      onChangeText={(text: string) => setCardDetails(prev => ({ ...prev, name: text }))}
                      placeholder="John Smith"
                      placeholderTextColor={colors.text.secondary}
                      autoCapitalize="words"
                    />
                  </View>
                  {cardErrors.name && <Text style={styles.cardErrorText}>{cardErrors.name}</Text>}
                </View>
              </View>
            </View>
          )}

          {/* Payment Breakdown */}
          <View style={styles.breakdownCard}>
            <View style={styles.breakdownCardGlass}>
              <BlurView intensity={15} />
            </View>
            <View style={styles.breakdownContent}>
              <Text style={styles.sectionTitle}>Payment Breakdown</Text>
              
              <View style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>Subtotal</Text>
                <Text style={styles.breakdownValue}>£{paymentBreakdown.subtotal.toFixed(2)}</Text>
              </View>
              
              <View style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>Tax (8%)</Text>
                <Text style={styles.breakdownValue}>£{paymentBreakdown.tax.toFixed(2)}</Text>
              </View>
              
              {paymentBreakdown.processingFee > 0 && (
                <View style={styles.breakdownRow}>
                  <Text style={styles.breakdownLabel}>Processing Fee</Text>
                  <Text style={styles.breakdownValue}>£{paymentBreakdown.processingFee.toFixed(2)}</Text>
                </View>
              )}
              
              <View style={styles.breakdownDivider} />
              
              <View style={styles.breakdownTotal}>
                <Text style={styles.breakdownTotalLabel}>Total</Text>
                <Text style={styles.breakdownTotalValue}>£{paymentBreakdown.total.toFixed(2)}</Text>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.securityNote}>
            <Ionicons name="shield-checkmark" size={16} color={colors.semantic.success} style={styles.securityIcon} />
            <Text style={styles.securityText}>Your payment is secured with 256-bit SSL encryption</Text>
          </View>
          
          <Button
            title={`Pay £${paymentBreakdown.total.toFixed(2)}`}
            onPress={handlePayment}
            variant="primary"
            size="lg"
            loading={processing}
            disabled={!selectedMethod || !amount || parseFloat(amount) <= 0}
            icon="card-outline"
          />
        </View>
      </KeyboardAvoidingView>
    </Animated.View>
  );
}