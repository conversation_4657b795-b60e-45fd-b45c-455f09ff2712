#!/usr/bin/env node

/**
 * Bundle Size Analyzer for CTRON Mobile App
 * Analyzes the app bundle and provides optimization recommendations
 */

const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

const BUNDLE_SIZE_LIMITS = {
  WARNING: 50 * 1024 * 1024, // 50MB
  ERROR: 100 * 1024 * 1024,  // 100MB
};

const LARGE_DEPENDENCIES = [
  '@react-navigation',
  'react-native-reanimated',
  'expo-linear-gradient',
  'react-native-svg',
  '@stripe/stripe-react-native',
  'socket.io-client',
  'axios'
];

function analyzeBundle() {
  console.log(chalk.blue('📊 Analyzing CTRON Mobile App Bundle Size...\n'));

  // Check if bundle exists
  const distPath = path.join(__dirname, '..', 'dist');
  if (!fs.existsSync(distPath)) {
    console.log(chalk.yellow('⚠️  No bundle found. Run "expo export" first to generate bundle.'));
    return;
  }

  // Analyze package.json dependencies
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  console.log(chalk.green('📦 Dependency Analysis:'));
  console.log(`Total dependencies: ${Object.keys(packageJson.dependencies || {}).length}`);
  console.log(`Total devDependencies: ${Object.keys(packageJson.devDependencies || {}).length}\n`);

  // Check for large dependencies
  console.log(chalk.yellow('🔍 Large Dependencies Detected:'));
  LARGE_DEPENDENCIES.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`  - ${dep}: ${packageJson.dependencies[dep]}`);
    }
  });

  // Bundle size recommendations
  console.log(chalk.blue('\n💡 Bundle Optimization Recommendations:\n'));
  
  const recommendations = [
    {
      title: 'Tree Shaking',
      description: 'Import only what you need from large libraries',
      example: 'import { specific } from "library" instead of import * as library'
    },
    {
      title: 'Code Splitting',
      description: 'Use React.lazy() for screen components that are not immediately needed',
      example: 'const LazyScreen = React.lazy(() => import("./LazyScreen"))'
    },
    {
      title: 'Image Optimization',
      description: 'Use WebP format and appropriate sizes for images',
      example: 'Compress images and use expo-image for better performance'
    },
    {
      title: 'Remove Unused Dependencies',
      description: 'Regularly audit and remove unused packages',
      example: 'Use "npx depcheck" to find unused dependencies'
    },
    {
      title: 'Bundle Analysis',
      description: 'Use Metro bundle analyzer to identify large modules',
      example: 'Run "npx react-native-bundle-visualizer" for detailed analysis'
    }
  ];

  recommendations.forEach((rec, index) => {
    console.log(chalk.cyan(`${index + 1}. ${rec.title}`));
    console.log(`   ${rec.description}`);
    console.log(chalk.gray(`   Example: ${rec.example}\n`));
  });

  // Performance tips
  console.log(chalk.magenta('⚡ Performance Tips:\n'));
  
  const performanceTips = [
    'Use FlatList instead of ScrollView for large lists',
    'Implement proper image caching with expo-image',
    'Use React.memo() for components that render frequently',
    'Optimize animations with native driver when possible',
    'Implement proper error boundaries to prevent crashes',
    'Use AsyncStorage efficiently with proper serialization'
  ];

  performanceTips.forEach((tip, index) => {
    console.log(`${index + 1}. ${tip}`);
  });

  console.log(chalk.green('\n✅ Bundle analysis complete!'));
  console.log(chalk.blue('💡 Run "expo export --dump-assetmap" for detailed bundle analysis.'));
}

// Check for specific optimization opportunities
function checkOptimizationOpportunities() {
  const srcPath = path.join(__dirname, '..', 'src');
  
  console.log(chalk.blue('\n🔍 Checking for optimization opportunities...\n'));

  // Check for large files
  function checkLargeFiles(dir, files = []) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.includes('node_modules')) {
        checkLargeFiles(fullPath, files);
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
        if (stat.size > 50 * 1024) { // Files larger than 50KB
          files.push({
            path: fullPath.replace(srcPath, ''),
            size: stat.size
          });
        }
      }
    });
    
    return files;
  }

  const largeFiles = checkLargeFiles(srcPath);
  
  if (largeFiles.length > 0) {
    console.log(chalk.yellow('📁 Large Source Files (>50KB):'));
    largeFiles
      .sort((a, b) => b.size - a.size)
      .slice(0, 10)
      .forEach(file => {
        const sizeKB = (file.size / 1024).toFixed(1);
        console.log(`  - ${file.path}: ${sizeKB}KB`);
      });
  } else {
    console.log(chalk.green('✅ No unusually large source files found.'));
  }
}

// Main execution
if (require.main === module) {
  analyzeBundle();
  checkOptimizationOpportunities();
}

module.exports = { analyzeBundle, checkOptimizationOpportunities };