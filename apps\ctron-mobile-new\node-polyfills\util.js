// Util polyfill for React Native
// This provides a minimal util implementation for Node.js modules

const util = {
  format: (f, ...args) => {
    if (typeof f !== 'string') {
      return [f, ...args].map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ');
    }
    
    let i = 0;
    const str = String(f).replace(/%[sdj%]/g, (x) => {
      if (x === '%%') return x;
      if (i >= args.length) return x;
      switch (x) {
        case '%s': return String(args[i++]);
        case '%d': return Number(args[i++]);
        case '%j':
          try {
            return JSON.stringify(args[i++]);
          } catch (_) {
            return '[Circular]';
          }
        default:
          return x;
      }
    });
    
    return str;
  },
  
  inspect: (obj) => {
    if (obj === null) return 'null';
    if (typeof obj === 'undefined') return 'undefined';
    if (typeof obj === 'string') return `'${obj}'`;
    if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);
    if (typeof obj === 'function') return obj.toString();
    
    try {
      return JSON.stringify(obj, null, 2);
    } catch (e) {
      return '[object Object]';
    }
  },
  
  isArray: Array.isArray,
  isDate: (obj) => obj instanceof Date,
  isError: (obj) => obj instanceof Error,
  isFunction: (obj) => typeof obj === 'function',
  isObject: (obj) => typeof obj === 'object' && obj !== null,
  isString: (obj) => typeof obj === 'string',
  isNumber: (obj) => typeof obj === 'number',
  isBoolean: (obj) => typeof obj === 'boolean',
  isNull: (obj) => obj === null,
  isUndefined: (obj) => typeof obj === 'undefined',
  
  inherits: (ctor, superCtor) => {
    ctor.super_ = superCtor;
    ctor.prototype = Object.create(superCtor.prototype, {
      constructor: {
        value: ctor,
        enumerable: false,
        writable: true,
        configurable: true
      }
    });
  }
};

module.exports = util;