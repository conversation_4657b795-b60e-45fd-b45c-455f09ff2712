// src/screens/Technician/AcceptJobScreen.tsx

import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import React, { useMemo, useState, useEffect } from 'react';

import { JobAPI } from '../../api/job.api'; // ✅ default export
import type { TechnicianStackParamList } from '../../types/navigation';
import type { Job as JobType } from '../../types/job';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from '../../utils/platformUtils';

interface Job {
  id: string;
  issue: string;
  scheduledAt: string;
}

type RouteParams = {
  job: Job;
};

interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
}

type AcceptJobScreenRouteProp = RouteProp<TechnicianStackParamList, 'AcceptJob'>;
type AcceptJobScreenNavigationProp = StackNavigationProp<TechnicianStackParamList, 'AcceptJob'>;

const AcceptJobScreen = () => {
  const route = useRoute<AcceptJobScreenRouteProp>();
  const navigation = useNavigation<AcceptJobScreenNavigationProp>();
  const { jobId } = route.params;
  
  const [job, setJob] = useState<JobType | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchJobDetails = async () => {
      try {
        const jobData = await JobAPI.getJobDetails(jobId);
        setJob(jobData);
      } catch (error) {
        console.error('Failed to fetch job details:', error);
        Alert.alert('Error', 'Failed to load job details');
      } finally {
        setLoading(false);
      }
    };

    fetchJobDetails();
  }, [jobId]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      padding: 24,
      backgroundColor: '#fff',
    },
    title: {
      fontSize: 22,
      fontWeight: 'bold',
      marginBottom: 16,
    },
    label: {
      marginTop: 12,
      fontWeight: '600',
    },
    value: {
      fontSize: 16,
      marginTop: 4,
      color: '#333',
    },
    button: {
      marginTop: 32,
      backgroundColor: '#004AAD',
      padding: 16,
      borderRadius: 8,
      alignItems: 'center',
    },
    buttonText: {
      color: '#fff',
      fontWeight: '600',
      fontSize: 16,
    },
  }), []);

  const handleAccept = async () => {
    try {
      if (job) {
        await JobAPI.updateStatus(job.id, 'IN_PROGRESS'); // ✅ FIXED: valid status
      }
      Alert.alert('Success', 'You accepted the job');
      navigation.goBack();
    } catch (err: unknown) {
      const apiError = err as ApiError;
      Alert.alert('Error', apiError?.response?.data?.message || 'Failed to accept job');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Accept Job #{job?.id.slice(0, 8)}</Text>

      <Text style={styles.label}>Issue:</Text>
      <Text style={styles.value}>{job?.issue}</Text>

      <Text style={styles.label}>Scheduled At:</Text>
      <Text style={styles.value}>
        {job?.scheduledAt ? new Date(job.scheduledAt).toLocaleString() : 'Not scheduled'}
      </Text>

      <TouchableOpacity style={styles.button} onPress={handleAccept}>
        <Text style={styles.buttonText}>Accept Job</Text>
      </TouchableOpacity>
    </View>
  );
};

export default AcceptJobScreen;

