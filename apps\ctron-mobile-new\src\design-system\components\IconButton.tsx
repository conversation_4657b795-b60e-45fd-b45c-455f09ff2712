// CTRON Home Design System - IconButton Component
// Standardized icon button with consistent styling

import React from 'react';
import { TouchableOpacity, ViewStyle } from '../../utils/platformUtils';
import { tokens } from '../tokens';

export interface IconButtonProps {
  icon: React.ReactNode;
  onPress: () => void;
  variant?: 'filled' | 'outlined' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'neutral';
  disabled?: boolean;
  style?: ViewStyle;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onPress,
  variant = 'ghost',
  size = 'md',
  color = 'primary',
  disabled = false,
  style,
}) => {
  const getSizeStyles = (): ViewStyle => {
    switch (size) {
      case 'sm':
        return {
          width: 32,
          height: 32,
          borderRadius: tokens.borderRadius.base,
        };
      
      case 'md':
        return {
          width: 40,
          height: 40,
          borderRadius: tokens.borderRadius.base,
        };
      
      case 'lg':
        return {
          width: 48,
          height: 48,
          borderRadius: tokens.borderRadius.lg,
        };
      
      default:
        return {
          width: 40,
          height: 40,
          borderRadius: tokens.borderRadius.base,
        };
    }
  };

  const getColorConfig = () => {
    switch (color) {
      case 'primary':
        return {
          main: tokens.colors.primary[500],
          light: tokens.colors.primary[50],
          border: tokens.colors.primary[200],
        };
      
      case 'secondary':
        return {
          main: tokens.colors.secondary[500],
          light: tokens.colors.secondary[50],
          border: tokens.colors.secondary[200],
        };
      
      case 'success':
        return {
          main: tokens.colors.success[500],
          light: tokens.colors.success[50],
          border: tokens.colors.success[200],
        };
      
      case 'warning':
        return {
          main: tokens.colors.warning[500],
          light: tokens.colors.warning[50],
          border: tokens.colors.warning[200],
        };
      
      case 'error':
        return {
          main: tokens.colors.error[500],
          light: tokens.colors.error[50],
          border: tokens.colors.error[200],
        };
      
      case 'neutral':
        return {
          main: tokens.colors.neutral[500],
          light: tokens.colors.neutral[50],
          border: tokens.colors.neutral[200],
        };
      
      default:
        return {
          main: tokens.colors.primary[500],
          light: tokens.colors.primary[50],
          border: tokens.colors.primary[200],
        };
    }
  };

  const getVariantStyles = (): ViewStyle => {
    const colorConfig = getColorConfig();
    const baseStyle: ViewStyle = {
      justifyContent: 'center',
      alignItems: 'center',
    };

    switch (variant) {
      case 'filled':
        return {
          ...baseStyle,
          backgroundColor: colorConfig.main,
          ...tokens.shadows.sm,
        };
      
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: colorConfig.border,
        };
      
      case 'ghost':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
        };
      
      default:
        return baseStyle;
    }
  };

  const buttonStyle: ViewStyle = {
    ...getSizeStyles(),
    ...getVariantStyles(),
    ...(disabled && { opacity: 0.5 }),
    ...style,
  };

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      {icon}
    </TouchableOpacity>
  );
};

// Preset icon button variants for common use cases
export const PrimaryIconButton: React.FC<Omit<IconButtonProps, 'color' | 'variant'>> = (props) => (
  <IconButton color="primary" variant="filled" {...props} />
);

export const SecondaryIconButton: React.FC<Omit<IconButtonProps, 'color' | 'variant'>> = (props) => (
  <IconButton color="secondary" variant="outlined" {...props} />
);

export const GhostIconButton: React.FC<Omit<IconButtonProps, 'variant'>> = (props) => (
  <IconButton variant="ghost" {...props} />
);