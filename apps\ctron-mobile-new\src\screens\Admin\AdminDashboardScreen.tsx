// CTRON Home - Admin Dashboard Screen
// Comprehensive admin interface for mobile platform

import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useMemo } from 'react';


import { AdminAPI } from '../../api/admin.api';
import type { DashboardMetrics as AdminDashboardMetrics, RecentActivity as AdminRecentActivity } from '../../api/admin.api';
import { Button, Card, Header } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import type { AdminStackParamList } from '../../navigation/AdminStack';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ActivityIndicator } from '../../utils/platformUtils';


// Using imported types from admin.api.ts

type AdminDashboardScreenNavigationProp = StackNavigationProp<AdminStackParamList, 'AdminDashboard'>;

export default function AdminDashboardScreen() {
  const navigation = useNavigation<AdminDashboardScreenNavigationProp>();
  const { user, logout } = useAuth();
  const { colors, spacing, typography, borderRadius } = useTheme();

  const [metrics, setMetrics] = useState<AdminDashboardMetrics>({
    totalRevenue: 0,
    totalJobs: 0,
    activeJobs: 0,
    completedJobs: 0,
    totalTechnicians: 0,
    activeTechnicians: 0,
    monthlyRevenue: 0,
    averageJobValue: 0,
    customerSatisfaction: 0,
  });
  const [recentActivity, setRecentActivity] = useState<AdminRecentActivity[]>([]);
  const [loading, setLoading] = useState(true);


  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load dashboard metrics and recent activity
      const [metricsData, activityData] = await Promise.all([
        AdminAPI.getDashboardMetrics(),
        AdminAPI.getRecentActivity(10)
      ]);

      setMetrics(metricsData);
      setRecentActivity(activityData.activities);

    } catch (error: any) {
      console.error('Failed to load dashboard data:', error);
      Alert.alert(
        'Error',
        error?.message || 'Failed to load dashboard data. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };




  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return colors.error.main || '#ef4444';
      case 'medium': return colors.warning.main || '#f59e0b';
      case 'low': return colors.success.main || '#10b981';
      default: return colors.text.secondary;
    }
  };

  const renderMetricCard = (title: string, value: string | number, icon: string, onPress?: () => void) => (
    <TouchableOpacity
      style={styles.metricCard}
      onPress={onPress}
      disabled={!onPress}
    >
      <Card style={styles.metricCardInner}>
        <Text style={styles.metricIcon}>{icon}</Text>
        <Text style={styles.metricValue}>{value}</Text>
        <Text style={styles.metricTitle}>{title}</Text>
      </Card>
    </TouchableOpacity>
  );

  const renderActivityItem = (item: AdminRecentActivity) => (
    <View key={item.id} style={styles.activityItem}>
      <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(item.priority) }]} />
      <View style={styles.activityContent}>
        <Text style={styles.activityMessage}>{item.message}</Text>
        <Text style={styles.activityTime}>{formatTime(item.timestamp)}</Text>
      </View>
    </View>
  );

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    scrollView: {
      flex: 1,
      padding: spacing.md,
    },
    welcomeSection: {
      marginBottom: spacing.lg,
      paddingHorizontal: spacing.sm,
    },
    welcomeText: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    adminName: {
      fontSize: typography.fontSize.xxl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    metricsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginBottom: spacing.lg,
    },
    metricCard: {
      width: '48%',
      marginBottom: spacing.md,
    },
    metricCardInner: {
      padding: spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 120,
    },
    metricIcon: {
      fontSize: 32,
      marginBottom: spacing.sm,
    },
    metricValue: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    metricTitle: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.normal,
      color: colors.text.secondary,
      textAlign: 'center',
    },
    sectionTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.md,
      paddingHorizontal: spacing.sm,
    },
    activityItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
      padding: spacing.md,
      borderRadius: borderRadius.md,
      marginBottom: spacing.sm,
      marginHorizontal: spacing.sm,
    },
    priorityIndicator: {
      width: 8,
      height: '100%',
      borderRadius: borderRadius.sm,
      marginRight: spacing.sm,
    },
    activityContent: {
      flex: 1,
    },
    activityMessage: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.normal,
      color: colors.text.primary,
    },
    activityTime: {
      ...typography.caption,
      color: colors.text.tertiary,
      marginTop: spacing.xs,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.normal,
      color: colors.text.secondary,
      marginTop: spacing.md,
    },
    logoutIcon: {
      fontSize: 24,
    },
    section: {
      padding: spacing.lg,
      paddingTop: spacing.sm,
    },
    quickActions: {
      gap: spacing.md,
    },
    actionButton: {
      marginBottom: spacing.sm,
    },
    activityCard: {
      padding: spacing.lg,
    },
    emptyText: {
      textAlign: 'center',
      color: colors.text.tertiary,
      fontStyle: 'italic',
    },
  }), [colors, spacing, typography, borderRadius]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary.main} />
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="Admin Dashboard"
        rightAction={{
          icon: <Text style={styles.logoutIcon}>👤</Text>,
          onPress: () => logout(true),
          accessibilityLabel: 'User menu',
        }}
      />

      <ScrollView
        style={styles.scrollView}
      // Pull to refresh functionality can be added here if needed
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>Welcome back,</Text>
          <Text style={styles.adminName}>{user?.fullName || 'Admin'} 👋</Text>
        </View>

        {/* Metrics Grid */}
        <View style={styles.metricsGrid}>
          {renderMetricCard(
            'Total Revenue',
            formatCurrency(metrics.totalRevenue),
            '💰',
            () => navigation.navigate('AdminJobs')
          )}
          {renderMetricCard(
            'Active Jobs',
            metrics.activeJobs.toString(),
            '📋',
            () => navigation.navigate('AdminJobs')
          )}
          {renderMetricCard(
            'Active Techs',
            metrics.activeTechnicians.toString(),
            '👷',
            () => navigation.navigate('AdminTechnicians')
          )}
          {renderMetricCard(
            'Total Jobs',
            metrics.totalJobs.toString(),
            '⏳'
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <Button
              title="Manage Jobs"
              onPress={() => navigation.navigate('AdminJobs')}
              variant="primary"
              size="lg"
              style={styles.actionButton}
            />
            <Button
              title="Approve Technicians"
              onPress={() => navigation.navigate('AdminTechnicians')}
              variant="secondary"
              size="lg"
              style={styles.actionButton}
            />
            <Button
              title="System Settings"
              onPress={() => navigation.navigate('AdminSettings')}
              variant="outline"
              size="lg"
              style={styles.actionButton}
            />
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <Card style={styles.activityCard}>
            {recentActivity.length > 0 ? (
              recentActivity.map(renderActivityItem)
            ) : (
              <Text style={styles.emptyText}>No recent activity</Text>
            )}
          </Card>
        </View>
      </ScrollView>
    </View>
  );
}

