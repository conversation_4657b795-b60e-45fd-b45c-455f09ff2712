// CTRON Home - Admin API Service
// API calls for admin functionality

import { API_BASE_URL } from '../config/api.config';
import { getAuthToken } from '../utils/auth.utils';

export interface DashboardMetrics {
  totalJobs: number;
  activeJobs: number;
  completedJobs: number;
  totalTechnicians: number;
  activeTechnicians: number;
  totalRevenue: number;
  monthlyRevenue: number;
  averageJobValue: number;
  customerSatisfaction: number;
}

export interface RecentActivity {
  id: string;
  type: 'job_created' | 'job_completed' | 'payment_completed' | 'technician_approved' | 'user_registered';
  message: string;
  timestamp: string;
  priority: 'low' | 'medium' | 'high';
  userId?: string;
  jobId?: string;
  technicianId?: string;
}

export interface SystemSettings {
  gracePeriodHours: number;
  stripeTestMode: boolean;
  statusMessage: string;
  maintenanceMode: boolean;
  maxJobsPerTechnician: number;
  commissionRate: number;
  autoAssignJobs: boolean;
  notificationsEnabled: boolean;
}

// Helper function to make authenticated API calls
const makeAuthenticatedRequest = async (method: string, endpoint: string, data?: unknown) => {
  const token = await getAuthToken();

  if (!token) {
    throw new Error('Authentication token not found. Please log in again.');
  }

  const config: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  };

  if (data) {
    config.body = JSON.stringify(data);
  }

  if (__DEV__) {
    console.log(`🔗 Admin API Request: ${method} ${endpoint}`);
    console.log('📦 Request data:', data);
  }

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

    if (__DEV__) {
      console.log(`✅ Admin API Response: ${method} ${endpoint} - ${response.status}`);
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  } catch (error: unknown) {
    if (__DEV__) {
      console.error(`❌ Admin API Error: ${method} ${endpoint}`);
      console.error('Error:', error);
    }

    throw error;
  }
};

export const AdminAPI = {
  /**
   * Get dashboard metrics
   */
  getDashboardMetrics: async (): Promise<DashboardMetrics> => {
    const response = await makeAuthenticatedRequest('GET', '/api/admin/dashboard/metrics');
    return response.metrics;
  },

  /**
   * Get recent activity
   */
  getRecentActivity: async (limit = 10): Promise<{ activities: RecentActivity[] }> => {
    return makeAuthenticatedRequest('GET', `/api/admin/recent-activity?limit=${limit}`);
  },

  /**
   * Get system settings
   */
  getSystemSettings: async (): Promise<SystemSettings> => {
    const response = await makeAuthenticatedRequest('GET', '/api/admin/settings');
    return response.settings;
  },

  /**
   * Update system settings
   */
  updateSystemSettings: async (settings: Partial<SystemSettings>): Promise<{ success: boolean }> => {
    return makeAuthenticatedRequest('PUT', '/api/admin/settings', settings);
  },

  /**
   * Get all users with pagination
   */
  getUsers: async (page = 1, limit = 20, role?: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN'): Promise<{
    users: Array<{
      id: string;
      fullName: string;
      email: string;
      role: string;
      isActive: boolean;
      createdAt: string;
      lastLoginAt?: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> => {
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    if (role) queryParams.append('role', role);

    return makeAuthenticatedRequest('GET', `/api/admin/users?${queryParams.toString()}`);
  },

  /**
   * Get all technicians with approval status
   */
  getTechnicians: async (page = 1, limit = 20, status?: 'PENDING' | 'APPROVED' | 'REJECTED'): Promise<{
    technicians: Array<{
      id: string;
      user: {
        fullName: string;
        email: string;
        phone?: string;
      };
      specialization: string;
      experience: number;
      status: string;
      rating?: number;
      completedJobs: number;
      createdAt: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> => {
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    if (status) queryParams.append('status', status);

    return makeAuthenticatedRequest('GET', `/api/admin/technicians?${queryParams.toString()}`);
  },

  /**
   * Approve or reject technician
   */
  updateTechnicianStatus: async (technicianId: string, status: 'APPROVED' | 'REJECTED', reason?: string): Promise<{ success: boolean }> => {
    return makeAuthenticatedRequest('PUT', `/api/admin/technicians/${technicianId}/status`, {
      status,
      reason,
    });
  },

  /**
   * Get all jobs with filters
   */
  getJobs: async (params: {
    page?: number;
    limit?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
    technicianId?: string;
    homeownerId?: string;
  } = {}): Promise<{
    jobs: Array<{
      id: string;
      issue: string;
      status: string;
      scheduledAt: string;
      createdAt: string;
      homeowner: {
        fullName: string;
        email: string;
      };
      technician?: {
        user: {
          fullName: string;
        };
        specialization: string;
      };
      payment?: {
        amount: number;
        status: string;
      };
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return makeAuthenticatedRequest('GET', `/api/admin/jobs?${queryParams.toString()}`);
  },

  /**
   * Get job details for admin
   */
  getJobDetails: async (jobId: string): Promise<{
    job: {
      id: string;
      issue: string;
      description?: string;
      status: string;
      scheduledAt: string;
      createdAt: string;
      updatedAt: string;
      homeowner: {
        id: string;
        fullName: string;
        email: string;
        phone?: string;
      };
      technician?: {
        id: string;
        user: {
          fullName: string;
          email: string;
          phone?: string;
        };
        specialization: string;
        rating?: number;
      };
      payment?: {
        id: string;
        amount: number;
        status: string;
        stripePaymentIntentId?: string;
        createdAt: string;
      };
      chat?: {
        id: string;
        messageCount: number;
        lastMessageAt?: string;
      };
    };
  }> => {
    return makeAuthenticatedRequest('GET', `/api/admin/jobs/${jobId}`);
  },

  /**
   * Update job status (admin override)
   */
  updateJobStatus: async (jobId: string, status: string, reason?: string): Promise<{ success: boolean }> => {
    return makeAuthenticatedRequest('PUT', `/api/admin/jobs/${jobId}/status`, {
      status,
      reason,
    });
  },

  /**
   * Get revenue analytics
   */
  getRevenueAnalytics: async (period: 'week' | 'month' | 'quarter' | 'year' = 'month'): Promise<{
    totalRevenue: number;
    periodRevenue: number;
    revenueGrowth: number;
    averageJobValue: number;
    topTechnicians: Array<{
      technicianId: string;
      name: string;
      revenue: number;
      jobCount: number;
    }>;
    revenueByDay: Array<{
      date: string;
      revenue: number;
      jobCount: number;
    }>;
  }> => {
    return makeAuthenticatedRequest('GET', `/api/admin/analytics/revenue?period=${period}`);
  },

  /**
   * Get user analytics
   */
  getUserAnalytics: async (): Promise<{
    totalUsers: number;
    newUsersThisMonth: number;
    activeUsers: number;
    userGrowthRate: number;
    usersByRole: {
      homeowners: number;
      technicians: number;
      admins: number;
    };
    registrationsByDay: Array<{
      date: string;
      count: number;
    }>;
  }> => {
    return makeAuthenticatedRequest('GET', '/api/admin/analytics/users');
  },

  /**
   * Export data to CSV
   */
  exportData: async (type: 'jobs' | 'users' | 'technicians' | 'payments', filters?: Record<string, any>): Promise<{ downloadUrl: string }> => {
    const queryParams = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    return makeAuthenticatedRequest('POST', `/api/admin/export/${type}?${queryParams.toString()}`);
  },
};