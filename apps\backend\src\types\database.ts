// backend/src/types/database.ts
import { Prisma } from '@prisma/client';

// ===== PRISMA INCLUDE TYPES =====
export type UserWithTechnician = Prisma.UserGetPayload<{
  include: { technician: true };
}>;

export type UserWithRelations = Prisma.UserGetPayload<{
  include: {
    technician: true;
    jobs: true;
    reviews: true;
    payments: true;
    notifications: true;
  };
}>;

export type JobWithFullRelations = Prisma.JobGetPayload<{
  include: {
    user: {
      select: {
        id: true;
        fullName: true;
        email: true;
        phone: true;
        role: true;
      };
    };
    technician: {
      include: {
        user: {
          select: {
            id: true;
            fullName: true;
            email: true;
            phone: true;
            role: true;
          };
        };
      };
    };
    payment: true;
    review: true;
    chat: {
      include: {
        messages: {
          include: {
            sender: {
              select: {
                id: true;
                fullName: true;
                email: true;
                role: true;
              };
            };
          };
        };
      };
    };
  };
}>;

export type TechnicianWithUser = Prisma.TechnicianGetPayload<{
  include: {
    user: {
      select: {
        id: true;
        fullName: true;
        email: true;
        phone: true;
        role: true;
        createdAt: true;
        lastLoginAt: true;
      };
    };
  };
}>;

export type TechnicianWithStats = Prisma.TechnicianGetPayload<{
  include: {
    user: true;
    jobs: {
      include: {
        review: true;
        payment: true;
      };
    };
    reviews: true;
  };
}>;

export type PaymentWithJobAndUser = Prisma.PaymentGetPayload<{
  include: {
    job: {
      include: {
        user: true;
        technician: {
          include: {
            user: true;
          };
        };
      };
    };
    user: true;
  };
}>;

export type ReviewWithRelations = Prisma.ReviewGetPayload<{
  include: {
    user: {
      select: {
        id: true;
        fullName: true;
        email: true;
        role: true;
      };
    };
    job: {
      select: {
        id: true;
        issue: true;
        status: true;
        createdAt: true;
        completedAt: true;
      };
    };
    technician: {
      include: {
        user: {
          select: {
            id: true;
            fullName: true;
            email: true;
          };
        };
      };
    };
  };
}>;

export type ChatWithFullRelations = Prisma.ChatGetPayload<{
  include: {
    job: {
      include: {
        user: true;
        technician: {
          include: {
            user: true;
          };
        };
      };
    };
    participants: {
      include: {
        user: {
          select: {
            id: true;
            fullName: true;
            email: true;
            role: true;
          };
        };
      };
    };
    messages: {
      include: {
        sender: {
          select: {
            id: true;
            fullName: true;
            email: true;
            role: true;
          };
        };
      };
      orderBy: {
        createdAt: 'desc';
      };
      take: 50;
    };
  };
}>;

export type NotificationWithUser = Prisma.NotificationGetPayload<{
  include: {
    user: {
      select: {
        id: true;
        fullName: true;
        email: true;
        role: true;
      };
    };
  };
}>;

// ===== DATABASE OPERATION TYPES =====
export interface DatabaseTransaction {
  user: Prisma.UserDelegate;
  job: Prisma.JobDelegate;
  technician: Prisma.TechnicianDelegate;
  payment: Prisma.PaymentDelegate;
  review: Prisma.ReviewDelegate;
  chat: Prisma.ChatDelegate;
  message: Prisma.MessageDelegate;
  notification: Prisma.NotificationDelegate;
  refreshToken: Prisma.RefreshTokenDelegate;
  pushToken: Prisma.PushTokenDelegate;
  setting: Prisma.SettingDelegate;
}

// ===== QUERY BUILDER TYPES =====
export interface BaseQueryOptions {
  skip?: number;
  take?: number;
  orderBy?: Record<string, 'asc' | 'desc'>;
}

export interface UserQueryOptions extends BaseQueryOptions {
  where?: Prisma.UserWhereInput;
  include?: Prisma.UserInclude;
  select?: Prisma.UserSelect;
}

export interface JobQueryOptions extends BaseQueryOptions {
  where?: Prisma.JobWhereInput;
  include?: Prisma.JobInclude;
  select?: Prisma.JobSelect;
}

export interface TechnicianQueryOptions extends BaseQueryOptions {
  where?: Prisma.TechnicianWhereInput;
  include?: Prisma.TechnicianInclude;
  select?: Prisma.TechnicianSelect;
}

export interface PaymentQueryOptions extends BaseQueryOptions {
  where?: Prisma.PaymentWhereInput;
  include?: Prisma.PaymentInclude;
  select?: Prisma.PaymentSelect;
}

export interface ReviewQueryOptions extends BaseQueryOptions {
  where?: Prisma.ReviewWhereInput;
  include?: Prisma.ReviewInclude;
  select?: Prisma.ReviewSelect;
}

export interface ChatQueryOptions extends BaseQueryOptions {
  where?: Prisma.ChatWhereInput;
  include?: Prisma.ChatInclude;
  select?: Prisma.ChatSelect;
}

export interface MessageQueryOptions extends BaseQueryOptions {
  where?: Prisma.MessageWhereInput;
  include?: Prisma.MessageInclude;
  select?: Prisma.MessageSelect;
}

export interface NotificationQueryOptions extends BaseQueryOptions {
  where?: Prisma.NotificationWhereInput;
  include?: Prisma.NotificationInclude;
  select?: Prisma.NotificationSelect;
}

// ===== AGGREGATION TYPES =====
export interface JobAggregation {
  _count: {
    id: number;
    status?: Record<string, number>;
    priority?: Record<string, number>;
  };
  _avg: {
    amount?: number;
  };
  _sum: {
    amount?: number;
  };
  _min: {
    createdAt?: Date;
    scheduledAt?: Date;
  };
  _max: {
    createdAt?: Date;
    completedAt?: Date;
  };
}

export interface PaymentAggregation {
  _count: {
    id: number;
  };
  _sum: {
    amount: number;
  };
  _avg: {
    amount: number;
  };
  _min: {
    amount: number;
    createdAt: Date;
  };
  _max: {
    amount: number;
    createdAt: Date;
  };
}

export interface ReviewAggregation {
  _count: {
    id: number;
  };
  _avg: {
    rating: number;
  };
  _min: {
    rating: number;
    createdAt: Date;
  };
  _max: {
    rating: number;
    createdAt: Date;
  };
}

// ===== SEARCH AND FILTER TYPES =====
export interface SearchFilters {
  searchTerm?: string;
  dateRange?: {
    from: Date;
    to: Date;
  };
  status?: string[];
  role?: string[];
  location?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface PaginationOptions {
  page: number;
  limit: number;
  offset: number;
}

// ===== BULK OPERATION TYPES =====
export interface BulkCreateResult<T> {
  count: number;
  data: T[];
  errors: Array<{
    index: number;
    error: string;
  }>;
}

export interface BulkUpdateResult {
  count: number;
  updated: string[];
  errors: Array<{
    id: string;
    error: string;
  }>;
}

export interface BulkDeleteResult {
  count: number;
  deleted: string[];
  errors: Array<{
    id: string;
    error: string;
  }>;
}

// ===== VALIDATION TYPES =====
export interface ValidationRule {
  field: string;
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'date' | 'email' | 'url';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

// ===== AUDIT LOG TYPES =====
export interface AuditLogEntry {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

export interface AuditLogOptions {
  includeOldValues?: boolean;
  includeNewValues?: boolean;
  includeMetadata?: boolean;
  excludeFields?: string[];
}

// ===== CACHE TYPES =====
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  key?: string;
  tags?: string[];
  invalidateOn?: string[];
}

export interface CacheResult<T> {
  data: T;
  cached: boolean;
  cacheKey: string;
  expiresAt?: Date;
}

// ===== MIGRATION TYPES =====
export interface MigrationInfo {
  id: string;
  name: string;
  appliedAt: Date;
  checksum: string;
}

export interface MigrationResult {
  success: boolean;
  appliedMigrations: string[];
  errors: string[];
  warnings: string[];
}