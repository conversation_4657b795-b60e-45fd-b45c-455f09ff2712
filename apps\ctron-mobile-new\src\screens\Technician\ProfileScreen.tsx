// src/screens/Technician/ProfileScreen.tsx
import axios from 'axios';
import React, { useState, useMemo } from 'react';

import { API_BASE_URL } from '../../config/api.config';
import { useAuth } from '../../context/AuthContext';
import { getAuthToken } from '../../utils/auth.utils';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Platform, ActivityIndicator } from '../../utils/platformUtils';
import { KeyboardAvoidingView } from '../../utils/platformUtils';

export default function ProfileScreen() {
  const { user, logout } = useAuth();

  const [fullName, setFullName] = useState(user?.fullName || '');
  const [email, setEmail] = useState(user?.email || '');

  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f9f9f9',
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
    },
    saveButton: {
      backgroundColor: '#28a745',
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
    },
    saveText: {
      color: 'white',
      fontWeight: 'bold',
    },
    editButton: {
      backgroundColor: '#007AFF',
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
    },
    editText: {
      color: 'white',
      fontWeight: 'bold',
    },
    fieldContainer: {
      marginBottom: 16,
    },
    label: {
      color: '#555',
      marginBottom: 4,
      fontWeight: 'bold',
    },
    input: {
      backgroundColor: 'white',
      borderColor: '#ddd',
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
    },
    disabledInput: {
      backgroundColor: '#eee',
    },
    logoutButton: {
      marginTop: 40,
      backgroundColor: '#FF3B30',
      padding: 14,
      borderRadius: 12,
      alignItems: 'center',
    },
    logoutText: {
      color: 'white',
      fontWeight: 'bold',
      fontSize: 16,
    },
  }), []);

  const handleSave = async () => {
    if (!fullName.trim() || !email.trim()) {
      return alert('Name and email cannot be empty!');
    }
    setLoading(true);
    try {
      const token = await getAuthToken();
      await axios.put(`${API_BASE_URL}/api/users/me`,
        { fullName, email },
        {
          headers: {
            'Content-Type': 'application/json',
            ...(token && { Authorization: `Bearer ${token}` }),
          },
        }
      );
      alert('✅ Profile updated successfully!');
      setIsEditing(false);
    } catch (error) {
      // Log error in development only
      if (__DEV__) {
        console.error('Profile update error:', error);
      }
      alert('❌ Failed to update profile. Try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={styles.container}
    >
      <View style={styles.header}>
        <Text style={styles.title}>My Profile</Text>
        {isEditing ? (
          <TouchableOpacity style={styles.saveButton} onPress={handleSave} disabled={loading}>
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.saveText}>Save</Text>
            )}
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.editButton} onPress={() => setIsEditing(true)}>
            <Text style={styles.editText}>Edit</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Profile Fields */}
      <View style={styles.fieldContainer}>
        <Text style={styles.label}>Full Name</Text>
        <TextInput
          style={[styles.input, !isEditing && styles.disabledInput]}
          value={fullName}
          editable={isEditing}
          onChangeText={setFullName}
        />
      </View>

      <View style={styles.fieldContainer}>
        <Text style={styles.label}>Email</Text>
        <TextInput
          style={[styles.input, !isEditing && styles.disabledInput]}
          value={email}
          editable={isEditing}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />
      </View>

      {/* Logout Button */}
      <TouchableOpacity style={styles.logoutButton} onPress={() => logout()}>
        <Text style={styles.logoutText}>Logout</Text>
      </TouchableOpacity>
    </KeyboardAvoidingView>
  );
}


