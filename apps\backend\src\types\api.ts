// backend/src/types/api.ts
import { User, Job, Technician, Payment, Review, Chat, Message, Notification, JobStatus, Role } from '@prisma/client';

// ===== USER TYPES =====
export interface UserProfile {
  id: string;
  fullName: string;
  email: string;
  phone: string | null;
  role: Role;
  createdAt: Date;
  lastLoginAt: Date | null;
}

export interface UserWithTechnician extends User {
  technician?: Technician | null;
}

// ===== JOB TYPES =====
export interface JobWithRelations extends Job {
  user: UserProfile;
  technician?: (Technician & { user: UserProfile }) | null;
  payment?: Payment | null;
  review?: Review | null;
  chat?: Chat | null;
}

export interface CreateJobRequest {
  issue: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
  latitude?: number;
  longitude?: number;
  photoUrl?: string;
  scheduledAt: string; // ISO date string
  technicianId?: string;
}

export interface UpdateJobRequest {
  issue?: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
  scheduledAt?: string; // ISO date string
}

export interface UpdateJobStatusRequest {
  status: JobStatus;
  photoUrl?: string;
  proofImageKey?: string;
  cancellationReason?: string;
}

// ===== TECHNICIAN TYPES =====
export interface TechnicianProfile extends Technician {
  user: UserProfile;
}

export interface TechnicianWithStats extends TechnicianProfile {
  totalJobs: number;
  completedJobs: number;
  averageRating: number;
  reviewCount: number;
}

// ===== PAYMENT TYPES =====
export interface PaymentWithJob extends Payment {
  job: JobWithRelations;
}

export interface CreatePaymentIntentRequest {
  jobId: string;
  amount: number;
  currency?: string;
}

export interface PaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
}

// ===== AUTHENTICATION TYPES =====
export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  fullName: string;
  email: string;
  phone: string;
  password: string;
  role: Role;
}

export interface AuthResponse {
  success: true;
  message: string;
  accessToken: string;
  refreshToken: string;
  user: UserProfile;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  success: true;
  accessToken: string;
  refreshToken: string;
}

// ===== REVIEW TYPES =====
export interface CreateReviewRequest {
  jobId: string;
  rating: number; // 1-5
  comment?: string;
}

export interface ReviewWithRelations extends Review {
  user: UserProfile;
  job: Job;
  technician?: TechnicianProfile | null;
}

// ===== CHAT TYPES =====
export interface ChatWithMessages extends Chat {
  messages: (Message & { sender: UserProfile })[];
  participants: Array<{
    user: UserProfile;
    role: Role;
    joinedAt: Date;
  }>;
}

export interface SendMessageRequest {
  content: string;
  attachments?: string;
}

// ===== NOTIFICATION TYPES =====
export interface NotificationWithUser extends Notification {
  user: UserProfile;
}

export interface CreateNotificationRequest {
  userId: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  type?: 'PUSH' | 'EMAIL' | 'SMS';
}

// ===== DASHBOARD TYPES =====
export interface DashboardStats {
  totalJobs: number;
  pendingJobs: number;
  completedJobs: number;
  totalRevenue: number;
  averageRating: number;
  activeChats: number;
}

export interface TechnicianDashboardStats extends DashboardStats {
  availabilityStatus: boolean;
  upcomingJobs: JobWithRelations[];
  recentReviews: ReviewWithRelations[];
}

export interface HomeownerDashboardStats extends DashboardStats {
  activeJobs: JobWithRelations[];
  recentJobs: JobWithRelations[];
  favoriteTehnicians: TechnicianProfile[];
}

// ===== SEARCH AND FILTER TYPES =====
export interface JobFilters {
  status?: JobStatus[];
  priority?: ('low' | 'medium' | 'high')[];
  dateFrom?: string;
  dateTo?: string;
  technicianId?: string;
  userId?: string;
}

export interface TechnicianFilters {
  specialization?: string[];
  isAvailable?: boolean;
  minRating?: number;
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // in kilometers
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ===== ERROR TYPES =====
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ApiError {
  success: false;
  message: string;
  error?: {
    code: string;
    details?: string;
    field?: string;
    stack?: string;
  };
  errors?: ValidationError[];
  meta?: {
    timestamp: string;
    requestId?: string;
  };
}

// ===== WEBHOOK TYPES =====
export interface StripeWebhookEvent {
  id: string;
  type: string;
  data: {
    object: any;
  };
  created: number;
}

// ===== FILE UPLOAD TYPES =====
export interface FileUploadResponse {
  success: true;
  url: string;
  key: string;
  filename: string;
  size: number;
  mimetype: string;
}

export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

// ===== LOCATION TYPES =====
export interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

export interface AddressInfo extends LocationCoordinates {
  address?: string;
  city?: string;
  postcode?: string;
  country?: string;
}

// ===== SETTINGS TYPES =====
export interface SystemSettings {
  gracePeriodHours: number;
  stripeTestMode: boolean;
  statusMessage: string;
}

export interface UpdateSettingsRequest {
  gracePeriodHours?: number;
  stripeTestMode?: boolean;
  statusMessage?: string;
}