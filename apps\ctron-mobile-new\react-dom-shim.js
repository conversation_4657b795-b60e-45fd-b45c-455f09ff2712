// React DOM shim for React Native
// This file provides a minimal react-dom implementation for React Native
// to handle dependencies that expect react-dom to be available

// Mock react-dom for React Native environment
const ReactDOM = {
  // Minimal implementation for compatibility
  render: () => {
    console.warn('ReactDOM.render is not available in React Native');
  },
  
  unmountComponentAtNode: () => {
    console.warn('ReactDOM.unmountComponentAtNode is not available in React Native');
  },
  
  findDOMNode: () => {
    console.warn('ReactDOM.findDOMNode is not available in React Native');
    return null;
  },
  
  createPortal: () => {
    console.warn('ReactDOM.createPortal is not available in React Native');
    return null;
  },
  
  // React 18 concurrent features
  createRoot: () => {
    console.warn('ReactDOM.createRoot is not available in React Native');
    return {
      render: () => {},
      unmount: () => {}
    };
  },
  
  hydrateRoot: () => {
    console.warn('ReactDOM.hydrateRoot is not available in React Native');
    return {
      render: () => {},
      unmount: () => {}
    };
  }
};

// Export for different import styles
module.exports = ReactDOM;
module.exports.default = ReactDOM;

// Handle named exports
Object.keys(ReactDOM).forEach(key => {
  module.exports[key] = ReactDOM[key];
});