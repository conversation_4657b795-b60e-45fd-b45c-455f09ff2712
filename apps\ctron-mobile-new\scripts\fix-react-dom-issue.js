#!/usr/bin/env node

/**
 * CTRON Mobile - React DOM Issue Fixer
 * 
 * This script specifically fixes the react-dom bundling issue with NativeBase
 * Run with: node scripts/fix-react-dom-issue.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 CTRON React DOM Issue Fixer');
console.log('===============================');

// Step 1: Ensure react-dom is properly installed
console.log('\n📦 Step 1: Checking react-dom installation...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  if (!packageJson.dependencies['react-dom']) {
    console.log('❌ react-dom not found in dependencies');
    console.log('📦 Installing react-dom...');
    execSync('yarn add react-dom@18.3.1', { stdio: 'inherit' });
    console.log('✅ react-dom installed');
  } else {
    console.log('✅ react-dom found in dependencies');
  }
  
  // Check resolutions
  if (!packageJson.resolutions || !packageJson.resolutions['react-dom']) {
    console.log('📝 Adding react-dom to resolutions...');
    const updatedPackageJson = {
      ...packageJson,
      resolutions: {
        ...packageJson.resolutions,
        'react': '18.3.1',
        'react-dom': '18.3.1'
      }
    };
    fs.writeFileSync('package.json', JSON.stringify(updatedPackageJson, null, 2));
    console.log('✅ react-dom added to resolutions');
  }
} catch (error) {
  console.error('❌ Error checking package.json:', error.message);
}

// Step 2: Clear all caches
console.log('\n🧹 Step 2: Clearing caches...');
try {
  // Clear yarn cache
  execSync('yarn cache clean', { stdio: 'inherit' });
  console.log('✅ Yarn cache cleared');
  
  // Clear Metro cache
  if (fs.existsSync('node_modules/.cache')) {
    execSync('rm -rf node_modules/.cache', { stdio: 'inherit' });
    console.log('✅ Metro cache cleared');
  }
  
  // Clear Expo cache
  try {
    execSync('npx expo start --clear --no-dev', { stdio: 'pipe', timeout: 3000 });
  } catch (error) {
    // Expected to timeout, just clearing cache
  }
  console.log('✅ Expo cache cleared');
  
} catch (error) {
  console.log('⚠️  Some cache clearing failed, but continuing...');
}

// Step 3: Reinstall dependencies
console.log('\n📦 Step 3: Reinstalling dependencies...');
try {
  console.log('🗑️  Removing node_modules...');
  execSync('rm -rf node_modules', { stdio: 'inherit' });
  
  console.log('📦 Installing dependencies...');
  execSync('yarn install', { stdio: 'inherit' });
  console.log('✅ Dependencies reinstalled');
} catch (error) {
  console.error('❌ Error reinstalling dependencies:', error.message);
}

// Step 4: Verify Metro configuration
console.log('\n🔍 Step 4: Verifying Metro configuration...');
const metroConfigPath = 'metro.config.js';
if (fs.existsSync(metroConfigPath)) {
  const metroConfig = fs.readFileSync(metroConfigPath, 'utf8');
  
  if (metroConfig.includes('react-dom') && metroConfig.includes('node_modules/react-dom')) {
    console.log('✅ Metro config includes react-dom alias');
  } else {
    console.log('❌ Metro config missing react-dom alias');
  }
  
  if (metroConfig.includes('native-base') && metroConfig.includes('@react-aria/utils')) {
    console.log('✅ Metro config includes NativeBase transpile packages');
  } else {
    console.log('❌ Metro config missing NativeBase transpile packages');
  }
} else {
  console.log('❌ Metro config not found');
}

// Step 5: Test the fix
console.log('\n🧪 Step 5: Testing the fix...');
try {
  console.log('🔍 Checking if react-dom can be resolved...');
  
  // Create a test file to check if react-dom resolves
  const testCode = `
    try {
      require('react-dom');
      console.log('✅ react-dom resolves correctly');
    } catch (error) {
      console.log('❌ react-dom resolution failed:', error.message);
    }
  `;
  
  fs.writeFileSync('temp-test.js', testCode);
  execSync('node temp-test.js', { stdio: 'inherit' });
  fs.unlinkSync('temp-test.js');
  
} catch (error) {
  console.log('⚠️  Test failed, but this might be normal in React Native environment');
}

// Step 6: Final recommendations
console.log('\n📋 Final Steps:');
console.log('===============');
console.log('1. ✅ Dependencies checked and installed');
console.log('2. ✅ Caches cleared');
console.log('3. ✅ Metro configuration verified');
console.log('');
console.log('🚀 Now try running:');
console.log('   yarn start');
console.log('');
console.log('🔧 If the issue persists:');
console.log('   1. Restart your terminal');
console.log('   2. Try: yarn start --clear');
console.log('   3. Try: yarn start --reset-cache');
console.log('   4. Check that no other Metro processes are running');
console.log('');
console.log('📞 If you still have issues:');
console.log('   - Check the NATIVEBASE_TROUBLESHOOTING_GUIDE.md');
console.log('   - Ensure you\'re using Node.js 16+ and Yarn 1.22+');
console.log('   - Try running on a different device/simulator');

console.log('\n🎉 React DOM issue fixer completed!');