// Crypto polyfill for React Native
// This provides a minimal crypto implementation for Node.js modules

const crypto = {
  // Random bytes generation
  randomBytes: (size) => {
    const bytes = new Uint8Array(size);
    
    // Use React Native's crypto if available, otherwise use Math.random
    if (typeof global !== 'undefined' && global.crypto && global.crypto.getRandomValues) {
      global.crypto.getRandomValues(bytes);
    } else {
      for (let i = 0; i < size; i++) {
        bytes[i] = Math.floor(Math.random() * 256);
      }
    }
    
    return Buffer.from ? Buffer.from(bytes) : bytes;
  },
  
  // Hash functions (basic implementations)
  createHash: (algorithm) => {
    return {
      update: function(data) {
        this._data = (this._data || '') + data;
        return this;
      },
      digest: function(encoding) {
        // Simple hash implementation (not cryptographically secure)
        let hash = 0;
        const str = this._data || '';
        
        for (let i = 0; i < str.length; i++) {
          const char = str.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32-bit integer
        }
        
        const result = Math.abs(hash).toString(16);
        
        if (encoding === 'hex') {
          return result.padStart(8, '0');
        }
        
        return result;
      }
    };
  },
  
  // HMAC (basic implementation)
  createHmac: (algorithm, key) => {
    return {
      update: function(data) {
        this._data = (this._data || '') + data;
        this._key = key;
        return this;
      },
      digest: function(encoding) {
        // Simple HMAC implementation (not cryptographically secure)
        const data = this._data || '';
        const keyStr = String(this._key || '');
        
        let hash = 0;
        const combined = keyStr + data;
        
        for (let i = 0; i < combined.length; i++) {
          const char = combined.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash;
        }
        
        const result = Math.abs(hash).toString(16);
        
        if (encoding === 'hex') {
          return result.padStart(8, '0');
        }
        
        return result;
      }
    };
  },
  
  // Constants
  constants: {
    SSL_OP_NO_SSLv2: 0x01000000,
    SSL_OP_NO_SSLv3: 0x02000000,
    SSL_OP_NO_TLSv1: 0x04000000,
    SSL_OP_NO_TLSv1_1: 0x10000000,
    SSL_OP_NO_TLSv1_2: 0x08000000
  }
};

// Add Buffer polyfill if not available
if (typeof Buffer === 'undefined') {
  global.Buffer = {
    from: (data) => {
      if (typeof data === 'string') {
        return new TextEncoder().encode(data);
      }
      return data;
    },
    alloc: (size) => new Uint8Array(size),
    isBuffer: (obj) => obj instanceof Uint8Array
  };
}

module.exports = crypto;