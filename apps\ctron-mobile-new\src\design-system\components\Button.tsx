// CTRON Home Design System - Button Component
// Standardized button with consistent variants and styling

import React from 'react';

import { TouchableOpacity, Text, ViewStyle, TextStyle, ActivityIndicator } from '../../utils/platformUtils';
import { tokens } from '../tokens';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
}) => {
  const getVariantStyles = (): { container: ViewStyle; text: TextStyle } => {
    const baseContainer: ViewStyle = {
      borderRadius: tokens.borderRadius.base,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: iconPosition === 'right' ? 'row-reverse' : 'row',
    };

    const baseText: TextStyle = {
      fontFamily: tokens.typography.fontFamily.primary,
      fontWeight: tokens.typography.fontWeight.medium,
    };

    switch (variant) {
      case 'primary':
        return {
          container: {
            ...baseContainer,
            backgroundColor: tokens.colors.primary[500],
            ...tokens.shadows.sm,
          },
          text: {
            ...baseText,
            color: tokens.colors.neutral[0],
          },
        };
      
      case 'secondary':
        return {
          container: {
            ...baseContainer,
            backgroundColor: tokens.colors.secondary[500],
            ...tokens.shadows.sm,
          },
          text: {
            ...baseText,
            color: tokens.colors.neutral[0],
          },
        };
      
      case 'outline':
        return {
          container: {
            ...baseContainer,
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderColor: tokens.colors.primary[500],
          },
          text: {
            ...baseText,
            color: tokens.colors.primary[500],
          },
        };
      
      case 'ghost':
        return {
          container: {
            ...baseContainer,
            backgroundColor: 'transparent',
          },
          text: {
            ...baseText,
            color: tokens.colors.primary[500],
          },
        };
      
      case 'danger':
        return {
          container: {
            ...baseContainer,
            backgroundColor: tokens.colors.error[500],
            ...tokens.shadows.sm,
          },
          text: {
            ...baseText,
            color: tokens.colors.neutral[0],
          },
        };
      
      default:
        return {
          container: baseContainer,
          text: baseText,
        };
    }
  };

  const getSizeStyles = (): { container: ViewStyle; text: TextStyle } => {
    switch (size) {
      case 'sm':
        return {
          container: {
            paddingHorizontal: tokens.spacing[3],
            paddingVertical: tokens.spacing[2],
            minHeight: 32,
          },
          text: {
            fontSize: tokens.typography.fontSize.sm,
          },
        };
      
      case 'md':
        return {
          container: {
            paddingHorizontal: tokens.spacing[4],
            paddingVertical: tokens.spacing[3],
            minHeight: 40,
          },
          text: {
            fontSize: tokens.typography.fontSize.base,
          },
        };
      
      case 'lg':
        return {
          container: {
            paddingHorizontal: tokens.spacing[6],
            paddingVertical: tokens.spacing[4],
            minHeight: 48,
          },
          text: {
            fontSize: tokens.typography.fontSize.lg,
          },
        };
      
      default:
        return {
          container: {},
          text: {},
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();

  const containerStyle: ViewStyle = {
    ...variantStyles.container,
    ...sizeStyles.container,
    ...(fullWidth && { width: '100%' }),
    ...(disabled && { opacity: 0.5 }),
    ...style,
  };

  const textStyleCombined: TextStyle = {
    ...variantStyles.text,
    ...sizeStyles.text,
    ...textStyle,
  };

  const iconSpacing = tokens.spacing[2];

  return (
    <TouchableOpacity
      style={containerStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator 
          color={(variantStyles.text as any).color} 
          size={size === 'sm' ? 'small' : 'small'}
        />
      ) : (
        <>
          {icon && iconPosition === 'left' && (
            <React.Fragment>
              {icon}
              <Text style={{ width: iconSpacing }} />
            </React.Fragment>
          )}
          
          <Text style={textStyleCombined}>{title}</Text>
          
          {icon && iconPosition === 'right' && (
            <React.Fragment>
              <Text style={{ width: iconSpacing }} />
              {icon}
            </React.Fragment>
          )}
        </>
      )}
    </TouchableOpacity>
  );
};