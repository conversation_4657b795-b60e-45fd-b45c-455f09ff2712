# CTRON Mobile App - Quick Start Guide

## 🚀 Get Started in 5 Minutes

This guide will get your CTRON mobile app with NativeBase running quickly, resolving any bundling issues.

## 📋 Prerequisites

- **Node.js 16+** installed
- **Yarn 1.22+** installed  
- **Expo CLI** installed globally: `npm install -g @expo/cli`
- **iOS Simulator** or **Android Emulator** running

## ⚡ Quick Setup

### Step 1: Navigate to Project
```bash
cd ctron-mobile-new
```

### Step 2: Fix React DOM Issue (if needed)
```bash
# Run the automated fixer
yarn fix:react-dom
```

### Step 3: Start the App
```bash
# Start the development server
yarn start
```

### Step 4: Choose Platform
- Press **'i'** for iOS Simulator
- Press **'a'** for Android Emulator
- Scan QR code for physical device

## 🔧 If You Encounter Issues

### Issue: "Unable to resolve react-dom"

**Quick Fix:**
```bash
# Run the React DOM fixer
yarn fix:react-dom

# Then start the app
yarn start
```

**Manual Fix:**
```bash
# Clear everything and reinstall
yarn fix:deps
yarn start
```

### Issue: Metro Bundling Timeout

**Quick Fix:**
```bash
# Clear Metro cache
yarn fix:metro

# Start with cleared cache
yarn start --clear
```

### Issue: General NativeBase Issues

**Quick Fix:**
```bash
# Run the comprehensive fixer
yarn fix:nativebase

# Start the app
yarn start
```

## 🧪 Test the Implementation

### 1. Navigate to Test Screen
- Open the app
- Navigate to "TestNativeBaseScreen" (if available in navigation)
- Verify all components render correctly

### 2. Test Core Screens
- **Home Screen**: Check service cards and quick actions
- **Book Job Screen**: Test the multi-step booking process
- **My Jobs Screen**: Verify job listings and filtering
- **Profile Screen**: Test profile editing and settings
- **Payment Screen**: Check payment form and processing

### 3. Verify Theme
- Check that CTRON blue (#2196F3) is used consistently
- Verify that components have proper spacing and shadows
- Test that buttons have loading states

## 📱 Platform Testing

### iOS Testing
```bash
# Start for iOS
yarn ios

# Or use Expo
yarn start
# Then press 'i'
```

### Android Testing
```bash
# Start for Android
yarn android

# Or use Expo
yarn start
# Then press 'a'
```

## 🎯 Success Indicators

You'll know everything is working when:

- [x] **App starts without errors**
- [x] **NativeBase components render correctly**
- [x] **CTRON theme colors are applied**
- [x] **Navigation works smoothly**
- [x] **No "react-dom" bundling errors**
- [x] **Components have proper styling**

## 🔍 Debugging Commands

### Check Dependencies
```bash
# Check NativeBase installation
yarn debug:deps

# Validate NativeBase setup
yarn debug:nativebase
```

### Performance Analysis
```bash
# Analyze bundle size
yarn debug:bundle

# Check for issues
yarn typecheck
```

### Cache Management
```bash
# Clear all caches
yarn cache:clean

# Reset everything
yarn start:reset
```

## 📚 Key Files to Know

### Core Implementation
- `src/theme/nativeBaseTheme.ts` - Custom CTRON theme
- `src/components/modern/` - NativeBase components
- `src/screens/Homeowner/Modern*` - NativeBase screens
- `App.tsx` - NativeBaseProvider setup

### Configuration
- `metro.config.js` - Metro bundler configuration
- `babel.config.js` - Babel transformation rules
- `package.json` - Dependencies and scripts
- `react-dom-shim.js` - React DOM compatibility layer

### Documentation
- `NATIVEBASE_TROUBLESHOOTING_GUIDE.md` - Detailed troubleshooting
- `NATIVEBASE_TESTING_CHECKLIST.md` - Comprehensive testing
- `NATIVEBASE_DEPLOYMENT_GUIDE.md` - Production deployment

## 🚨 Common Issues & Quick Fixes

| Issue | Quick Fix |
|-------|-----------|
| "Unable to resolve react-dom" | `yarn fix:react-dom` |
| Metro bundling timeout | `yarn fix:metro` |
| Components not rendering | `yarn fix:nativebase` |
| TypeScript errors | `yarn typecheck` |
| Cache issues | `yarn start:reset` |
| Dependency conflicts | `yarn fix:deps` |

## 🎉 You're Ready!

Once the app starts successfully:

1. **Explore the screens** - Navigate through all the modern screens
2. **Test interactions** - Try booking a service, viewing jobs, etc.
3. **Check responsiveness** - Test on different screen sizes
4. **Verify performance** - Ensure smooth animations and transitions

## 📞 Need Help?

If you encounter any issues:

1. **Check the error message** in the terminal/console
2. **Run the appropriate fix script** from the table above
3. **Consult the troubleshooting guide** for detailed solutions
4. **Clear caches and restart** if all else fails

## 🔄 Development Workflow

### Daily Development
```bash
# Start development
yarn start

# Run tests (if implemented)
yarn test

# Check code quality
yarn lint
yarn typecheck
```

### Before Committing
```bash
# Format code
yarn format

# Run linting
yarn lint

# Check types
yarn typecheck
```

### Deployment
```bash
# Build for production
yarn build:ios    # iOS build
yarn build:android # Android build
```

## 🎯 Next Steps

After getting the app running:

1. **Follow the testing checklist** to validate all functionality
2. **Review the implementation report** to understand the architecture
3. **Plan additional features** using the NativeBase component library
4. **Set up monitoring** for production deployment

**Happy coding with NativeBase! 🚀**