declare namespace NodeJS {
    interface ProcessEnv {
      // Server Configuration
      NODE_ENV: string;
      PORT: string;
      HOST: string;

      // Database Configuration
      DATABASE_URL: string;

      // Authentication & Security
      JWT_SECRET: string;
      JWT_REFRESH_SECRET: string;
      JWT_EXPIRES_IN: string;
      JWT_REFRESH_EXPIRES_IN: string;

      // Stripe Configuration
      STRIPE_SECRET_KEY: string;
      STRIPE_WEBHOOK_SECRET: string;
      STRIPE_API_VERSION: string;

      // OpenAI Configuration
      OPENAI_API_KEY: string;
      OPENAI_API_URL: string;

      // AWS Configuration
      AWS_REGION: string;
      AWS_BUCKET_NAME: string;
      AWS_ACCESS_KEY_ID: string;
      AWS_SECRET_ACCESS_KEY: string;
      AWS_S3_ENDPOINT: string;

      // Email Configuration
      EMAIL_SERVICE: string;
      EMAIL_USER: string;
      EMAIL_PASS: string;

      // Expo Configuration
      EXPO_TOKEN: string;
      EXPO_API_URL: string;

      // CORS Configuration
      CORS_ORIGIN: string;
      SOCKET_CORS_ORIGIN: string;

      // Rate Limiting
      RATE_LIMIT_WINDOW_MS: string;
      RATE_LIMIT_MAX_REQUESTS: string;

      // App Configuration
      APP_VERSION: string;
      APP_NAME: string;

      // Socket.IO Configuration
      SOCKET_PING_TIMEOUT: string;
      SOCKET_PING_INTERVAL: string;
    }
  }
