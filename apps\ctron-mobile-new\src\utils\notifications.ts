// CTRON Home - Notification Utilities
// Handles push notifications and local notifications

import Constants from 'expo-constants';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';

import { API_BASE_URL } from '../config/api.config';

import { getAuthToken } from './auth.utils';
import { debugLogger } from './debugLogger';
import { Platform } from './platformUtils';

// Configure notification behavior for native platforms
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export class NotificationService {
  private static instance: NotificationService;
  private pushToken: string | null = null;
  private isInitialized: boolean = false;

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Request notification permissions and get push token
   */
  async initialize(): Promise<string | null> {
    try {
      if (this.isInitialized && this.pushToken) {
        return this.pushToken;
      }

      // Check if device supports push notifications
      if (!Device.isDevice) {
        debugLogger.warn('Push notifications only work on physical devices');
        return null;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        debugLogger.warn('Notification permissions not granted');
        return null;
      }

      // Get push token
      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      this.pushToken = tokenData.data;
      this.isInitialized = true;

      debugLogger.success('Push token obtained:', this.pushToken);

      // Register token with backend
      if (this.pushToken) {
        await this.registerTokenWithBackend(this.pushToken);
      }

      return this.pushToken;
    } catch (error) {
      debugLogger.error('Failed to initialize notifications:', error);
      return null;
    }
  }



  /**
   * Register push token with backend
   */
  private async registerTokenWithBackend(token: string): Promise<void> {
    try {
      const authToken = await getAuthToken();
      if (!authToken) {
        debugLogger.warn('No auth token available for push token registration');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/notifications/register-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          pushToken: token,
          platform: Platform.OS,
          deviceInfo: {
            brand: Device.brand,
            modelName: Device.modelName,
            osName: Device.osName,
            osVersion: Device.osVersion,
          }
        }),
      });

      if (response.ok) {
        debugLogger.success('Push token registered with backend');
      } else {
        debugLogger.error('Failed to register push token with backend:', response.status);
      }
    } catch (error) {
      debugLogger.error('Error registering push token with backend:', error);
    }
  }

  /**
   * Get the current push token
   */
  getPushToken(): string | null {
    return this.pushToken;
  }

  /**
   * Schedule a local notification
   */
  async scheduleLocalNotification(
    title: string,
    body: string,
    data?: Record<string, unknown>,
    trigger?: Notifications.NotificationTriggerInput | null
  ): Promise<string> {
    try {


      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
          badge: 1,
        },
        trigger: trigger || null, // null means immediate
      });

      debugLogger.info('Local notification scheduled:', identifier);
      return identifier;
    } catch (error) {
      debugLogger.error('Failed to schedule local notification:', error);
      throw error;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
      debugLogger.info('Notification cancelled:', identifier);
    } catch (error) {
      debugLogger.error('Failed to cancel notification:', error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      debugLogger.info('All notifications cancelled');
    } catch (error) {
      debugLogger.error('Failed to cancel all notifications:', error);
    }
  }

  /**
   * Handle notification received while app is in foreground
   */
  onNotificationReceived(callback: (notification: Notifications.Notification) => void) {
    return Notifications.addNotificationReceivedListener(callback);
  }

  /**
   * Handle notification tapped/opened
   */
  onNotificationResponse(callback: (response: Notifications.NotificationResponse) => void) {
    return Notifications.addNotificationResponseReceivedListener(callback);
  }

  /**
   * Remove notification listeners
   */
  removeListeners(subscription: Notifications.Subscription) {
    if (subscription && subscription.remove) {
      subscription.remove();
    }
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();

// Legacy function for backward compatibility
export async function registerForPush() {
  return await notificationService.initialize();
}
