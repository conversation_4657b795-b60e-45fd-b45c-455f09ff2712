// apps/backend/src/config/env.ts
import dotenv from 'dotenv';
import { z } from 'zod';

dotenv.config();

// Environment validation schema
const envSchema = z.object({
  // Server Configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().regex(/^\d+$/).transform(Number).default('3001'),
  HOST: z.string().default('localhost'),

  // Database Configuration
  DATABASE_URL: z.string().url('DATABASE_URL must be a valid URL'),

  // Authentication & Security
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_REFRESH_SECRET: z.string().min(32, 'JWT_REFRESH_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('15m'), // Reduced from 7d for security
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),

  // Stripe Configuration (NO DEFAULT VALUES for production secrets)
  STRIPE_SECRET_KEY: z.string().min(1, 'STRIPE_SECRET_KEY is required'),
  STRIPE_WEBHOOK_SECRET: z.string().min(1, 'STRIPE_WEBHOOK_SECRET is required'),
    STRIPE_API_VERSION: z.enum([
    '2025-05-28.basil',
    '2025-04-30.basil',
    '2025-03-31.basil',
    '2025-03-01.dashboard',
    '2024-10-28.acacia',
  ]).default('2025-05-28.basil'),

  // OpenAI Configuration (NO DEFAULT VALUES for production secrets)
  OPENAI_API_KEY: z.string().min(1, 'OPENAI_API_KEY is required'),
  OPENAI_API_URL: z.string().url().default('https://api.openai.com/v1'),

  // AWS Configuration (NO DEFAULT VALUES for production secrets)
  AWS_REGION: z.string().default('eu-west-2'),
  AWS_BUCKET_NAME: z.string().min(1, 'AWS_BUCKET_NAME is required'),
  AWS_ACCESS_KEY_ID: z.string().min(1, 'AWS_ACCESS_KEY_ID is required'),
  AWS_SECRET_ACCESS_KEY: z.string().min(1, 'AWS_SECRET_ACCESS_KEY is required'),
  AWS_S3_ENDPOINT: z.string().url().default('https://s3.eu-west-2.amazonaws.com'),

  // Email Configuration
  EMAIL_SERVICE: z.string().default('gmail'),
  EMAIL_USER: z.string().email().optional(),
  EMAIL_PASS: z.string().optional(),

  // Expo Configuration
  EXPO_TOKEN: z.string().optional(),
  EXPO_API_URL: z.string().url().default('https://exp.host/--/api/v2'),

  // CORS Configuration
  CORS_ORIGIN: z.string().default('http://localhost:5173,http://localhost:3000'),
  SOCKET_CORS_ORIGIN: z.string().default('*'),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().regex(/^\d+$/).transform(Number).default('900000'),
  RATE_LIMIT_MAX_REQUESTS: z.string().regex(/^\d+$/).transform(Number).default('100'),

  // App Configuration
  APP_VERSION: z.string().default('1.0.0'),
  APP_NAME: z.string().default('CTRON Home'),

  // Socket.IO Configuration
  SOCKET_PING_TIMEOUT: z.string().regex(/^\d+$/).transform(Number).default('60000'),
  SOCKET_PING_INTERVAL: z.string().regex(/^\d+$/).transform(Number).default('25000'),
});

// Validate environment variables
function validateEnv() {
  try {
    const validatedEnv = envSchema.parse(process.env);

    // Additional production-specific validations
    if (validatedEnv.NODE_ENV === 'production') {
      // Ensure no test/development keys in production
      if (validatedEnv.STRIPE_SECRET_KEY.includes('test')) {
        throw new Error('❌ Production environment cannot use Stripe test keys');
      }

      if (validatedEnv.OPENAI_API_KEY.includes('default') || validatedEnv.OPENAI_API_KEY.length < 20) {
        throw new Error('❌ Production environment requires valid OpenAI API key');
      }

      if (validatedEnv.AWS_ACCESS_KEY_ID.includes('dev') || validatedEnv.AWS_SECRET_ACCESS_KEY.includes('dev')) {
        throw new Error('❌ Production environment cannot use development AWS credentials');
      }

      // Ensure JWT secrets are strong in production
      if (validatedEnv.JWT_SECRET.length < 64) {
        throw new Error('❌ Production JWT_SECRET must be at least 64 characters');
      }

      if (validatedEnv.JWT_REFRESH_SECRET.length < 64) {
        throw new Error('❌ Production JWT_REFRESH_SECRET must be at least 64 characters');
      }
    }

    return validatedEnv;
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`❌ Environment validation failed:\n${missingVars.join('\n')}`);
    }
    throw error;
  }
}

// Export validated environment variables
export const env = validateEnv();

// Log environment status (without sensitive data) - will be logged by startup validator
// Removed console.log statements to use proper logging system
