// CTRON Home - Loading State Component
// Consistent loading indicators across the app

import React from 'react';

import { useTheme } from '../../context/ThemeContext';
import { View, Text, ActivityIndicator, StyleSheet } from '../../utils/platformUtils';

interface LoadingStateProps {
  message?: string;
  size?: 'small' | 'large';
  color?: string;
  style?: any;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  size = 'large',
  color,
  style,
}) => {
  const { colors, spacing, typography } = useTheme();

  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator 
        size={size} 
        color={color || colors.primary.main} 
      />
      {message && (
        <Text style={[styles.message, { 
          color: colors.text.secondary,
          fontSize: typography.fontSize.md,
          marginTop: spacing.md,
        }]}>
          {message}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  message: {
    textAlign: 'center',
  },
});