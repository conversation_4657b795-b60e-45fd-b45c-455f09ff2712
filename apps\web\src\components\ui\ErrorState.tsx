// CTRON Home - Web Error State Component
// Consistent error displays for web admin

import React from 'react';
import { Button } from './button';
import { AlertTriangle } from 'lucide-react';

interface ErrorStateProps {
  title?: string;
  message: string;
  actionTitle?: string;
  onAction?: () => void;
  className?: string;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'Something went wrong',
  message,
  actionTitle = 'Try Again',
  onAction,
  className = '',
}) => {
  return (
    <div className={`text-center py-16 ${className}`}>
      <div className="mx-auto w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mb-4">
        <AlertTriangle className="w-12 h-12 text-red-600" />
      </div>
      <h3 className="text-xl font-semibold text-red-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-4 max-w-md mx-auto">{message}</p>
      {onAction && (
        <Button onClick={onAction} variant="secondary">
          {actionTitle}
        </Button>
      )}
    </div>
  );
};

export default ErrorState;