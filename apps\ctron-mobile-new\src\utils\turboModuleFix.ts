// Fix for TurboModule Registry errors in development
// These modules are used for debugging and aren't essential for app functionality

// Define interfaces for TurboModuleRegistry and related modules
// Removed unused SourceCodeModule interface

// Removed unused interfaces to reduce linting warnings

// Global declarations moved to global.d.ts to avoid conflicts

export const setupTurboModuleFix = () => {
  if (__DEV__) {
    // Mock the TurboModule registry to prevent crashes
    try {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { TurboModuleRegistry } = require('react-native');
      
      // Add 'get' method to TurboModuleRegistry if it doesn't exist
      // This fixes warnings in react-native-web
      if (TurboModuleRegistry && !TurboModuleRegistry.get) {
        TurboModuleRegistry.get = (_name: string) => {
          // Mocking TurboModuleRegistry.get
          return null;
        };
      }
      
      if (TurboModuleRegistry && TurboModuleRegistry.getEnforcing) {
        const originalGetEnforcing = TurboModuleRegistry.getEnforcing;
        TurboModuleRegistry.getEnforcing = (name: string) => {
          // Return mock for problematic debugging modules
          if (name === 'SourceCode') {
            return {
              getScriptURL: () => 'http://localhost:8081/',
              getScriptText: () => ''
            };
          }
          
          if (name.includes('NativePerformance')) {
            // Handle both NativePerformance and NativePerformanceObserver
            if (name === 'NativePerformanceObserver' || name === 'NativePerformanceObserverCxx') {
              return {
                startReporting: () => {},
                stopReporting: () => {},
                setIsBuffered: () => {},
                popPendingEntries: () => ({ entries: [], droppedEntriesCount: 0 }),
                setOnPerformanceEntryCallback: () => {},
                logRawEntry: () => {},
                getEventCounts: () => ({}),
                setDurationThreshold: () => {},
                clearEntries: () => {},
                getEntries: () => [],
                getSupportedPerformanceEntryTypes: () => ['mark', 'measure']
              };
            }
            
            // Handle NativePerformance
            return {
              mark: () => {},
              measure: () => {},
              clearMarks: () => {},
              clearMeasures: () => {},
              getEntries: () => [],
              warnNoNativePerformanceObserver: () => {}
            };
          }

          if (name === 'LogBox') {
            return {
              install: () => {},
              uninstall: () => {},
              ignoreLogs: () => {},
              ignoreAllLogs: () => {}
            };
          }

          // Try original implementation for other modules
          try {
            return originalGetEnforcing(name);
          } catch (error) {
            // TurboModule not found, returning mock
            return {};
          }
        };
      }
    } catch (_error) {
          // TurboModule fix setup failed
    }
  }
};
