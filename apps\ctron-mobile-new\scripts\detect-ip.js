#!/usr/bin/env node
/**
 * CTRON Home - IP Detection Script
 * Automatically detects the local network IP address for development
 */

const os = require('os');
const fs = require('fs');
const path = require('path');

/**
 * Get the local network IP address
 * @returns {string} The local IP address
 */
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  
  // Priority order for interface types
  const interfacePriority = ['Ethernet', 'Wi-Fi', 'WiFi', 'Wireless', 'en0', 'en1', 'eth0', 'wlan0'];
  
  let fallbackIP = null;
  
  // First, try to find IP from priority interfaces
  for (const interfaceName of interfacePriority) {
    const networkInterface = interfaces[interfaceName];
    if (networkInterface) {
      for (const alias of networkInterface) {
        if (alias.family === 'IPv4' && !alias.internal) {
          return alias.address;
        }
      }
    }
  }
  
  // If no priority interface found, search all interfaces
  for (const interfaceName in interfaces) {
    const networkInterface = interfaces[interfaceName];
    for (const alias of networkInterface) {
      if (alias.family === 'IPv4' && !alias.internal) {
        if (!fallbackIP) {
          fallbackIP = alias.address;
        }
        // Prefer non-virtual interfaces
        if (!interfaceName.toLowerCase().includes('virtual') && 
            !interfaceName.toLowerCase().includes('vmware') &&
            !interfaceName.toLowerCase().includes('vbox')) {
          return alias.address;
        }
      }
    }
  }
  
  return fallbackIP || 'localhost';
}

/**
 * Update the .env file with the detected IP
 * @param {string} detectedIP - The detected IP address
 */
function updateEnvFile(detectedIP) {
  const envPath = path.join(__dirname, '..', '.env');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env file not found at:', envPath);
    process.exit(1);
  }
  
  let envContent = fs.readFileSync(envPath, 'utf8');
  
  // Update IP-related environment variables
  const updates = [
    { key: 'EXPO_PUBLIC_DEV_SERVER_IP', value: detectedIP },
    { key: 'EXPO_PUBLIC_API_URL', value: `http://${detectedIP}:3001` },
    { key: 'EXPO_PUBLIC_API_BASE_URL', value: `http://${detectedIP}:3001` },
    { key: 'EXPO_PUBLIC_SOCKET_URL', value: `http://${detectedIP}:3001` }
  ];
  
  updates.forEach(({ key, value }) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, `${key}=${value}`);
      console.log(`✅ Updated ${key}=${value}`);
    } else {
      console.log(`⚠️  ${key} not found in .env file`);
    }
  });
  
  fs.writeFileSync(envPath, envContent);
  console.log('\n🎉 .env file updated successfully!');
}

/**
 * Main function
 */
function main() {
  console.log('🔍 Detecting local network IP address...');
  
  const detectedIP = getLocalIP();
  
  console.log(`📍 Detected IP: ${detectedIP}`);
  
  if (detectedIP === 'localhost') {
    console.log('⚠️  Could not detect network IP, using localhost');
    console.log('   This may not work on physical devices.');
  }
  
  // Ask for confirmation in interactive mode
  if (process.argv.includes('--auto') || process.argv.includes('-y')) {
    updateEnvFile(detectedIP);
  } else {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question(`\nUpdate .env file with IP ${detectedIP}? (y/N): `, (answer) => {
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        updateEnvFile(detectedIP);
      } else {
        console.log('❌ Operation cancelled');
      }
      rl.close();
    });
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { getLocalIP, updateEnvFile };