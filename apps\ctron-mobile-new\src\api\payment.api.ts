// src/api/payment.api.ts

import axios from 'axios';

import { API_BASE_URL } from '../config/api.config';
import { getAuthToken } from '../utils/auth.utils';

// Helper function to make authenticated API calls
const makeAuthenticatedRequest = async (method: string, endpoint: string, data?: unknown) => {
  const token = await getAuthToken();
  const config: any = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  };

  if (data) {
    config.data = data;
  }

  return axios(config);
};

const PaymentAPI = {
  // 💳 Called by homeowner to make a direct payment for a job
  createPayment: async (jobId: string, amount: number, currency: string = 'GBP') => {
    const res = await makeAuthenticatedRequest('POST', '/api/payments/create', {
      jobId,
      amount,
      currency,
    });
    return res.data;
  },

  // 🧾 Called to initiate Stripe payment sheet (native)
  createPaymentIntent: async () => {
    const res = await makeAuthenticatedRequest('POST', '/api/payments/create-intent');
    return res.data; // should return { clientSecret: string }
  },

  // 📈 Called by technician to check total earnings
  getEarnings: async () => {
    const res = await makeAuthenticatedRequest('GET', '/api/payments/earnings');
    return res.data;
  },
};

export default PaymentAPI;
