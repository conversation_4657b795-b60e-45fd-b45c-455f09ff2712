// CTRON Home Design System - Avatar Component
// Standardized avatar with consistent styling

import React from 'react';

import { View, Text, Image, ViewStyle, TextStyle } from '../../utils/platformUtils';
import { tokens } from '../tokens';

export interface AvatarProps {
  source?: { uri: string } | number;
  name?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  variant?: 'circular' | 'rounded' | 'square';
  backgroundColor?: string;
  textColor?: string;
  style?: ViewStyle;
}

export const Avatar: React.FC<AvatarProps> = ({
  source,
  name,
  size = 'md',
  variant = 'circular',
  backgroundColor = tokens.colors.primary[500],
  textColor = tokens.colors.neutral[0],
  style,
}) => {
  const getSizeStyles = (): { container: ViewStyle; text: TextStyle } => {
    switch (size) {
      case 'xs':
        return {
          container: { width: 24, height: 24 },
          text: { fontSize: tokens.typography.fontSize.xs },
        };
      
      case 'sm':
        return {
          container: { width: 32, height: 32 },
          text: { fontSize: tokens.typography.fontSize.sm },
        };
      
      case 'md':
        return {
          container: { width: 40, height: 40 },
          text: { fontSize: tokens.typography.fontSize.base },
        };
      
      case 'lg':
        return {
          container: { width: 48, height: 48 },
          text: { fontSize: tokens.typography.fontSize.lg },
        };
      
      case 'xl':
        return {
          container: { width: 64, height: 64 },
          text: { fontSize: tokens.typography.fontSize.xl },
        };
      
      case '2xl':
        return {
          container: { width: 80, height: 80 },
          text: { fontSize: tokens.typography.fontSize['2xl'] },
        };
      
      default:
        return {
          container: { width: 40, height: 40 },
          text: { fontSize: tokens.typography.fontSize.base },
        };
    }
  };

  const getVariantStyles = (): ViewStyle => {
    const sizeStyles = getSizeStyles();
    const baseStyle: ViewStyle = {
      ...sizeStyles.container,
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
    };

    switch (variant) {
      case 'circular':
        return {
          ...baseStyle,
          borderRadius: (sizeStyles.container as any).width / 2,
        };
      
      case 'rounded':
        return {
          ...baseStyle,
          borderRadius: tokens.borderRadius.lg,
        };
      
      case 'square':
        return {
          ...baseStyle,
          borderRadius: 0,
        };
      
      default:
        return baseStyle;
    }
  };

  const getInitials = (fullName: string): string => {
    const names = fullName.trim().split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  };

  const sizeStyles = getSizeStyles();
  const containerStyle: ViewStyle = {
    ...getVariantStyles(),
    backgroundColor: source ? 'transparent' : backgroundColor,
    ...style,
  };

  const textStyle: TextStyle = {
    ...sizeStyles.text,
    fontFamily: tokens.typography.fontFamily.primary,
    fontWeight: tokens.typography.fontWeight.medium,
    color: textColor,
    textAlign: 'center',
  };

  const imageStyle: ViewStyle = {
    width: '100%',
    height: '100%',
  };

  return (
    <View style={containerStyle}>
      {source ? (
        <Image source={source} style={imageStyle} resizeMode="cover" />
      ) : (
        <Text style={textStyle}>
          {name ? getInitials(name) : '?'}
        </Text>
      )}
    </View>
  );
};

// Preset avatar variants for common use cases
export const UserAvatar: React.FC<Omit<AvatarProps, 'variant'>> = (props) => (
  <Avatar variant="circular" {...props} />
);

export const CompanyAvatar: React.FC<Omit<AvatarProps, 'variant'>> = (props) => (
  <Avatar variant="rounded" {...props} />
);