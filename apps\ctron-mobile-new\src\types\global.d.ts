declare global {
  interface Global {
    nativeCallSyncHook: unknown;
    __DEV__: boolean;
    ErrorUtils?: {
      getGlobalHandler?: () => (error: unknown, isFatal?: boolean) => void;
      setGlobalHandler?: (handler: (error: unknown, isFatal?: boolean) => void) => void;
    };
  }
}

declare module 'react-native' {
  interface NativeModulesStatic {
    Device: {
      isTablet: boolean;
      isDisplayZoomed: boolean;
    };
  }

  interface LinkingStatic {
    openURL(url: string): Promise<void>;
    canOpenURL(url: string): Promise<boolean>;
    openSettings(): Promise<void>;
    addEventListener(type: string, handler: (event: { url: string }) => void): void;
    removeEventListener(type: string, handler: (event: { url: string }) => void): void;
    getInitialURL(): Promise<string | null>;
  }
}