import { Router } from 'express';
import { authMiddleware } from '../middleware/auth.middleware';
import { UserController } from '../controllers/user.controller';

const router = Router();

router.get('/profile',  authMiddleware, UserController.getProfile);
router.put('/profile',  authMiddleware, UserController.updateProfile);
router.patch('/push-token', authMiddleware, UserController.savePushToken);

export default router;
