// CTRON Forgot Password Screen - Clean, Professional Design
// Based on reference UI with CTRON branding

import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useRef, useEffect } from 'react';

import AuthAPI from '../../api/auth.api';
import { useAccessibility } from '../../hooks/useAccessibility';
import type { AuthStackParamList } from '../../navigation/AuthStack';
import { ctronStyles, ctronColors, ctronSpacing, ctronBorderRadius } from '../../styles/ctronDesignSystem';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert, StatusBar, KeyboardAvoidingView, Animated, Easing, Platform, ActivityIndicator, SafeAreaView } from '../../utils/platformUtils';

export default function ForgotPasswordScreen() {
  const navigation = useNavigation<StackNavigationProp<AuthStackParamList, 'ForgotPassword'>>();
  const { announceText } = useAccessibility({
    announceOnMount: 'Forgot password screen loaded. Please enter your email address.',
  });

  const [email, setEmail] = useState('');
  const [errors, setErrors] = useState<{ email?: string; general?: string }>({});
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  
  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const validateEmail = (value: string) => {
    if (!value.trim()) {
      return 'Email is required';
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return 'Please enter a valid email address';
    }
    return '';
  };
  
  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (errors.email) {
      const emailError = validateEmail(value);
      setErrors(prev => ({ ...prev, email: emailError }));
    }
  };

  const handleResetPassword = async () => {
    setErrors({});
    
    const emailError = validateEmail(email);
    if (emailError) {
      setErrors({ email: emailError });
      announceText('Please fix the form errors before submitting');
      return;
    }

    setLoading(true);
    
    try {
      await AuthAPI.forgotPassword(email);
      setEmailSent(true);
      announceText('Password reset email sent successfully');
    } catch (error: unknown) {
      console.error('Forgot password error:', error);
      const err = error as { response?: { data?: { message?: string } }; message?: string };
      const errorMessage = err?.response?.data?.message || 'Failed to send reset email. Please try again.';
      setErrors({ general: errorMessage });
      announceText(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (emailSent) {
    return (
      <SafeAreaView style={ctronStyles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={ctronColors.background} />
        <View style={ctronStyles.safeContainer}>
          <Animated.View 
            style={[
              ctronStyles.centerContent,
              { flex: 1, opacity: fadeAnim }
            ]}
          >
            {/* Success Icon */}
            <View style={[ctronStyles.logo, { backgroundColor: ctronColors.success, marginBottom: ctronSpacing.xl }]}>
              <Text style={[ctronStyles.logoText, { fontSize: 32 }]}>✓</Text>
            </View>
            
            <Text style={[ctronStyles.welcomeTitle, { marginBottom: ctronSpacing.md }]}>Check Your Email</Text>
            <Text style={[ctronStyles.welcomeSubtitle, { textAlign: 'center', marginBottom: ctronSpacing.xl }]}>
              We've sent a password reset link to {email}
            </Text>
            
            <TouchableOpacity
              style={ctronStyles.primaryButton}
              onPress={() => navigation.navigate('Login')}
            >
              <Text style={ctronStyles.primaryButtonText}>Back to Login</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[ctronStyles.linkButton, { marginTop: ctronSpacing.lg }]}
              onPress={() => {
                setEmailSent(false);
                setEmail('');
                setErrors({});
              }}
            >
              <Text style={ctronStyles.linkButtonText}>Didn't receive email? Try again</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={ctronStyles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={ctronColors.background} />
      <KeyboardAvoidingView
        style={ctronStyles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={ctronStyles.safeContainer}>
            {/* Header Section */}
            <Animated.View 
              style={[
                ctronStyles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {/* CTRON Logo */}
              <View style={ctronStyles.logo}>
                <Text style={ctronStyles.logoText}>C</Text>
              </View>
              <Text style={ctronStyles.brandName}>CTRON</Text>
              
              {/* Title */}
              <Text style={ctronStyles.welcomeTitle}>Forgot Password?</Text>
              <Text style={ctronStyles.welcomeSubtitle}>
                No worries! Enter your email address and we'll send you a link to reset your password.
              </Text>
            </Animated.View>

            {/* Form Section */}
            <Animated.View
              style={[
                ctronStyles.formContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {/* Email Input */}
              <View style={ctronStyles.inputGroup}>
                <Text style={ctronStyles.inputLabel}>Email</Text>
                <TextInput
                  style={[
                    ctronStyles.input,
                    errors.email && ctronStyles.inputError,
                  ]}
                  placeholder="Enter your email address"
                  placeholderTextColor={ctronColors.inputPlaceholder}
                  value={email}
                  onChangeText={handleEmailChange}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  accessibilityLabel="Email address"
                  testID="forgot-password-email-input"
                />
                {errors.email && (
                  <Text style={ctronStyles.errorText}>{errors.email}</Text>
                )}
              </View>

              {/* Reset Password Button */}
              <TouchableOpacity
                style={[
                  ctronStyles.primaryButton,
                  loading && { opacity: 0.7 },
                ]}
                onPress={handleResetPassword}
                disabled={loading}
                accessibilityLabel="Send reset password email"
              >
                {loading ? (
                  <View style={[ctronStyles.row, { justifyContent: 'center' }]}>
                    <ActivityIndicator size="small" color={ctronColors.textLight} />
                    <Text style={[ctronStyles.primaryButtonText, { marginLeft: 8 }]}>Sending...</Text>
                  </View>
                ) : (
                  <Text style={ctronStyles.primaryButtonText}>Send Reset Link</Text>
                )}
              </TouchableOpacity>

              {/* Error Display */}
              {errors.general && (
                <View style={{ marginTop: ctronSpacing.md, padding: ctronSpacing.md, backgroundColor: ctronColors.error + '10', borderRadius: ctronBorderRadius.md }}>
                  <Text style={[ctronStyles.errorText, { textAlign: 'center' }]}>{errors.general}</Text>
                </View>
              )}

              {/* Back to Login Link */}
              <View style={[ctronStyles.row, ctronStyles.centerContent, { marginTop: ctronSpacing.xl }]}>
                <Text style={{ color: ctronColors.textSecondary, fontSize: 14 }}>Remember your password? </Text>
                <TouchableOpacity
                  onPress={() => navigation.navigate('Login')}
                  accessibilityLabel="Go back to login"
                >
                  <Text style={[ctronStyles.linkButtonText, { fontSize: 14 }]}>Sign In</Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}