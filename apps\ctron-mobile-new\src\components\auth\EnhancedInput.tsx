// Enhanced Input Component with Floating Labels and Animations
// Implementation of UI improvements for CTRON authentication

import React, { useState, useRef, useEffect } from 'react';

import { ctronColors, ctronSpacing, ctronBorderRadius, ctronTypography } from '../../styles/ctronDesignSystem';
import { View, Text, TextInput, TouchableOpacity, Animated, Easing, StyleSheet } from '../../utils/platformUtils';

interface EnhancedInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  onBlur?: () => void;
  error?: string;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  placeholder?: string;
  showPasswordToggle?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

const EnhancedInput: React.FC<EnhancedInputProps> = ({
  label,
  value,
  onChangeText,
  onBlur,
  error,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'none',
  placeholder,
  showPasswordToggle = false,
  accessibilityLabel,
  accessibilityHint,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  // Animation values
  const labelAnimation = useRef(new Animated.Value(value ? 1 : 0)).current;
  const borderAnimation = useRef(new Animated.Value(0)).current;
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  // Animate label position
  useEffect(() => {
    Animated.timing(labelAnimation, {
      toValue: isFocused || value ? 1 : 0,
      duration: 200,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: false,
    }).start();
  }, [isFocused, value]);

  // Animate border color
  useEffect(() => {
    Animated.timing(borderAnimation, {
      toValue: isFocused ? 1 : 0,
      duration: 200,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: false,
    }).start();
  }, [isFocused]);

  // Shake animation for errors
  useEffect(() => {
    if (error) {
      Animated.sequence([
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: -10,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 10,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnimation, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [error]);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const labelTop = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [18, -8],
  });

  const labelFontSize = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [16, 12],
  });

  const borderColor = borderAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [ctronColors.inputBorder, ctronColors.primary],
  });

  const labelColor = error 
    ? ctronColors.error 
    : isFocused 
    ? ctronColors.primary 
    : ctronColors.textSecondary;

  return (
    <Animated.View 
      style={[
        styles.container,
        { transform: [{ translateX: shakeAnimation }] }
      ]}
    >
      <View style={styles.inputContainer}>
        <Animated.Text
          style={[
            styles.label,
            {
              top: labelTop,
              fontSize: labelFontSize,
              color: labelColor,
            },
          ]}
        >
          {label}
        </Animated.Text>
        
        <Animated.View
          style={[
            styles.inputWrapper,
            { borderColor },
            error && styles.inputError,
          ]}
        >
          <TextInput
            style={[
              styles.input,
              showPasswordToggle && styles.inputWithToggle,
            ]}
            value={value}
            onChangeText={onChangeText}
            onFocus={handleFocus}
            onBlur={handleBlur}
            secureTextEntry={secureTextEntry && !showPassword}
            keyboardType={keyboardType}
            autoCapitalize={autoCapitalize}
            placeholder={isFocused ? placeholder : ''}
            placeholderTextColor={ctronColors.inputPlaceholder}
            accessibilityLabel={accessibilityLabel || label}
            accessibilityHint={accessibilityHint}
            accessibilityState={{ invalid: !!error }}
          />
          
          {showPasswordToggle && (
            <TouchableOpacity
              style={styles.passwordToggle}
              onPress={togglePasswordVisibility}
              accessibilityRole="button"
              accessibilityLabel={showPassword ? 'Hide password' : 'Show password'}
            >
              <Text style={styles.passwordToggleText}>
                {showPassword ? '👁️' : '👁️‍🗨️'}
              </Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      </View>
      
      {error && (
        <Animated.View style={styles.errorContainer}>
          <Text 
            style={styles.errorText}
            accessibilityRole="alert"
            accessibilityLiveRegion="polite"
          >
            ⚠️ {error}
          </Text>
        </Animated.View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: ctronSpacing.lg,
  },
  inputContainer: {
    position: 'relative',
  },
  label: {
    position: 'absolute',
    left: ctronSpacing.md,
    zIndex: 1,
    backgroundColor: ctronColors.background,
    paddingHorizontal: 4,
    fontWeight: ctronTypography.fontWeight.medium,
  },
  inputWrapper: {
    borderWidth: 2,
    borderRadius: ctronBorderRadius.lg,
    backgroundColor: ctronColors.inputBackground,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 56,
  },
  input: {
    flex: 1,
    paddingHorizontal: ctronSpacing.md,
    paddingVertical: ctronSpacing.md,
    fontSize: ctronTypography.fontSize.base,
    color: ctronColors.textPrimary,
    fontWeight: ctronTypography.fontWeight.normal,
  },
  inputWithToggle: {
    paddingRight: 50,
  },
  inputError: {
    borderColor: ctronColors.error,
  },
  passwordToggle: {
    position: 'absolute',
    right: ctronSpacing.md,
    padding: ctronSpacing.sm,
  },
  passwordToggleText: {
    fontSize: 18,
    color: ctronColors.textSecondary,
  },
  errorContainer: {
    marginTop: ctronSpacing.xs,
    paddingHorizontal: ctronSpacing.sm,
  },
  errorText: {
    fontSize: ctronTypography.fontSize.sm,
    color: ctronColors.error,
    fontWeight: ctronTypography.fontWeight.medium,
  },
});

export default EnhancedInput;
export { EnhancedInput };