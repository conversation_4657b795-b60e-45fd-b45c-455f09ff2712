{"name": "ctron-mobile", "version": "1.0.0", "main": "index.tsx", "packageManager": "yarn@1.22.19", "scripts": {"prestart": "node ./scripts/fix-errors.js", "start": "npx expo start", "start:fast": "npx expo start --no-dev --minify", "start:clean": "npx expo start --clear", "start:reset": "yarn cache:clean && npx expo start --clear", "android": "npx expo run:android", "ios": "npx expo run:ios", "lint": "eslint . --ext .ts,.tsx,.js --fix", "lint:check": "eslint . --ext .ts,.tsx,.js", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "generate-modules": "node scripts/generate-native-modules.js", "cache:clean": "npx react-native-clean-project", "optimize": "node scripts/optimize-bundling.js", "analyze-bundle": "npx expo export --dump-assetmap", "bundle-size": "node scripts/analyze-bundle-size.js", "start:optimized": "yarn cache:clean && yarn start:fast", "typecheck": "tsc --noEmit", "typecheck:watch": "tsc --noEmit --watch", "prepare": "node ./scripts/fix-errors.js", "detect-ip": "node scripts/detect-ip.js", "detect-ip:auto": "node scripts/detect-ip.js --auto", "debug:nativebase": "node -e \"const { validateNativeBaseSetup } = require('./src/utils/nativeBaseValidator'); validateNativeBaseSetup();\"", "debug:deps": "yarn list react react-native native-base", "debug:bundle": "npx expo export --dump-assetmap && node scripts/analyze-bundle-size.js", "fix:metro": "rm -rf node_modules/.cache && npx expo start --clear", "fix:deps": "rm -rf node_modules yarn.lock && yarn install", "fix:nativebase": "node scripts/fix-nativebase-issues.js", "fix:react-dom": "node scripts/fix-react-dom-issue.js", "fix:socketio": "node scripts/fix-socketio-issue.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@expo/config-plugins": "~9.0.0", "@expo/metro-runtime": "~4.0.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.4.1", "@stripe/stripe-js": "^7.4.0", "@stripe/stripe-react-native": "0.38.6", "axios": "^1.7.7", "babel-preset-expo": "~12.0.0", "date-fns": "^4.1.0", "expo": "^52.0.0", "expo-asset": "~11.0.5", "expo-background-task": "~0.1.4", "expo-blur": "~14.0.3", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "~18.0.10", "expo-notifications": "~0.29.14", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-task-manager": "~12.0.6", "jwt-decode": "^4.0.0", "native-base": "^3.4.28", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "^18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-picker-select": "^9.3.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-svg-transformer": "^1.5.1", "react-native-toast-message": "^2.3.3", "socket.io-client": "^4.8.1", "uuid": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/plugin-syntax-flow": "^7.27.1", "@babel/plugin-transform-flow-strip-types": "^7.27.1", "@babel/plugin-transform-runtime": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-flow": "^7.27.1", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@emotion/babel-plugin": "^11.13.5", "@expo/metro-config": "~0.19.0", "@react-native-community/cli": "^13.4.1", "@testing-library/react-native": "^12.4.0", "@types/date-fns": "^2.5.3", "@types/jest": "^29.5.14", "@types/node": "^20.19.3", "@types/prop-types": "^15.7.15", "@types/react": "~18.3.12", "@types/react-native-linear-gradient": "^2.4.0", "@types/react-test-renderer": "^18.0.7", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-imports": "^2.0.0", "chalk": "^4.1.2", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.30.0", "eslint-plugin-react-hooks": "^4.6.2", "glob": "^10.3.10", "husky": "^9.0.11", "jest": "^29.7.0", "jest-expo": "~52.0.1", "lint-staged": "^15.2.2", "minimatch": "^10.0.3", "prettier": "^3.2.5", "react-native-clean-project": "^4.0.3", "react-test-renderer": "18.3.1", "rimraf": "^5.0.5", "tailwindcss": "^3.3.0", "typescript": "5.3.3"}, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1", "lodash.pick": "^4.4.0"}, "overrides": {"lodash.pick": "^4.4.0"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["jwt-decode", "@stripe/stripe-js", "buffer", "crypto-browserify", "socket.io-client", "uuid"], "listUnknownPackages": false}}}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}}