/**
 * Platform-specific Stripe hook implementation
 * 
 * This module serves as an entry point that dynamically resolves to the appropriate
 * platform-specific implementation at runtime.
 */
import { StripeInterface } from '../types/stripe';

// Direct import of native implementation since web support is disabled
export const useStripe: () => StripeInterface = () => {
  try {
    // Dynamically import the native implementation
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { useStripe } = require('./useStripe.native');
    return useStripe();
  } catch (error) {
    console.error('Failed to load native Stripe implementation:', error);
    // Return a fallback implementation that logs errors
    return createFallbackStripeImplementation('native');
  }
};

/**
 * Creates a fallback implementation of the Stripe interface that logs errors
 * This ensures the app doesn't crash if <PERSON><PERSON> fails to initialize
 */
function createFallbackStripeImplementation(platform: 'native'): StripeInterface {
  const errorPrefix = `Stripe ${platform} implementation unavailable:`;
  
  return {
    initPaymentSheet: async () => {
      console.error(`${errorPrefix} initPaymentSheet called but not available`);
      return { 
        error: new Error('Stripe implementation unavailable') 
      };
    },
    presentPaymentSheet: async () => {
      console.error(`${errorPrefix} presentPaymentSheet called but not available`);
      return { 
        error: new Error('Stripe implementation unavailable') 
      };
    },
    // Add other required methods from StripeInterface
  };
}