// CTRON Home - Modern Button Component
// Enhanced button with NativeBase styling and animations

import { Button as NBButton, IButtonProps, useTheme, Spinner } from 'native-base';
import React from 'react';

export interface ModernButtonProps extends IButtonProps {
  variant?: 'solid' | 'outline' | 'ghost' | 'service' | 'emergency' | 'success';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactElement;
  rightIcon?: React.ReactElement;
  children: React.ReactNode;
}

export const ModernButton: React.FC<ModernButtonProps> = ({
  variant = 'solid',
  size = 'md',
  isLoading = false,
  loadingText,
  leftIcon,
  rightIcon,
  children,
  colorScheme = 'primary',
  ...props
}) => {
  const theme = useTheme();

  // Custom variant configurations
  const getVariantProps = () => {
    switch (variant) {
      case 'service':
        return {
          bg: 'primary.500',
          borderRadius: 'xl',
          shadow: 2,
          _text: {
            color: 'white',
            fontWeight: 'bold',
          },
          _pressed: {
            bg: 'primary.600',
            shadow: 4,
          },
          _hover: {
            bg: 'primary.600',
          },
        };
      case 'emergency':
        return {
          bg: 'error.500',
          borderRadius: 'xl',
          shadow: 3,
          _text: {
            color: 'white',
            fontWeight: 'bold',
          },
          _pressed: {
            bg: 'error.600',
            shadow: 5,
          },
          _hover: {
            bg: 'error.600',
          },
        };
      case 'success':
        return {
          bg: 'success.500',
          borderRadius: 'xl',
          shadow: 2,
          _text: {
            color: 'white',
            fontWeight: 'bold',
          },
          _pressed: {
            bg: 'success.600',
            shadow: 4,
          },
          _hover: {
            bg: 'success.600',
          },
        };
      default:
        return {};
    }
  };

  const getSizeProps = () => {
    switch (size) {
      case 'xs':
        return {
          px: 2,
          py: 1,
          _text: { fontSize: 'xs' },
        };
      case 'sm':
        return {
          px: 3,
          py: 2,
          _text: { fontSize: 'sm' },
        };
      case 'md':
        return {
          px: 4,
          py: 3,
          _text: { fontSize: 'md' },
        };
      case 'lg':
        return {
          px: 6,
          py: 4,
          _text: { fontSize: 'lg' },
        };
      case 'xl':
        return {
          px: 8,
          py: 5,
          _text: { fontSize: 'xl' },
        };
      default:
        return {};
    }
  };

  return (
    <NBButton
      variant={variant === 'service' || variant === 'emergency' || variant === 'success' ? 'solid' : variant}
      colorScheme={colorScheme}
      isDisabled={isLoading || props.isDisabled}
      leftIcon={isLoading ? <Spinner size="sm" color="white" /> : leftIcon}
      rightIcon={!isLoading ? rightIcon : undefined}
      {...getVariantProps()}
      {...getSizeProps()}
      {...props}
    >
      {isLoading ? (loadingText || 'Loading...') : children}
    </NBButton>
  );
};

export default ModernButton;