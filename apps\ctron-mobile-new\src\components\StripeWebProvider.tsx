/**
 * StripeWebProvider Component
 * 
 * This component provides Stripe functionality for web platforms using the @stripe/stripe-js library.
 * It initializes the Stripe instance and makes it available through a React Context.
 */
import { loadStripe, Stripe } from '@stripe/stripe-js';
import React, { createContext, useContext, useEffect, useState } from 'react';

/**
 * Props for the StripeWebProvider component
 */
interface StripeWebProviderProps {
  children: React.ReactNode;
}

// Create a context to hold the Stripe instance
const StripeWebContext = createContext<Stripe | null>(null);

/**
 * Provider component that initializes Stripe for web and provides it through context
 */
export const StripeWebProvider: React.FC<StripeWebProviderProps> = ({ children }) => {
  const [stripe, setStripe] = useState<Stripe | null>(null);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const initializeStripe = async () => {
      try {
        // Get the Stripe key from environment variables
        const stripeKey = process.env.EXPO_PUBLIC_STRIPE_PUBLIC_KEY;
        
        if (!stripeKey) {
          const missingKeyError = new Error('No Stripe public key found in environment variables');
          console.warn(missingKeyError.message);
          setError(missingKeyError);
          return;
        }
        
        // Log only the first few characters of the key for security
        console.info('Initializing Stripe with key:', stripeKey.substring(0, 4) + '...');
        
        // Load the Stripe instance
        const stripeInstance = await loadStripe(stripeKey);
        
        if (!stripeInstance) {
          throw new Error('Failed to initialize Stripe instance');
        }
        
        setStripe(stripeInstance);
        console.info('Stripe initialized successfully');
      } catch (err) {
        const stripeError = err instanceof Error ? err : new Error('Unknown error initializing Stripe');
        console.error('Failed to initialize Stripe:', stripeError);
        setError(stripeError);
      }
    };
    
    initializeStripe();
  }, []);

  // Provide both the stripe instance and any initialization error
  const contextValue = stripe;

  // If there's an error initializing Stripe, log it but still render children
  // This allows the app to function even if Stripe isn't available
  if (error) {
    console.warn('Stripe initialization error:', error.message);
  }

  return (
    <StripeWebContext.Provider value={contextValue}>
      {children}
    </StripeWebContext.Provider>
  );
};

/**
 * Hook to access the Stripe instance from the StripeWebContext
 * @returns The Stripe instance or null if not initialized
 */
export const useStripeWeb = (): Stripe | null => {
  const context = useContext(StripeWebContext);
  
  if (context === undefined) {
    console.error('useStripeWeb was called outside of a StripeWebProvider');
    return null;
  }
  
  return context;
};