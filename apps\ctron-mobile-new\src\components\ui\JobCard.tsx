// CTRON Home Design System - Job Card Component
// Specialized card for displaying job information

import React from 'react';

import { useTheme } from '../../context/ThemeContext';
import { View, Text, TouchableOpacity, StyleSheet } from '../../utils/platformUtils';

import { StatusBadge } from './StatusBadge';

// Define types locally since they're not available in React Native web
type ViewStyle = Record<string, any>;

export interface JobData {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled' | 'overdue';
  priority?: 'low' | 'medium' | 'high' | 'emergency';
  scheduledAt?: string;
  location?: string;
  technician?: {
    name: string;
    avatar?: string;
    rating?: number;
  };
  estimatedDuration?: string;
  price?: number;
}

export interface JobCardProps {
  job: JobData;
  onPress?: () => void;
  style?: ViewStyle;
  showTechnician?: boolean;
  showPrice?: boolean;
}

export const JobCard: React.FC<JobCardProps> = ({
  job,
  onPress,
  style,
  showTechnician = true,
  showPrice = true,
}) => {
  const { colors, spacing, typography, borderRadius, shadows } = useTheme();
  const getStatusBorderColor = () => {
    const statusColors = {
      pending: colors.status.pending,
      assigned: colors.status.assigned,
      active: colors.status.active,
      completed: colors.status.completed,
      cancelled: colors.status.cancelled,
      overdue: colors.status.overdue,
    };
    return statusColors[job.status];
  };

  const getPriorityStyle = () => {
    if (job.priority === 'emergency') {
      return {
        borderWidth: 2,
        borderColor: colors.secondary[500],
        ...shadows.lg,
      };
    }
    return {};
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const styles = getStyles(colors, spacing, typography, borderRadius, shadows);

  const cardStyles = [
    styles.container,
    {
      borderLeftColor: getStatusBorderColor(),
      borderLeftWidth: 4,
    },
    getPriorityStyle(),
    style,
  ];

  const CardContent = (
    <View style={cardStyles}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title} numberOfLines={1}>
            {job.title}
          </Text>
          {job.priority && (
            <View style={styles.priorityContainer}>
              <StatusBadge status={job.priority as any} variant="priority" size="sm" />
            </View>
          )}
        </View>
        <StatusBadge status={job.status} size="sm" />
      </View>

      {/* Description */}
      <Text style={styles.description} numberOfLines={2}>
        {job.description}
      </Text>

      {/* Details */}
      <View style={styles.details}>
        {job.scheduledAt && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Scheduled:</Text>
            <Text style={styles.detailValue}>{formatDate(job.scheduledAt)}</Text>
          </View>
        )}

        {job.location && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Location:</Text>
            <Text style={styles.detailValue} numberOfLines={1}>
              {job.location}
            </Text>
          </View>
        )}

        {job.estimatedDuration && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Duration:</Text>
            <Text style={styles.detailValue}>{job.estimatedDuration}</Text>
          </View>
        )}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        {showTechnician && job.technician && (
          <View style={styles.technicianInfo}>
            <Text style={styles.technicianName}>{job.technician.name}</Text>
            {job.technician.rating && (
              <Text style={styles.rating}>⭐ {job.technician.rating.toFixed(1)}</Text>
            )}
          </View>
        )}

        {showPrice && job.price && (
          <Text style={styles.price}>£{job.price.toFixed(2)}</Text>
        )}
      </View>
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.95}
        accessibilityRole="button"
        accessibilityLabel={`Job: ${job.title}, Status: ${job.status}`}
      >
        {CardContent}
      </TouchableOpacity>
    );
  }

  return CardContent;
};

const getStyles = (colors: any, spacing: any, typography: any, borderRadius: any, shadows: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.background.primary,
    borderRadius: borderRadius.lg,
    padding: spacing.xl,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border.primary,
    ...shadows.sm,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },

  titleContainer: {
    flex: 1,
    marginRight: spacing.sm,
  },

  title: {
    ...typography.text.lg,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },

  priorityContainer: {
    marginLeft: spacing.sm,
  },

  description: {
    ...typography.text.md,
    color: colors.text.secondary,
    marginBottom: spacing.md,
  },

  details: {
    marginBottom: spacing.md,
  },

  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },

  detailLabel: {
    ...typography.text.xs,
    color: colors.text.tertiary,
    marginRight: spacing.xs,
    fontWeight: '600',
  },

  detailValue: {
    ...typography.text.xs,
    color: colors.text.primary,
    flexShrink: 1,
  },

  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border.secondary,
  },

  technicianInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  technicianName: {
    ...typography.text.sm,
    color: colors.text.primary,
    marginRight: spacing.xs,
  },

  rating: {
    ...typography.text.xs,
    color: colors.text.secondary,
  },

  price: {
    ...typography.text.lg,
    color: colors.primary.main,
    fontWeight: 'bold',
  },
});

export default JobCard;

