// src/services/stripe.ts

import PaymentAPI from '../api/payment.api';
import { useStripe } from '../hooks/useStripe';
import { StripeOperationResult } from '../types/stripe';

/**
 * Hook to initialize and present <PERSON><PERSON>'s native Payment Sheet
 * @returns Methods for initializing and presenting the Stripe payment sheet
 */
export const useStripePayment = () => {
  const { initPaymentSheet, presentPaymentSheet } = useStripe();

  /**
  * Step 1: Initialize Stripe's Payment Sheet
  * This pulls a client secret from your backend.
  * @returns Result of the payment sheet initialization
  */
const initializePayment = async (): Promise<StripeOperationResult> => {
  try {
    // Get client secret from backend
    const response = await PaymentAPI.createPaymentIntent();
    
    if (!response || !response.clientSecret) {
      console.error('Invalid response from payment API:', response);
      return { success: false, error: 'Missing client secret from server' };
    }
    
    const { clientSecret } = response;
    
    // Initialize the payment sheet
    const { error } = await initPaymentSheet({
      merchantDisplayName: 'CTRON Services',
      paymentIntentClientSecret: clientSecret,
    });
    
    if (error) {
      console.error('Payment sheet initialization error:', error.message);
      return { success: false, error: error.message };
    }
    
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown payment initialization error';
    console.error('Stripe init error:', errorMessage);
    return { success: false, error: errorMessage };
  }
};

  /**
  * Step 2: Launch the Stripe sheet for payment
  * @returns Result of the payment sheet presentation
  */
const openPaymentSheet = async (): Promise<StripeOperationResult> => {
  try {
    const { error } = await presentPaymentSheet();
    
    if (error) {
      console.error('Payment sheet presentation error:', error.message);
      return { success: false, error: error.message };
    }
    
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown payment processing error';
    console.error('Stripe payment error:', errorMessage);
    return { success: false, error: errorMessage };
  }
};

  return {
    initializePayment,
    openPaymentSheet,
    // Add a method to check if Stripe is available
    isStripeAvailable: () => {
      return !!initPaymentSheet && !!presentPaymentSheet;
    }
  };
};
