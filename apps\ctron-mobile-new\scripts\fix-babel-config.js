/**
 * This script checks and fixes the babel.config.js file
 * to ensure it's properly formatted and doesn't contain syntax errors.
 */

const fs = require('fs');
const path = require('path');

const chalk = require('chalk');

console.log(chalk.blue('Checking and fixing babel.config.js...'));

// Path to the babel.config.js file
const babelConfigPath = path.join(__dirname, '..', 'babel.config.js');

// Check if the file exists
if (!fs.existsSync(babelConfigPath)) {
  console.log(chalk.red(`babel.config.js not found at ${babelConfigPath}`));
  process.exit(1);
}

// Read the current content
let content = fs.readFileSync(babelConfigPath, 'utf8');

// Check for common issues
const hasTransformImportsOutsidePlugins = content.includes("['babel-plugin-transform-imports'") && 
                                        !content.includes("plugins: [\n") && 
                                        content.includes("// Add platform-aware import transformation");

if (hasTransformImportsOutsidePlugins) {
  console.log(chalk.yellow('Found babel-plugin-transform-imports outside plugins array, fixing...'));
  
  // Fix the content by moving the transform-imports plugin inside the plugins array
  content = content.replace(
    /\s+\],\s+\/\/\s+Add platform-aware import transformation\s+\['babel-plugin-transform-imports',\s+\{[^\}]+\}\s+\]\,/g,
    `,
      // Add platform-aware import transformation
      ['babel-plugin-transform-imports', {
        '^react-native$': {
          transform: 'react-native-web',
          preventFullImport: true,
        }
      }],
    ],`
  );
  
  // Write the fixed content back to the file
  fs.writeFileSync(babelConfigPath, content);
  console.log(chalk.green('Fixed babel-plugin-transform-imports placement.'));
} else {
  console.log(chalk.green('babel.config.js structure looks correct.'));
}

// Check for any BOM (Byte Order Mark) issues
const buffer = fs.readFileSync(babelConfigPath);
if (buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
  console.log(chalk.yellow('BOM detected in babel.config.js, removing...'));
  const contentWithoutBOM = buffer.slice(3);
  fs.writeFileSync(babelConfigPath, contentWithoutBOM);
  console.log(chalk.green('BOM removed from babel.config.js.'));
}

console.log(chalk.blue('babel.config.js check and fix completed.'));