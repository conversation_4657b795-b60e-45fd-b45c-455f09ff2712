// CTRON Home Design System - Typography Component
// Standardized text component with consistent styling

import React from 'react';

import { Text, TextStyle } from '../../utils/platformUtils';
import { tokens } from '../tokens';

export interface TypographyProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'overline';
  color?: string;
  align?: 'left' | 'center' | 'right' | 'justify';
  weight?: keyof typeof tokens.typography.fontWeight;
  style?: TextStyle;
  numberOfLines?: number;
}

export const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body1',
  color = tokens.colors.neutral[900],
  align = 'left',
  weight,
  style,
  numberOfLines,
}) => {
  const getVariantStyles = (): TextStyle => {
    switch (variant) {
      case 'h1':
        return {
          fontSize: tokens.typography.fontSize['5xl'],
          fontWeight: tokens.typography.fontWeight.bold,
          lineHeight: tokens.typography.lineHeight.tight,
        };
      
      case 'h2':
        return {
          fontSize: tokens.typography.fontSize['4xl'],
          fontWeight: tokens.typography.fontWeight.bold,
          lineHeight: tokens.typography.lineHeight.tight,
        };
      
      case 'h3':
        return {
          fontSize: tokens.typography.fontSize['3xl'],
          fontWeight: tokens.typography.fontWeight.semibold,
          lineHeight: tokens.typography.lineHeight.tight,
        };
      
      case 'h4':
        return {
          fontSize: tokens.typography.fontSize['2xl'],
          fontWeight: tokens.typography.fontWeight.semibold,
          lineHeight: tokens.typography.lineHeight.normal,
        };
      
      case 'h5':
        return {
          fontSize: tokens.typography.fontSize.xl,
          fontWeight: tokens.typography.fontWeight.medium,
          lineHeight: tokens.typography.lineHeight.normal,
        };
      
      case 'h6':
        return {
          fontSize: tokens.typography.fontSize.lg,
          fontWeight: tokens.typography.fontWeight.medium,
          lineHeight: tokens.typography.lineHeight.normal,
        };
      
      case 'body1':
        return {
          fontSize: tokens.typography.fontSize.base,
          fontWeight: tokens.typography.fontWeight.normal,
          lineHeight: tokens.typography.lineHeight.normal,
        };
      
      case 'body2':
        return {
          fontSize: tokens.typography.fontSize.sm,
          fontWeight: tokens.typography.fontWeight.normal,
          lineHeight: tokens.typography.lineHeight.normal,
        };
      
      case 'caption':
        return {
          fontSize: tokens.typography.fontSize.xs,
          fontWeight: tokens.typography.fontWeight.normal,
          lineHeight: tokens.typography.lineHeight.normal,
        };
      
      case 'overline':
        return {
          fontSize: tokens.typography.fontSize.xs,
          fontWeight: tokens.typography.fontWeight.medium,
          lineHeight: tokens.typography.lineHeight.normal,
          textTransform: 'uppercase',
          letterSpacing: 1,
        };
      
      default:
        return {};
    }
  };

  const textStyle: TextStyle = {
    fontFamily: tokens.typography.fontFamily.primary,
    color,
    textAlign: align,
    ...getVariantStyles(),
    ...(weight && { fontWeight: tokens.typography.fontWeight[weight] }),
    ...style,
  };

  return (
    <Text style={textStyle} numberOfLines={numberOfLines}>
      {children}
    </Text>
  );
};

// Preset typography components for common use cases
export const Heading: React.FC<Omit<TypographyProps, 'variant'> & { level?: 1 | 2 | 3 | 4 | 5 | 6 }> = ({ level = 1, ...props }) => (
  <Typography variant={`h${level}` as any} {...props} />
);

export const Body: React.FC<Omit<TypographyProps, 'variant'> & { size?: 1 | 2 }> = ({ size = 1, ...props }) => (
  <Typography variant={`body${size}` as any} {...props} />
);

export const Caption: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="caption" {...props} />
);

export const Overline: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="overline" {...props} />
);