// CTRON Home - API Service
// Centralized API service for mobile app

import axios, { AxiosInstance, AxiosResponse, AxiosRequestConfig } from 'axios';

import { API_BASE_URL, REQUEST_TIMEOUT } from '../config/api.config';
import { getAuthToken } from '../utils/auth.utils';
import { debugLogger } from '../utils/debugLogger';
import { performanceMonitor } from '../utils/performanceMonitor';

// Type definitions for API service
interface RequestMetadata {
  requestId: string;
  networkTracker: {
    end: (status: number, responseSize?: number, error?: string) => void;
  };
}

interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  metadata?: RequestMetadata;
}

interface APIError extends Error {
  status?: number;
  response?: {
    status: number;
    data?: {
      message?: string;
    };
  };
  config?: AxiosRequestConfig;
}

// API Response types
interface DashboardData {
  stats: Record<string, number>;
  recentJobs: unknown[];
  revenue: number;
}

interface JobData {
  id: string;
  title: string;
  description: string;
  status: string;
  createdAt: string;
}

interface TechnicianData {
  id: string;
  name: string;
  specialization: string;
  rating: number;
  isAvailable: boolean;
}

interface UserProfile {
  id: string;
  email: string;
  fullName: string;
  role: string;
}

interface NotificationData {
  id: string;
  title: string;
  body: string;
  isRead: boolean;
  createdAt: string;
}

interface PaymentData {
  id: string;
  amount: number;
  status: string;
  jobId: string;
}

class APIService {
  private static instance: APIService;
  private client: AxiosInstance;

  private constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  static getInstance(): APIService {
    if (!APIService.instance) {
      APIService.instance = new APIService();
    }
    return APIService.instance;
  }

  private setupInterceptors(): void {
    // Request interceptor to add auth token and start performance tracking
    this.client.interceptors.request.use(
      async (config) => {
        const token = await getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Start performance tracking for this request
        const requestId = `${config.method?.toUpperCase()}_${config.url}_${Date.now()}`;
        const extendedConfig = config as ExtendedAxiosRequestConfig;
        extendedConfig.metadata = {
          requestId,
          networkTracker: performanceMonitor.trackNetworkRequest(
            config.url || 'unknown',
            config.method?.toUpperCase() || 'GET'
          )
        };

        return config;
      },
      (error) => {
        debugLogger.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling and performance tracking
    this.client.interceptors.response.use(
      (response) => {
        // Complete performance tracking for successful requests
        const extendedConfig = response.config as ExtendedAxiosRequestConfig;
        const networkTracker = extendedConfig?.metadata?.networkTracker;
        if (networkTracker) {
          const responseSize = JSON.stringify(response.data).length;
          networkTracker.end(response.status, responseSize);
        }
        return response;
      },
      (error: APIError) => {
        // Complete performance tracking for failed requests
        const extendedConfig = error.config as ExtendedAxiosRequestConfig;
        const networkTracker = extendedConfig?.metadata?.networkTracker;
        if (networkTracker) {
          networkTracker.end(
            error.response?.status || 0,
            undefined,
            error.message
          );
        }

        if (__DEV__) {
          debugLogger.error('API Error:', {
            url: error.config?.url,
            method: error.config?.method,
            status: error.response?.status,
            message: error.message,
            data: error.response?.data,
          });
        }

        // Transform error for consistent handling
        const transformedError: APIError = {
          ...error,
          message: error.response?.data?.message || error.message || 'Network error occurred',
          status: error.response?.status,
        };

        return Promise.reject(transformedError);
      }
    );
  }

  // Generic HTTP methods
  async get<T = unknown>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get(url, config);
  }

  async post<T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post(url, data, config);
  }

  async put<T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put(url, data, config);
  }

  async patch<T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.patch(url, data, config);
  }

  async delete<T = unknown>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete(url, config);
  }

  // Admin API methods
  async getAdminDashboard(): Promise<AxiosResponse<DashboardData>> {
    return this.get<DashboardData>('/api/admin/dashboard');
  }

  async getAdminJobs(params?: Record<string, unknown>): Promise<AxiosResponse<JobData[]>> {
    return this.get<JobData[]>('/api/admin/jobs', { params });
  }

  async getAdminJobDetails(jobId: string): Promise<AxiosResponse<JobData>> {
    return this.get<JobData>(`/api/admin/jobs/${jobId}`);
  }

  async updateAdminJob(jobId: string, data: Partial<JobData>): Promise<AxiosResponse<JobData>> {
    return this.put<JobData>(`/api/admin/jobs/${jobId}`, data);
  }

  async getAdminTechnicians(params?: Record<string, unknown>): Promise<AxiosResponse<TechnicianData[]>> {
    return this.get<TechnicianData[]>('/api/admin/technicians', { params });
  }

  async updateAdminTechnician(technicianId: string, data: Partial<TechnicianData>): Promise<AxiosResponse<TechnicianData>> {
    return this.put<TechnicianData>(`/api/admin/technicians/${technicianId}`, data);
  }

  async getAdminSettings(): Promise<AxiosResponse<Record<string, unknown>>> {
    return this.get<Record<string, unknown>>('/api/admin/settings');
  }

  async updateAdminSettings(data: Record<string, unknown>): Promise<AxiosResponse<Record<string, unknown>>> {
    return this.put<Record<string, unknown>>('/api/admin/settings', data);
  }

  // Notification API methods
  async getNotifications(params?: Record<string, unknown>): Promise<AxiosResponse<NotificationData[]>> {
    return this.get<NotificationData[]>('/api/notifications', { params });
  }

  async markNotificationRead(notificationId: string): Promise<AxiosResponse<NotificationData>> {
    return this.patch<NotificationData>(`/api/notifications/${notificationId}/read`);
  }

  async markAllNotificationsRead(): Promise<AxiosResponse<{ updated: number }>> {
    return this.patch<{ updated: number }>('/api/notifications/read-all');
  }

  async deleteNotification(notificationId: string): Promise<AxiosResponse<{ success: boolean }>> {
    return this.delete<{ success: boolean }>(`/api/notifications/${notificationId}`);
  }

  // Job API methods
  async getJobs(params?: Record<string, unknown>): Promise<AxiosResponse<JobData[]>> {
    return this.get<JobData[]>('/api/jobs', { params });
  }

  async getJobDetails(jobId: string): Promise<AxiosResponse<JobData>> {
    return this.get<JobData>(`/api/jobs/${jobId}`);
  }

  async createJob(data: Partial<JobData>): Promise<AxiosResponse<JobData>> {
    return this.post<JobData>('/api/jobs', data);
  }

  async updateJob(jobId: string, data: Partial<JobData>): Promise<AxiosResponse<JobData>> {
    return this.put<JobData>(`/api/jobs/${jobId}`, data);
  }

  async deleteJob(jobId: string): Promise<AxiosResponse<{ success: boolean }>> {
    return this.delete<{ success: boolean }>(`/api/jobs/${jobId}`);
  }

  // User API methods
  async getUserProfile(): Promise<AxiosResponse<UserProfile>> {
    return this.get<UserProfile>('/api/users/profile');
  }

  async updateUserProfile(data: Partial<UserProfile>): Promise<AxiosResponse<UserProfile>> {
    return this.put<UserProfile>('/api/users/profile', data);
  }

  // Technician API methods
  async getTechnicians(params?: Record<string, unknown>): Promise<AxiosResponse<TechnicianData[]>> {
    return this.get<TechnicianData[]>('/api/technicians', { params });
  }

  async getTechnicianDetails(technicianId: string): Promise<AxiosResponse<TechnicianData>> {
    return this.get<TechnicianData>(`/api/technicians/${technicianId}`);
  }

  // Payment API methods
  async getPayments(params?: Record<string, unknown>): Promise<AxiosResponse<PaymentData[]>> {
    return this.get<PaymentData[]>('/api/payments', { params });
  }

  async createPayment(data: Partial<PaymentData>): Promise<AxiosResponse<PaymentData>> {
    return this.post<PaymentData>('/api/payments', data);
  }

  // Health check
  async healthCheck(): Promise<AxiosResponse<{ status: string; message: string }>> {
    return this.get<{ status: string; message: string }>('/api/health');
  }
}

// Export singleton instance
const api = APIService.getInstance();
export default api;

// Export class for testing
export { APIService };
