// CTRON Home Design System - BlurCard Component
// Standardized blur effect card with consistent styling

import { BlurView } from 'expo-blur';
import React from 'react';

import { View, ViewStyle } from '../../utils/platformUtils';
import { tokens } from '../tokens';

export interface BlurCardProps {
  children: React.ReactNode;
  intensity?: number;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  variant?: 'light' | 'dark' | 'extraLight';
  borderRadius?: keyof typeof tokens.borderRadius;
  shadow?: keyof typeof tokens.shadows;
  padding?: keyof typeof tokens.spacing;
}

export const BlurCard: React.FC<BlurCardProps> = ({
  children,
  intensity = 15,
  style,
  contentStyle,
  variant = 'light',
  borderRadius = 'lg',
  shadow = 'md',
  padding = 4,
}) => {
  const containerStyle: ViewStyle = {
    borderRadius: tokens.borderRadius[borderRadius],
    overflow: 'hidden',
    ...tokens.shadows[shadow],
    ...style,
  };

  const blurContainerStyle: ViewStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  };

  const contentContainerStyle: ViewStyle = {
    padding: tokens.spacing[padding],
    ...contentStyle,
  };

  return (
    <View style={containerStyle}>
      {/* Blur Background */}
      <View style={blurContainerStyle}>
        <BlurView 
          intensity={intensity} 
          tint={variant}
 
        />
      </View>
      
      {/* Content */}
      <View style={contentContainerStyle}>
        {children}
      </View>
    </View>
  );
};

// Preset variants for common use cases
export const HeroBlurCard: React.FC<Omit<BlurCardProps, 'intensity' | 'shadow'>> = (props) => (
  <BlurCard intensity={20} shadow="lg" {...props} />
);

export const ContentBlurCard: React.FC<Omit<BlurCardProps, 'intensity'>> = (props) => (
  <BlurCard intensity={15} {...props} />
);

export const SubtleBlurCard: React.FC<Omit<BlurCardProps, 'intensity' | 'shadow'>> = (props) => (
  <BlurCard intensity={10} shadow="sm" {...props} />
);