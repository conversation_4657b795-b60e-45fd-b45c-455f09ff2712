// CTRON Home - Modern Home Screen with NativeBase
// Migrated version using NativeBase components for better UX

import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Box,
  VStack,
  HStack,
  Text,
  ScrollView,
  Button,
  Avatar,
  Badge,
  Icon,
  Pressable,
  Skeleton,
  RefreshControl,
  useTheme,
} from 'native-base';
import React, { useState, useEffect, useCallback } from 'react';

import { ServiceCard, JobStatusCard } from '../../components/modern';
import { useAuth } from '../../context/AuthContext';
import { useJobs } from '../../context/JobContext';
import type { HomeownerStackParamList } from '../../navigation/types';
import { StatusBar } from '../../utils/platformUtils';

type HomeScreenNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'Home'>;

const ModernHomeScreenNB: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { user } = useAuth();
  const { myJobs = [], refreshJobs, loading } = useJobs();
  const [refreshing, setRefreshing] = useState(false);
  const theme = useTheme();

  const firstName = user?.fullName?.split(' ')[0] || 'User';

  // Filter jobs
  const activeJobs = myJobs.filter(
    job => job.status === 'PENDING' || job.status === 'IN_PROGRESS' || job.status === 'ACCEPTED'
  );
  const recentJobs = myJobs.slice(0, 3);

  // Mock services data (replace with actual API call)
  const popularServices = [
    {
      id: '1',
      title: 'Emergency Plumbing',
      description: 'Quick fixes for leaks, clogs, and pipe issues',
      category: 'Plumbing',
      price: 85,
      status: 'available' as const,
      rating: 4.8,
      reviewCount: 127,
      estimatedTime: '1-2 hours',
      technician: {
        id: '1',
        name: 'Mike Johnson',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        specialization: 'Licensed Plumber',
        isVerified: true,
      },
    },
    {
      id: '2',
      title: 'Electrical Repair',
      description: 'Safe electrical work by certified electricians',
      category: 'Electrical',
      price: 95,
      status: 'available' as const,
      rating: 4.9,
      reviewCount: 89,
      estimatedTime: '2-3 hours',
      technician: {
        id: '2',
        name: 'Sarah Wilson',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        specialization: 'Certified Electrician',
        isVerified: true,
      },
    },
  ];

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refreshJobs();
    setRefreshing(false);
  }, [refreshJobs]);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const QuickActionButton = ({ 
    icon, 
    title, 
    subtitle, 
    onPress, 
    colorScheme = 'primary' 
  }: {
    icon: string;
    title: string;
    subtitle: string;
    onPress: () => void;
    colorScheme?: string;
  }) => (
    <Pressable flex={1} onPress={onPress}>
      <Box
        bg="white"
        rounded="xl"
        shadow={2}
        p={4}
        borderWidth={1}
        borderColor="gray.100"
        _pressed={{
          bg: `${colorScheme}.50`,
          shadow: 3,
        }}
      >
        <VStack space={2} alignItems="center">
          <Box
            bg={`${colorScheme}.100`}
            rounded="full"
            p={3}
          >
            <Icon
              as={MaterialIcons}
              name={icon}
              size="lg"
              color={`${colorScheme}.600`}
            />
          </Box>
          <Text fontSize="md" fontWeight="semibold" textAlign="center">
            {title}
          </Text>
          <Text fontSize="xs" color="gray.500" textAlign="center">
            {subtitle}
          </Text>
        </VStack>
      </Box>
    </Pressable>
  );

  const StatCard = ({ 
    value, 
    label, 
    icon, 
    colorScheme = 'primary' 
  }: {
    value: string;
    label: string;
    icon: string;
    colorScheme?: string;
  }) => (
    <Box flex={1} bg="white" rounded="lg" shadow={1} p={3}>
      <VStack space={2} alignItems="center">
        <Icon
          as={MaterialIcons}
          name={icon}
          size="md"
          color={`${colorScheme}.500`}
        />
        <Text fontSize="xl" fontWeight="bold" color={`${colorScheme}.600`}>
          {value}
        </Text>
        <Text fontSize="sm" color="gray.500" textAlign="center">
          {label}
        </Text>
      </VStack>
    </Box>
  );

  if (loading) {
    return (
      <Box flex={1} bg="gray.50" safeArea>
        <VStack space={4} p={4}>
          <Skeleton h="20" rounded="xl" />
          <HStack space={3}>
            <Skeleton flex={1} h="24" rounded="xl" />
            <Skeleton flex={1} h="24" rounded="xl" />
            <Skeleton flex={1} h="24" rounded="xl" />
          </HStack>
          <Skeleton h="32" rounded="xl" />
          <Skeleton h="32" rounded="xl" />
        </VStack>
      </Box>
    );
  }

  return (
    <Box flex={1} bg="gray.50" safeArea>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <VStack space={6} p={4}>
          {/* Header */}
          <HStack justifyContent="space-between" alignItems="center" pt={2}>
            <VStack flex={1}>
              <Text fontSize="lg" color="gray.600">
                {getGreeting()},
              </Text>
              <Text fontSize="2xl" fontWeight="bold" color="gray.800">
                {firstName} 👋
              </Text>
              <HStack alignItems="center" space={2} mt={1}>
                <Box w={2} h={2} bg="success.500" rounded="full" />
                <Text fontSize="sm" color="gray.500">
                  Online
                </Text>
              </HStack>
            </VStack>
            <Pressable onPress={() => navigation.navigate('Profile')}>
              <Avatar
                bg="primary.500"
                size="lg"
                source={user?.avatar ? { uri: user.avatar } : undefined}
              >
                {firstName.charAt(0)}
              </Avatar>
            </Pressable>
          </HStack>

          {/* Quick Actions */}
          <VStack space={4}>
            <Text fontSize="xl" fontWeight="bold" color="gray.800">
              Quick Actions
            </Text>
            <HStack space={3}>
              <QuickActionButton
                icon="add-circle"
                title="Book Service"
                subtitle="Find a technician"
                onPress={() => navigation.navigate('BookJob')}
                colorScheme="primary"
              />
              <QuickActionButton
                icon="chat"
                title="Messages"
                subtitle="Chat with techs"
                onPress={() => navigation.navigate('ChatList')}
                colorScheme="success"
              />
              <QuickActionButton
                icon="payment"
                title="Payments"
                subtitle="Manage billing"
                onPress={() => navigation.navigate('MyJobs')}
                colorScheme="warning"
              />
            </HStack>
          </VStack>

          {/* Stats Overview */}
          <VStack space={4}>
            <Text fontSize="xl" fontWeight="bold" color="gray.800">
              Overview
            </Text>
            <HStack space={3}>
              <StatCard
                value={activeJobs.length.toString()}
                label="Active Jobs"
                icon="build"
                colorScheme="primary"
              />
              <StatCard
                value={myJobs.filter(job => job.status === 'COMPLETED').length.toString()}
                label="Completed"
                icon="check-circle"
                colorScheme="success"
              />
              <StatCard
                value="4.8"
                label="Rating"
                icon="star"
                colorScheme="warning"
              />
            </HStack>
          </VStack>

          {/* Active Jobs */}
          {activeJobs.length > 0 && (
            <VStack space={4}>
              <HStack justifyContent="space-between" alignItems="center">
                <Text fontSize="xl" fontWeight="bold" color="gray.800">
                  Active Jobs
                </Text>
                <Button
                  variant="ghost"
                  size="sm"
                  onPress={() => navigation.navigate('MyJobs')}
                >
                  View All
                </Button>
              </HStack>
              {activeJobs.slice(0, 2).map((job) => (
                <JobStatusCard
                  key={job.id}
                  job={{
                    id: job.id,
                    title: job.title || job.issue,
                    description: job.description || job.issue,
                    status: job.status.toLowerCase() as any,
                    priority: 'medium',
                    scheduledDate: job.scheduledAt || job.createdAt,
                    estimatedDuration: '2-3 hours',
                    address: job.address || 'Address not specified',
                    price: 85,
                    technician: job.technician ? {
                      id: job.technician.id,
                      name: job.technician.user.fullName,
                      phone: '+44 7700 900123',
                      rating: job.technician.rating || 4.5,
                    } : undefined,
                  }}
                  onPress={() => navigation.navigate('JobDetails', { jobId: job.id })}
                  onContactPress={() => {
                    // Handle contact technician
                  }}
                  onTrackPress={() => navigation.navigate('JobDetails', { jobId: job.id })}
                  variant="compact"
                />
              ))}
            </VStack>
          )}

          {/* Popular Services */}
          <VStack space={4}>
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontSize="xl" fontWeight="bold" color="gray.800">
                Popular Services
              </Text>
              <Button
                variant="ghost"
                size="sm"
                onPress={() => navigation.navigate('BookJob')}
              >
                View All
              </Button>
            </HStack>
            {popularServices.map((service) => (
              <ServiceCard
                key={service.id}
                service={service}
                onPress={() => {
                  // Handle service details
                }}
                onBookPress={() => navigation.navigate('BookJob')}
                variant="default"
              />
            ))}
          </VStack>

          {/* Empty State for New Users */}
          {myJobs.length === 0 && (
            <Box bg="white" rounded="xl" shadow={1} p={6} alignItems="center">
              <Icon
                as={MaterialIcons}
                name="home-repair-service"
                size="4xl"
                color="gray.300"
                mb={4}
              />
              <Text fontSize="lg" fontWeight="semibold" color="gray.700" mb={2}>
                Welcome to CTRON Home!
              </Text>
              <Text fontSize="md" color="gray.500" textAlign="center" mb={6}>
                Book your first service to get started with professional home maintenance.
              </Text>
              <Button
                colorScheme="primary"
                size="lg"
                onPress={() => navigation.navigate('BookJob')}
                leftIcon={<Icon as={MaterialIcons} name="add" size="sm" />}
              >
                Book Your First Service
              </Button>
            </Box>
          )}

          {/* Bottom Spacing */}
          <Box h={4} />
        </VStack>
      </ScrollView>
    </Box>
  );
};

export default ModernHomeScreenNB;