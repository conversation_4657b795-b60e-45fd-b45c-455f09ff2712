// CTRON Home - Technician Tab Navigator
// Bottom tab navigation for technician screens

import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import { View, Text, StyleSheet } from '../utils/platformUtils';
import { useTheme } from '../context/ThemeContext';
// Import screens
import ChatListScreen from '../screens/Chat/ChatListScreen';
import AvailableJobsScreen from '../screens/Technician/AvailableJobsScreen';
import TechnicianHomeScreen from '../screens/Technician/TechnicianHomeScreen';

// Icon components (you can replace these with actual icon libraries)




const Tab = createBottomTabNavigator();

export const TechnicianTabNavigator = () => {
  const { colors, spacing } = useTheme();

  const styles = StyleSheet.create({
    tabBar: {
      backgroundColor: colors.background.primary,
      borderTopWidth: 1,
      borderTopColor: colors.border.light,
      paddingTop: spacing.sm,
      paddingBottom: spacing.sm,
      height: 80,
      elevation: 8,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: -2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 8,
    },

    tabBarItem: {
      paddingVertical: spacing.xs,
    },

    tabBarLabel: {
      fontSize: 12,
      fontWeight: '500',
      marginTop: spacing.xs,
    },

    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.xs,
    },

    iconContainerActive: {
      backgroundColor: `${colors.primary[900]}15`, // 15% opacity
    },

    icon: {
      fontSize: 20,
      color: colors.text.secondary,
    },

    iconActive: {
      color: colors.primary.main,
    },

    placeholderContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
      padding: spacing[6],
    },

    placeholderText: {
      fontSize: 24,
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },

    placeholderSubtext: {
      fontSize: 16,
      color: colors.text.secondary,
      textAlign: 'center',
    },
  });

  // Icon components with access to styles
  const HomeIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>🏠</Text>
    </View>
  );

  const JobsIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>🔧</Text>
    </View>
  );

  const MyJobsIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>📋</Text>
    </View>
  );

  const EarningsIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>💰</Text>
    </View>
  );

  const ChatIcon = ({ focused }: { focused: boolean }) => (
    <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
      <Text style={[styles.icon, focused && styles.iconActive]}>💬</Text>
    </View>
  );

  // Placeholder screens with access to styles
  const MyJobsScreen = () => (
    <View style={styles.placeholderContainer}>
      <Text style={styles.placeholderText}>My Jobs</Text>
      <Text style={styles.placeholderSubtext}>Manage your assigned jobs</Text>
    </View>
  );

  const EarningsScreen = () => (
    <View style={styles.placeholderContainer}>
      <Text style={styles.placeholderText}>Earnings</Text>
      <Text style={styles.placeholderSubtext}>Track your income and payments</Text>
    </View>
  );

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: colors.primary[900],
        tabBarInactiveTintColor: colors.text.tertiary,
        tabBarLabelStyle: styles.tabBarLabel,
        tabBarItemStyle: styles.tabBarItem,
      }}
    >
      <Tab.Screen
        name="TechnicianHome"
        component={TechnicianHomeScreen}
        options={{
          tabBarLabel: 'Dashboard',
          tabBarIcon: ({ focused }) => <HomeIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="AvailableJobs"
        component={AvailableJobsScreen}
        options={{
          tabBarLabel: 'Available',
          tabBarIcon: ({ focused }) => <MyJobsIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="MyJobs"
        component={MyJobsScreen}
        options={{
          tabBarLabel: 'My Jobs',
          tabBarIcon: ({ focused }) => <JobsIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="Earnings"
        component={EarningsScreen}
        options={{
          tabBarLabel: 'Earnings',
          tabBarIcon: ({ focused }) => <EarningsIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="Messages"
        component={ChatListScreen}
        options={{
          tabBarLabel: 'Messages',
          tabBarIcon: ({ focused }) => <ChatIcon focused={focused} />,
        }}
      />
    </Tab.Navigator>
  );
};

export default TechnicianTabNavigator;

