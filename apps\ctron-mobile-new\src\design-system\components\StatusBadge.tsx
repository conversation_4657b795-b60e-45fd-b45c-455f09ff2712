// CTRON Home Design System - StatusBadge Component
// Standardized status indicator with consistent styling

import React from 'react';

import { View, Text, ViewStyle, TextStyle } from '../../utils/platformUtils';
import { tokens } from '../tokens';

export interface StatusBadgeProps {
  status: 'PENDING' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'success' | 'warning' | 'error' | 'info';
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'filled' | 'outlined' | 'subtle';
  style?: ViewStyle;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  text,
  size = 'md',
  variant = 'filled',
  style,
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'PENDING':
        return {
          color: tokens.colors.warning[500],
          backgroundColor: tokens.colors.warning[50],
          borderColor: tokens.colors.warning[200],
          label: text || 'Pending',
        };
      
      case 'ACCEPTED':
        return {
          color: tokens.colors.info[500],
          backgroundColor: tokens.colors.info[50],
          borderColor: tokens.colors.info[200],
          label: text || 'Accepted',
        };
      
      case 'IN_PROGRESS':
        return {
          color: tokens.colors.primary[500],
          backgroundColor: tokens.colors.primary[50],
          borderColor: tokens.colors.primary[200],
          label: text || 'In Progress',
        };
      
      case 'COMPLETED':
        return {
          color: tokens.colors.success[500],
          backgroundColor: tokens.colors.success[50],
          borderColor: tokens.colors.success[200],
          label: text || 'Completed',
        };
      
      case 'CANCELLED':
        return {
          color: tokens.colors.error[500],
          backgroundColor: tokens.colors.error[50],
          borderColor: tokens.colors.error[200],
          label: text || 'Cancelled',
        };
      
      case 'success':
        return {
          color: tokens.colors.success[500],
          backgroundColor: tokens.colors.success[50],
          borderColor: tokens.colors.success[200],
          label: text || 'Success',
        };
      
      case 'warning':
        return {
          color: tokens.colors.warning[500],
          backgroundColor: tokens.colors.warning[50],
          borderColor: tokens.colors.warning[200],
          label: text || 'Warning',
        };
      
      case 'error':
        return {
          color: tokens.colors.error[500],
          backgroundColor: tokens.colors.error[50],
          borderColor: tokens.colors.error[200],
          label: text || 'Error',
        };
      
      case 'info':
        return {
          color: tokens.colors.info[500],
          backgroundColor: tokens.colors.info[50],
          borderColor: tokens.colors.info[200],
          label: text || 'Info',
        };
      
      default:
        return {
          color: tokens.colors.neutral[500],
          backgroundColor: tokens.colors.neutral[50],
          borderColor: tokens.colors.neutral[200],
          label: text || status,
        };
    }
  };

  const getSizeStyles = (): { container: ViewStyle; text: TextStyle } => {
    switch (size) {
      case 'sm':
        return {
          container: {
            paddingHorizontal: tokens.spacing[2],
            paddingVertical: tokens.spacing[1],
            borderRadius: tokens.borderRadius.sm,
          },
          text: {
            fontSize: tokens.typography.fontSize.xs,
          },
        };
      
      case 'md':
        return {
          container: {
            paddingHorizontal: tokens.spacing[3],
            paddingVertical: tokens.spacing[1],
            borderRadius: tokens.borderRadius.base,
          },
          text: {
            fontSize: tokens.typography.fontSize.sm,
          },
        };
      
      case 'lg':
        return {
          container: {
            paddingHorizontal: tokens.spacing[4],
            paddingVertical: tokens.spacing[2],
            borderRadius: tokens.borderRadius.base,
          },
          text: {
            fontSize: tokens.typography.fontSize.base,
          },
        };
      
      default:
        return {
          container: {},
          text: {},
        };
    }
  };

  const statusConfig = getStatusConfig();
  const sizeStyles = getSizeStyles();

  const getVariantStyles = (): ViewStyle => {
    switch (variant) {
      case 'filled':
        return {
          backgroundColor: statusConfig.color,
        };
      
      case 'outlined':
        return {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: statusConfig.color,
        };
      
      case 'subtle':
        return {
          backgroundColor: statusConfig.backgroundColor,
        };
      
      default:
        return {};
    }
  };

  const containerStyle: ViewStyle = {
    ...sizeStyles.container,
    ...getVariantStyles(),
    alignSelf: 'flex-start',
    ...style,
  };

  const textStyle: TextStyle = {
    ...sizeStyles.text,
    fontFamily: tokens.typography.fontFamily.primary,
    fontWeight: tokens.typography.fontWeight.medium,
    color: variant === 'filled' ? tokens.colors.neutral[0] : statusConfig.color,
    textAlign: 'center',
  };

  return (
    <View style={containerStyle}>
      <Text style={textStyle}>{statusConfig.label}</Text>
    </View>
  );
};