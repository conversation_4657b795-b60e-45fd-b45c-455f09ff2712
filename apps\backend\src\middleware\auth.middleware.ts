// apps/backend/src/middleware/auth.middleware.ts

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { env } from '../config/env';
import { AuthController } from '../controllers/auth.controller';
import { logger } from '../utils/logger';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    userId: string;
    role: 'HOMEOWNER' | 'TECHNICIAN' | 'ADMIN';
    email?: string;
    fullName?: string;
  };
}

export const authMiddleware = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const authHeader = req.headers.authorization;

  if (!authHeader?.startsWith('Bearer ')) {
    res.status(401).json({
      success: false,
      message: 'Unauthorized: No token provided'
    });
    return;
  }

  const token = authHeader.split(' ')[1];

  // Check if token is blacklisted
  if (AuthController.isTokenBlacklisted(token)) {
    res.status(401).json({
      success: false,
      message: 'Unauthorized: Token has been revoked'
    });
    return;
  }

  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as AuthenticatedRequest['user'];

    if (!decoded?.userId || !decoded?.role) {
      logger.warn('Invalid token payload', {
        hasUserId: !!decoded?.userId,
        hasRole: !!decoded?.role,
        ip: req.ip
      });

      res.status(403).json({
        success: false,
        message: 'Forbidden: Invalid token payload'
      });
      return;
    }

    req.user = { ...decoded, id: decoded.userId };
    next();
  } catch (err) {
    if (err instanceof jwt.TokenExpiredError) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized: Token expired',
        code: 'TOKEN_EXPIRED'
      });
      return;
    }

    if (err instanceof jwt.JsonWebTokenError) {
      logger.warn('Invalid JWT token', {
        error: err instanceof Error ? err.message : String(err),
        ip: req.ip
      });

      res.status(403).json({
        success: false,
        message: 'Forbidden: Invalid token',
        code: 'INVALID_TOKEN'
      });
      return;
    }

    logger.error('Auth middleware error', {
      error: err instanceof Error ? err.message : String(err),
      ip: req.ip
    });

    res.status(500).json({
      success: false,
      message: 'Internal authentication error'
    });
    return;
  }
};

// Role-based authorization middleware
export const requireRole = (allowedRoles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Unauthorized: Authentication required'
      });
      return;
    }

    if (!allowedRoles.includes(req.user.role)) {
      logger.warn('Insufficient permissions', {
        userId: req.user.userId,
        userRole: req.user.role,
        requiredRoles: allowedRoles,
        ip: req.ip
      });

      res.status(403).json({
        success: false,
        message: 'Forbidden: Insufficient permissions'
      });
      return;
    }

    next();
  };
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuth = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const authHeader = req.headers.authorization;

  if (!authHeader?.startsWith('Bearer ')) {
    // No token provided, continue without authentication
    next();
    return;
  }

  const token = authHeader.split(' ')[1];

  // Check if token is blacklisted
  if (AuthController.isTokenBlacklisted(token)) {
    // Token is blacklisted, continue without authentication
    next();
    return;
  }

  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as AuthenticatedRequest['user'];

    if (decoded?.userId && decoded?.role) {
      req.user = { ...decoded, id: decoded.userId };
    }
  } catch (err) {
    // Token is invalid, continue without authentication
    logger.debug('Optional auth failed', {
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  next();
};

// Export alias for compatibility
export const authenticateToken = authMiddleware;
