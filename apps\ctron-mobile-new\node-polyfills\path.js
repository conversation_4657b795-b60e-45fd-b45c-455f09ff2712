// Path polyfill for React Native
// This provides a minimal path implementation for Node.js modules

const path = {
  sep: '/',
  delimiter: ':',
  posix: null, // Will be set to this object
  win32: null, // Minimal win32 implementation
  
  normalize: (p) => {
    if (!p || p === '.') return '.';
    
    const parts = p.split('/');
    const result = [];
    
    for (const part of parts) {
      if (part === '' || part === '.') continue;
      if (part === '..') {
        if (result.length > 0 && result[result.length - 1] !== '..') {
          result.pop();
        } else {
          result.push('..');
        }
      } else {
        result.push(part);
      }
    }
    
    const normalized = result.join('/');
    return p.startsWith('/') ? '/' + normalized : normalized || '.';
  },
  
  join: (...args) => {
    const parts = args.filter(arg => arg && typeof arg === 'string');
    if (parts.length === 0) return '.';
    
    const joined = parts.join('/');
    return path.normalize(joined);
  },
  
  resolve: (...args) => {
    let resolved = '';
    let absolute = false;
    
    for (let i = args.length - 1; i >= 0 && !absolute; i--) {
      const p = args[i];
      if (p && typeof p === 'string') {
        resolved = p + '/' + resolved;
        absolute = p.charAt(0) === '/';
      }
    }
    
    if (!absolute) {
      resolved = '/' + resolved; // Assume root for mobile
    }
    
    return path.normalize(resolved) || '/';
  },
  
  dirname: (p) => {
    if (!p) return '.';
    const normalized = path.normalize(p);
    const lastSlash = normalized.lastIndexOf('/');
    
    if (lastSlash === -1) return '.';
    if (lastSlash === 0) return '/';
    
    return normalized.slice(0, lastSlash);
  },
  
  basename: (p, ext) => {
    if (!p) return '';
    const normalized = path.normalize(p);
    const lastSlash = normalized.lastIndexOf('/');
    
    let base = lastSlash === -1 ? normalized : normalized.slice(lastSlash + 1);
    
    if (ext && base.endsWith(ext)) {
      base = base.slice(0, -ext.length);
    }
    
    return base;
  },
  
  extname: (p) => {
    if (!p) return '';
    const base = path.basename(p);
    const lastDot = base.lastIndexOf('.');
    
    if (lastDot === -1 || lastDot === 0) return '';
    return base.slice(lastDot);
  },
  
  parse: (p) => {
    const dir = path.dirname(p);
    const base = path.basename(p);
    const ext = path.extname(p);
    const name = base.slice(0, base.length - ext.length);
    
    return {
      root: p.startsWith('/') ? '/' : '',
      dir,
      base,
      ext,
      name
    };
  },
  
  format: (pathObject) => {
    const { root = '', dir = '', base = '', name = '', ext = '' } = pathObject;
    
    if (base) {
      return (dir ? dir + '/' : root) + base;
    }
    
    return (dir ? dir + '/' : root) + name + ext;
  },
  
  isAbsolute: (p) => {
    return p && p.charAt(0) === '/';
  },
  
  relative: (from, to) => {
    const fromParts = path.resolve(from).split('/').filter(Boolean);
    const toParts = path.resolve(to).split('/').filter(Boolean);
    
    let i = 0;
    while (i < fromParts.length && i < toParts.length && fromParts[i] === toParts[i]) {
      i++;
    }
    
    const upCount = fromParts.length - i;
    const relativeParts = Array(upCount).fill('..').concat(toParts.slice(i));
    
    return relativeParts.join('/') || '.';
  }
};

// Set circular references
path.posix = path;
path.win32 = {
  ...path,
  sep: '\\',
  delimiter: ';'
};

module.exports = path;