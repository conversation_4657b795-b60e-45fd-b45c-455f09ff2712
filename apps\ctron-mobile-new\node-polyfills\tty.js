// TTY polyfill for React Native
// This provides a minimal tty implementation for Node.js modules

const tty = {
  isatty: () => false,
  ReadStream: function() {
    return {
      isRaw: false,
      setRawMode: () => {},
      isTTY: false
    };
  },
  WriteStream: function() {
    return {
      columns: 80,
      rows: 24,
      isTTY: false,
      hasColors: () => false,
      getColorDepth: () => 1,
      clearLine: () => {},
      clearScreenDown: () => {},
      cursorTo: () => {},
      moveCursor: () => {}
    };
  }
};

module.exports = tty;