/**
 * Type definitions for Stripe integration
 * 
 * This file provides common type definitions for Stripe functionality
 * to ensure consistency between web and native implementations.
 */

/**
 * Common response type for Stripe operations
 */
export interface StripeOperationResult {
  success: boolean;
  error?: string;
}

/**
 * Options for initializing the Stripe Payment Sheet
 */
export interface PaymentSheetInitOptions {
  merchantDisplayName: string;
  paymentIntentClientSecret: string;
  customerId?: string;
  customerEphemeralKeySecret?: string;
  defaultBillingDetails?: {
    name?: string;
    email?: string;
    phone?: string;
    address?: {
      city?: string;
      country?: string;
      line1?: string;
      line2?: string;
      postalCode?: string;
      state?: string;
    };
  };
  allowsDelayedPaymentMethods?: boolean;
  appearance?: Record<string, unknown>; // Platform-specific appearance options
}

/**
 * Common interface for Stripe functionality across platforms
 */
export interface StripeInterface {
  initPaymentSheet: (options: PaymentSheetInitOptions) => Promise<{ error: Error | null }>;
  presentPaymentSheet: () => Promise<{ error: Error | null }>;
  // Add other methods as needed
}