/**
 * CTRON Home Mobile App - Entry Point
 * 
 * This is the entry file for the CTRON Home mobile application.
 * It sets up global error handling and registers the root component.
 */
import { registerRootComponent } from 'expo';

import App from './App';
import { Platform, isWeb, isNative } from './src/utils/platformUtils';

/**
 * Global error handling for early startup errors (web only)
 * This catches errors that occur before the React error boundaries are mounted
 */
if (isWeb) {
  // Handle synchronous errors
  window.addEventListener('error', (event: ErrorEvent) => {
    console.error(
      'Unhandled error (window.onerror):',
      event.error || event.message || event
    );
    // Uncomment to display errors to users in production
    // alert(`Fatal Error: ${String(event.error || event.message || event)}`);
  });

  // Handle asynchronous promise rejections
  window.addEventListener('unhandledrejection', (event: PromiseRejectionEvent) => {
    console.error('Unhandled promise rejection:', event.reason);
    // Uncomment to display errors to users in production
    // alert(`Fatal Error (Promise): ${String(event.reason)}`);
  });
}

/**
 * Platform-specific imports and configurations
 */
if (isNative) {
  // Import gesture handler only for native platforms
  // This is required for React Navigation gesture handling
  require('react-native-gesture-handler');
}

// Log startup information
console.info('📱 Platform detected:', Platform.OS);
console.info('🚀 Registering app component');

/**
 * Register the root component with Expo
 * This makes the App component the root of the application
 */
registerRootComponent(App);