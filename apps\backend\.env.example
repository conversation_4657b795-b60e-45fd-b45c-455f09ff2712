# ========================================
# CTRON BACKEND - ENVIRONMENT VARIABLES
# ========================================

# REQUIRED VARIABLES (Server will fail to start without these)
# =============================================================

# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Database Configuration
DATABASE_URL=postgresql://postgres:admin@localhost:5432/ctron

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-change-in-production

# OPTIONAL VARIABLES (Server will work without these but features may be limited)
# =================================================================================

# Stripe Payment Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_RETURN_URL=http://localhost:3001/payments/success

# AWS S3 Configuration (for file uploads)
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=ctron-uploads
AWS_BUCKET_NAME=ctron-uploads

# OpenAI API Configuration (for AI assistant)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# App Configuration
APP_VERSION=1.0.0

# Testing Configuration
TEST_USER_PASSWORD=TestPassword123!
TEST_EMAIL_DOMAIN=test.local
TEST_DATABASE_URL=postgresql://postgres:admin@localhost:5432/ctron_test
VERBOSE_TESTS=false
