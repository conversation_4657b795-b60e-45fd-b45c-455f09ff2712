import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useTheme } from '../context/ThemeContext';
import { colors } from '../styles/designSystem';
import { View, StyleSheet } from '../utils/platformUtils';

interface ScreenContainerProps {
  children: React.ReactNode;
}

export const ScreenContainer: React.FC<ScreenContainerProps> = ({ children }) => {
  const { spacing } = useTheme();
  return (
    <View style={styles.safe}>
      <View style={[styles.inner, { padding: spacing.md }]}>{children}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  safe: { flex: 1, backgroundColor: colors.white },
  inner: { flex: 1 },
});

