// CTRON Home - AI Assistant API Service
// API calls for AI assistant functionality

import api from '../services/api';

export interface AIQueryRequest {
  query: string;
  context?: string;
  userId?: string;
}

export interface AIQueryResponse {
  response: string;
  confidence: number;
  sources?: string[];
  suggestions?: string[];
  timestamp: string;
}

export interface QueryTemplate {
  id: string;
  text: string;
  category: string;
  description?: string;
}

export const aiAPI = {
  // Process AI query
  processQuery: async (request: AIQueryRequest): Promise<{ data: AIQueryResponse }> => {
    const response = await api.post('/api/ai/query', request);
    return response;
  },

  // Get query templates
  getTemplates: async (): Promise<{ data: { templates: string[] } }> => {
    const response = await api.get('/api/ai/templates');
    return response;
  },

  // Get query history
  getQueryHistory: async (limit = 20): Promise<{
    data: {
      queries: Array<{
        id: string;
        query: string;
        response: string;
        timestamp: string;
        confidence: number;
      }>;
    };
  }> => {
    const response = await api.get(`/api/ai/history?limit=${limit}`);
    return response;
  },

  // Save query feedback
  saveQueryFeedback: async (queryId: string, feedback: 'positive' | 'negative', comment?: string): Promise<{ data: { success: boolean } }> => {
    const response = await api.post(`/api/ai/feedback/${queryId}`, {
      feedback,
      comment,
    });
    return response;
  },

  // Get AI analytics
  getAIAnalytics: async (): Promise<{
    data: {
      totalQueries: number;
      averageConfidence: number;
      topCategories: Array<{
        category: string;
        count: number;
      }>;
      satisfactionRate: number;
    };
  }> => {
    const response = await api.get('/api/ai/analytics');
    return response;
  },
};