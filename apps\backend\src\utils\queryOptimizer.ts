// backend/src/utils/queryOptimizer.ts
import { logger } from './logger';

interface QueryMetrics {
  query: string;
  duration: number;
  timestamp: Date;
  userId?: string;
  endpoint?: string;
}

class QueryOptimizer {
  private queryMetrics: QueryMetrics[] = [];
  private readonly MAX_METRICS = 1000; // Keep last 1000 queries
  private readonly SLOW_QUERY_THRESHOLD = 1000; // 1 second

  /**
   * Wrap a database query with performance monitoring
   */
  async monitorQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    context?: { userId?: string; endpoint?: string }
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;
      
      // Log slow queries
      if (duration > this.SLOW_QUERY_THRESHOLD) {
        logger.warn(`🐌 Slow query detected: ${queryName} took ${duration}ms`, {
          query: queryName,
          duration,
          ...context
        });
      }
      
      // Store metrics
      this.storeMetrics({
        query: queryName,
        duration,
        timestamp: new Date(),
        ...context
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`❌ Query failed: ${queryName} after ${duration}ms`, {
        query: queryName,
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
        ...context
      });
      throw error;
    }
  }

  /**
   * Store query metrics
   */
  private storeMetrics(metrics: QueryMetrics): void {
    this.queryMetrics.push(metrics);
    
    // Keep only the last MAX_METRICS entries
    if (this.queryMetrics.length > this.MAX_METRICS) {
      this.queryMetrics = this.queryMetrics.slice(-this.MAX_METRICS);
    }
  }

  /**
   * Get query performance statistics
   */
  getQueryStats(): {
    totalQueries: number;
    averageDuration: number;
    slowQueries: number;
    topSlowQueries: Array<{ query: string; avgDuration: number; count: number }>;
  } {
    if (this.queryMetrics.length === 0) {
      return {
        totalQueries: 0,
        averageDuration: 0,
        slowQueries: 0,
        topSlowQueries: []
      };
    }

    const totalQueries = this.queryMetrics.length;
    const averageDuration = this.queryMetrics.reduce((sum, m) => sum + m.duration, 0) / totalQueries;
    const slowQueries = this.queryMetrics.filter(m => m.duration > this.SLOW_QUERY_THRESHOLD).length;

    // Group by query name and calculate averages
    const queryGroups = this.queryMetrics.reduce((groups, metric) => {
      if (!groups[metric.query]) {
        groups[metric.query] = { durations: [], count: 0 };
      }
      groups[metric.query].durations.push(metric.duration);
      groups[metric.query].count++;
      return groups;
    }, {} as Record<string, { durations: number[]; count: number }>);

    const topSlowQueries = Object.entries(queryGroups)
      .map(([query, data]) => ({
        query,
        avgDuration: data.durations.reduce((sum, d) => sum + d, 0) / data.durations.length,
        count: data.count
      }))
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, 10);

    return {
      totalQueries,
      averageDuration,
      slowQueries,
      topSlowQueries
    };
  }

  /**
   * Clear stored metrics
   */
  clearMetrics(): void {
    this.queryMetrics = [];
    logger.info('🧹 Query metrics cleared');
  }

  /**
   * Get recent slow queries
   */
  getRecentSlowQueries(limit: number = 10): QueryMetrics[] {
    return this.queryMetrics
      .filter(m => m.duration > this.SLOW_QUERY_THRESHOLD)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }
}

// Export singleton instance
export const queryOptimizer = new QueryOptimizer();

/**
 * Decorator for monitoring database queries
 */
export function MonitorQuery(queryName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      return queryOptimizer.monitorQuery(
        `${target.constructor.name}.${propertyName}`,
        () => method.apply(this, args)
      );
    };

    return descriptor;
  };
}

/**
 * Common database query optimizations
 */
export const QueryOptimizations = {
  /**
   * Add pagination to prevent large result sets
   */
  addPagination: (page: number = 1, limit: number = 20) => {
    const skip = (page - 1) * limit;
    return { skip, take: Math.min(limit, 100) }; // Max 100 items per page
  },

  /**
   * Add common includes for user queries
   */
  userIncludes: {
    basic: {
      technician: {
        select: {
          id: true,
          specialization: true,
          rating: true,
          isAvailable: true
        }
      }
    },
    detailed: {
      technician: true,
      jobs: {
        take: 5,
        orderBy: { createdAt: 'desc' as const },
        include: {
          review: true,
          payment: true
        }
      }
    }
  },

  /**
   * Add common includes for job queries
   */
  jobIncludes: {
    basic: {
      user: {
        select: {
          id: true,
          fullName: true,
          email: true,
          phone: true
        }
      },
      technician: {
        select: {
          id: true,
          specialization: true,
          rating: true,
          user: {
            select: {
              fullName: true,
              phone: true
            }
          }
        }
      }
    },
    detailed: {
      user: true,
      technician: {
        include: {
          user: true
        }
      },
      review: true,
      payment: true,
      chat: {
        include: {
          messages: {
            take: 10,
            orderBy: { createdAt: 'desc' as const }
          }
        }
      }
    }
  },

  /**
   * Common where clauses
   */
  whereClause: {
    activeJobs: {
      status: {
        in: ['PENDING', 'ACCEPTED', 'IN_PROGRESS']
      }
    },
    completedJobs: {
      status: 'COMPLETED'
    },
    availableTechnicians: {
      isAvailable: true,
      kycStatus: 'APPROVED'
    }
  }
};