{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "allowJs": true, "noEmit": true, "skipLibCheck": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "baseUrl": ".", "module": "esnext", "moduleResolution": "node", "types": ["node"], "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@utils/*": ["src/utils/*"], "@hooks/*": ["src/hooks/*"], "@services/*": ["src/services/*"], "@api/*": ["src/api/*"], "@types/*": ["src/types/*"], "@navigation/*": ["src/navigation/*"], "@context/*": ["src/context/*"], "@styles/*": ["src/styles/*"], "@theme/*": ["src/theme/*"], "@config/*": ["src/config/*"], "@shims/*": ["src/shims/*"]}}, "include": ["**/*.ts", "**/*.tsx", "global.d.ts", "nativewind-env.d.ts", "src/test/**/*.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}