// CTRON Home - Input Validation Utilities
// Comprehensive validation and sanitization for user inputs

import { debugLogger } from './debugLogger';
import { VALIDATION_CONSTANTS } from '../config/constants';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: unknown;
}

export class InputValidator {
  private static instance: InputValidator;

  static getInstance(): InputValidator {
    if (!InputValidator.instance) {
      InputValidator.instance = new InputValidator();
    }
    return InputValidator.instance;
  }

  /**
   * Validate email address
   */
  validateEmail(email: string): ValidationResult {
    const errors: string[] = [];
    const trimmedEmail = email.trim().toLowerCase();

    if (!trimmedEmail) {
      errors.push('Email is required');
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(trimmedEmail)) {
        errors.push('Please enter a valid email address');
      }
      if (trimmedEmail.length > 254) {
        errors.push('Email address is too long');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: trimmedEmail,
    };
  }

  /**
   * Validate password
   */
  validatePassword(password: string): ValidationResult {
    const errors: string[] = [];

    if (!password) {
      errors.push('Password is required');
    } else {
      if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
      }
      if (password.length > 128) {
        errors.push('Password is too long');
      }
      if (!/(?=.*[a-z])/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
      }
      if (!/(?=.*[A-Z])/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
      }
      if (!/(?=.*\d)/.test(password)) {
        errors.push('Password must contain at least one number');
      }
      if (!/(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?])/.test(password)) {
        errors.push('Password must contain at least one special character');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: password, // Don't sanitize passwords
    };
  }

  /**
   * Validate phone number
   */
  validatePhone(phone: string): ValidationResult {
    const errors: string[] = [];
    const sanitizedPhone = phone.replace(/\D/g, ''); // Remove non-digits

    if (!phone.trim()) {
      errors.push('Phone number is required');
    } else {
      if (sanitizedPhone.length < 10 || sanitizedPhone.length > 15) {
        errors.push('Please enter a valid phone number');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: sanitizedPhone,
    };
  }

  /**
   * Validate name (first name, last name, etc.)
   */
  validateName(name: string, fieldName: string = 'Name'): ValidationResult {
    const errors: string[] = [];
    const sanitizedName = this.sanitizeText(name);

    if (!sanitizedName) {
      errors.push(`${fieldName} is required`);
    } else {
      if (sanitizedName.length < 2) {
        errors.push(`${fieldName} must be at least 2 characters long`);
      }
      if (sanitizedName.length > 50) {
        errors.push(`${fieldName} is too long`);
      }
      if (!/^[a-zA-Z\s'-]+$/.test(sanitizedName)) {
        errors.push(`${fieldName} can only contain letters, spaces, hyphens, and apostrophes`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: sanitizedName,
    };
  }

  /**
   * Validate job description
   */
  validateJobDescription(description: string): ValidationResult {
    const errors: string[] = [];
    const sanitizedDescription = this.sanitizeText(description);

    if (!sanitizedDescription) {
      errors.push('Job description is required');
    } else {
      if (sanitizedDescription.length < 10) {
        errors.push('Job description must be at least 10 characters long');
      }
      if (sanitizedDescription.length > VALIDATION_CONSTANTS.MAX_DESCRIPTION_LENGTH) {
    errors.push(`Job description is too long (max ${VALIDATION_CONSTANTS.MAX_DESCRIPTION_LENGTH} characters)`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: sanitizedDescription,
    };
  }

  /**
   * Validate message content
   */
  validateMessage(message: string): ValidationResult {
    const errors: string[] = [];
    const sanitizedMessage = this.sanitizeText(message);

    if (!sanitizedMessage) {
      errors.push('Message cannot be empty');
    } else {
      if (sanitizedMessage.length > VALIDATION_CONSTANTS.MAX_MESSAGE_LENGTH) {
    errors.push(`Message is too long (max ${VALIDATION_CONSTANTS.MAX_MESSAGE_LENGTH} characters)`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: sanitizedMessage,
    };
  }

  /**
   * Validate price/amount
   */
  validatePrice(price: string | number): ValidationResult {
    const errors: string[] = [];
    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;

    if (isNaN(numericPrice)) {
      errors.push('Please enter a valid price');
    } else {
      if (numericPrice < 0) {
        errors.push('Price cannot be negative');
      }
      if (numericPrice > VALIDATION_CONSTANTS.MAX_PAYMENT_AMOUNT) {
        errors.push('Price seems too high (max £10,000)');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: numericPrice,
    };
  }

  /**
   * Sanitize text input
   */
  sanitizeText(text: string): string {
    if (!text) return '';
    
    return text
      .trim()
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .substring(0, VALIDATION_CONSTANTS.MAX_DESCRIPTION_LENGTH); // Limit length
  }

  /**
   * Sanitize HTML to prevent XSS
   */
  sanitizeHtml(html: string): string {
    if (!html) return '';
    
    return html
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Validate form data
   */
  validateForm(data: Record<string, unknown>, rules: Record<string, string[]>): ValidationResult {
    const errors: string[] = [];
    const sanitizedData: Record<string, unknown> = {};

    for (const [field, fieldRules] of Object.entries(rules)) {
      const value = data[field];
      const stringValue = typeof value === 'string' ? value : (value?.toString() || '');
      
      for (const rule of fieldRules) {
        let result: ValidationResult;
        
        switch (rule) {
          case 'email':
            result = this.validateEmail(stringValue);
            break;
          case 'password':
            result = this.validatePassword(stringValue);
            break;
          case 'phone':
            result = this.validatePhone(stringValue);
            break;
          case 'name':
            result = this.validateName(stringValue, field);
            break;
          case 'required':
            result = { 
              isValid: !!stringValue.trim(), 
              errors: stringValue.trim() ? [] : [`${field} is required`] 
            };
            break;
          default:
            result = { isValid: true, errors: [] };
        }
        
        if (!result.isValid) {
          errors.push(...result.errors);
        } else if (result.sanitizedValue !== undefined) {
          sanitizedData[field] = result.sanitizedValue;
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: sanitizedData,
    };
  }

  /**
   * Log validation errors for debugging
   */
  logValidationError(field: string, errors: string[]): void {
    if (__DEV__ && errors.length > 0) {
      debugLogger.warn(`Validation failed for ${field}:`, errors);
    }
  }
}

// Export singleton instance
export const inputValidator = InputValidator.getInstance();

// Export convenience functions
export const validateEmail = (email: string) => inputValidator.validateEmail(email);
export const validatePassword = (password: string) => inputValidator.validatePassword(password);
export const validatePhone = (phone: string) => inputValidator.validatePhone(phone);
export const validateName = (name: string, fieldName?: string) => inputValidator.validateName(name, fieldName);
export const validateJobDescription = (description: string) => inputValidator.validateJobDescription(description);
export const validateMessage = (message: string) => inputValidator.validateMessage(message);
export const validatePrice = (price: string | number) => inputValidator.validatePrice(price);
export const sanitizeText = (text: string) => inputValidator.sanitizeText(text);
export const sanitizeHtml = (html: string) => inputValidator.sanitizeHtml(html);
export const validateForm = (data: Record<string, unknown>, rules: Record<string, string[]>) => 
  inputValidator.validateForm(data, rules);
