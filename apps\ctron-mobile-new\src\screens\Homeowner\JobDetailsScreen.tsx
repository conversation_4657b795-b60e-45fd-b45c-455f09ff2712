/**
 *  CTRON Home - Enhanced Job Details Screen
 * Job details with chat integration and status management
 */

import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import React, { useCallback, useEffect, useState, useMemo } from 'react';


import { ChatAPI } from '../../api/chat.api';
import { JobAPI } from '../../api/job.api';
import { But<PERSON>, Card, Header, Screen, LoadingState, ErrorState } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import type { HomeownerStackParamList } from '../../types/navigation';
import { View, Text, StyleSheet, Alert, ScrollView } from '../../utils/platformUtils';

interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
}

interface Job {
  id: string;
  issue: string;
  status: string;
  scheduledAt: string;
  createdAt: string;
  updatedAt: string;
  technicianId?: string;
  technician?: {
    id: string;
    user: {
      fullName: string;
      phone?: string;
    };
    specialization: string;
    rating?: number;
  };
}

type JobDetailsScreenRouteProp = RouteProp<HomeownerStackParamList, 'JobDetails'>;
type JobDetailsScreenNavigationProp = StackNavigationProp<HomeownerStackParamList, 'JobDetails'>;

const JobDetailsScreen = () => {
  const { colors, typography, spacing, borderRadius, shadows } = useTheme();
  const route = useRoute<JobDetailsScreenRouteProp>();
  const navigation = useNavigation<JobDetailsScreenNavigationProp>();

  const { jobId } = route.params;

  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [chatLoading, setChatLoading] = useState(false);

  const fetchJobDetails = useCallback(async () => {
    try {
      const res = await JobAPI.getJobDetails(jobId);
      setJob(res);
    } catch (err: unknown) {
      console.error(err);
      Alert.alert('Error', 'Failed to fetch job details');
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  const handleStartChat = useCallback(async () => {
    if (!job?.technicianId) {
      Alert.alert('No Technician', 'This job has not been assigned to a technician yet.');
      return;
    }

    try {
      setChatLoading(true);
      const chatResponse = await ChatAPI.getOrCreateJobChat(jobId);
      const chat = chatResponse.chat;

      navigation.navigate('Chat', {
        chatId: chat.id,
        jobTitle: `Job #${job.id.slice(0, 8)}`,
      });
    } catch (error: unknown) {
      console.error('Chat error:', error);
      const apiError = error as ApiError;
      Alert.alert(
        'Chat Unavailable',
        apiError.response?.data?.message || 'Unable to start chat. Please try again later.'
      );
    } finally {
      setChatLoading(false);
    }
  }, [job, navigation, jobId]);

  const handleCancelJob = useCallback(async () => {
    try {
      setLoading(true);
      await JobAPI.cancelJob(jobId, 'Cancelled by homeowner');

      // Update local state
      setJob(prev => prev ? { ...prev, status: 'CANCELLED' } : null);

      Alert.alert(
        'Job Cancelled',
        'Your job has been successfully cancelled.',
        [{ text: 'OK' }]
      );
    } catch (error: unknown) {
      console.error('Cancel job error:', error);
      const apiError = error as ApiError;
      Alert.alert(
        'Error',
        apiError.response?.data?.message || 'Failed to cancel job. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return colors.warning;
      case 'ASSIGNED':
        return colors.info;
      case 'IN_PROGRESS':
        return colors.primary.main;
      case 'COMPLETED':
        return colors.success;
      case 'CANCELLED':
        return colors.error.main;
      default:
        return colors.text.secondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'Pending Assignment';
      case 'ASSIGNED':
        return 'Assigned to Technician';
      case 'IN_PROGRESS':
        return 'Work in Progress';
      case 'COMPLETED':
        return 'Completed';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status;
    }
  };

  useEffect(() => {
    fetchJobDetails();
  }, [fetchJobDetails]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    loaderContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    loadingText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginTop: spacing.md,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.md,
    },
    errorTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    errorText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      textAlign: 'center',
      marginBottom: spacing.md,
    },
    errorButton: {
      marginTop: spacing.md,
    },
    backIcon: {
      fontSize: typography.fontSize.xl,
      color: colors.text.primary,
    },
    chatIcon: {
      fontSize: typography.fontSize.xl,
      color: colors.text.primary,
    },
    content: {
      padding: spacing.md,
    },
    section: {
      marginBottom: spacing.md,
    },
    sectionTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    detailLabel: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
    },
    detailValue: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    statusContainer: {
      paddingVertical: spacing.xs,
      paddingHorizontal: spacing.sm,
      borderRadius: borderRadius.sm,
      alignSelf: 'flex-start',
    },
    statusText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    technicianCard: {
      padding: spacing.md,
      borderRadius: borderRadius.md,
      backgroundColor: colors.background.secondary,
      ...shadows.sm,
    },
    technicianName: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    technicianDetail: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.xxs,
    },
    technicianRating: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.xxs,
    },
    contactButton: {
      marginTop: spacing.sm,
    },
    chatSection: {
      marginTop: spacing.md,
    },
    chatButton: {
      marginTop: spacing.sm,
    },
    cancelButton: {
      marginTop: spacing.md,
      backgroundColor: colors.error.main,
    },
    cancelButtonText: {
      color: colors.text.inverse,
    },
    chatInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.sm,
      borderTopWidth: 1,
      borderColor: colors.border.light,
      backgroundColor: colors.background.secondary,
    },
    chatInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: colors.border.medium,
      borderRadius: borderRadius.lg,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.xs,
      marginRight: spacing.sm,
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
    },
    sendButton: {
      backgroundColor: colors.primary.main,
      borderRadius: borderRadius.lg,
      paddingVertical: spacing.xs,
      paddingHorizontal: spacing.md,
    },
    sendButtonText: {
      color: colors.text.inverse,
      fontWeight: typography.fontWeight.bold,
    },
    messageContainer: {
      flexDirection: 'row',
      marginVertical: spacing.xs,
    },
    myMessage: {
      backgroundColor: colors.semantic.successLight,
      padding: spacing.sm,
      borderRadius: borderRadius.md,
      maxWidth: '80%',
      alignSelf: 'flex-end',
    },
    otherMessage: {
      backgroundColor: colors.neutral[200],
      padding: spacing.sm,
      borderRadius: borderRadius.md,
      maxWidth: '80%',
      alignSelf: 'flex-start',
    },
    scrollView: {
      padding: spacing.md,
    },
    scrollContent: {
      padding: spacing.md,
    },
    statusCard: {
      marginBottom: spacing.md,
    },
    statusHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    statusLabel: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    statusBadge: {
      paddingVertical: spacing.xs,
      paddingHorizontal: spacing.sm,
      borderRadius: borderRadius.sm,
    },
    detailsCard: {
      marginBottom: spacing.md,
    },
    cardTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    technicianInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    technicianDetails: {
      flex: 1,
    },
    technicianSpecialization: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    technicianActions: {
      flexDirection: 'row',
      marginTop: spacing.sm,
    },
    callButton: {
      marginLeft: spacing.sm,
    },
    noTechnicianCard: {
      marginBottom: spacing.md,
    },
    noTechnicianText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      textAlign: 'center',
      marginBottom: spacing.md,
    },
    findTechnicianButton: {
      marginTop: spacing.sm,
    },
    actionsCard: {
      marginBottom: spacing.md,
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    actionButton: {
      flex: 1,
    },
  }), [colors, spacing, typography, borderRadius, shadows]);

  if (loading) {
    return (
      <Screen>
        <Header
          title="Job Details"
          leftAction={{
            icon: <Text style={styles.backIcon}>←</Text>,
            onPress: () => navigation.goBack(),
            accessibilityLabel: 'Go back',
          }}
        />
        <LoadingState message="Loading job details..." />
      </Screen>
    );
  }

  if (!job) {
    return (
      <Screen>
        <Header
          title="Job Details"
          leftAction={{
            icon: <Text style={styles.backIcon}>←</Text>,
            onPress: () => navigation.goBack(),
            accessibilityLabel: 'Go back',
          }}
        />
        <ErrorState
          title="Job Not Found"
          message="The requested job could not be found or you don't have access to it."
          actionTitle="Go Back"
          onAction={() => navigation.goBack()}
        />
      </Screen>
    );
  }

  return (
    <Screen>
      <Header
        title={`Job #${job.id.slice(0, 8)}`}
        leftAction={{
          icon: <Text style={styles.backIcon}>←</Text>,
          onPress: () => navigation.goBack(),
          accessibilityLabel: 'Go back',
        }}
        rightAction={job.technicianId ? {
          icon: <Text style={styles.chatIcon}>💬</Text>,
          onPress: handleStartChat,
          accessibilityLabel: 'Start chat',
        } : undefined}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Status Card */}
        <Card style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Text style={styles.statusLabel}>Status</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(job.status) as string }]}>
              <Text style={styles.statusText}>{getStatusText(job.status)}</Text>
            </View>
          </View>
        </Card>

        {/* Job Details Card */}
        <Card style={styles.detailsCard}>
          <Text style={styles.cardTitle}>Service Request</Text>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Issue Description:</Text>
            <Text style={styles.detailValue}>{job.issue}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Scheduled Date:</Text>
            <Text style={styles.detailValue}>
              {new Date(job.scheduledAt).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Scheduled Time:</Text>
            <Text style={styles.detailValue}>
              {new Date(job.scheduledAt).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Created:</Text>
            <Text style={styles.detailValue}>
              {new Date(job.createdAt).toLocaleDateString()}
            </Text>
          </View>
        </Card>

        {/* Technician Card */}
        {job.technician ? (
          <Card style={styles.technicianCard}>
            <Text style={styles.cardTitle}>Assigned Technician</Text>

            <View style={styles.technicianInfo}>
              <View style={styles.technicianDetails}>
                <Text style={styles.technicianName}>{job.technician.user.fullName}</Text>
                <Text style={styles.technicianSpecialization}>{job.technician.specialization}</Text>
                {job.technician.rating && (
                  <Text style={styles.technicianRating}>
                    ⭐ {job.technician.rating.toFixed(1)} rating
                  </Text>
                )}
              </View>
            </View>

            <View style={styles.technicianActions}>
              <Button
                title={chatLoading ? 'Loading...' : 'Chat with Technician'}
                onPress={handleStartChat}
                variant="primary"
                loading={chatLoading}
                disabled={chatLoading}
                style={styles.chatButton}
              />

              {job.technician.user.phone && (
                <Button
                  title="Call Technician"
                  onPress={() => {
                    // In production, use: Linking.openURL(`tel:${job.technician.user.phone}`)
                    Alert.alert('Calling', `Calling ${job.technician?.user?.phone}...`);
                  }}
                  variant="secondary"
                  style={styles.callButton}
                />
              )}
            </View>
          </Card>
        ) : (
          <Card style={styles.noTechnicianCard}>
            <Text style={styles.cardTitle}>Technician Assignment</Text>
            <Text style={styles.noTechnicianText}>
              Your service request is pending technician assignment. You&apos;ll be notified once a technician accepts your job.
            </Text>
            <Button
              title="Find Technicians"
              onPress={() => navigation.navigate('BookJob')}
              variant="secondary"
              style={styles.findTechnicianButton}
            />
          </Card>
        )}

        {/* Actions Card */}
        <Card style={styles.actionsCard}>
          <Text style={styles.cardTitle}>Actions</Text>

          <View style={styles.actionButtons}>
            <Button
              title="Edit Job"
              onPress={() => {
                if (job.status === 'PENDING') {
                  navigation.navigate('EditJob', { jobId: job.id });
                } else {
                  Alert.alert(
                    'Cannot Edit Job',
                    'Jobs can only be edited when they are in pending status.',
                    [{ text: 'OK' }]
                  );
                }
              }}
              variant="secondary"
              style={styles.actionButton}
              disabled={job.status !== 'PENDING'}
            />

            <Button
              title="Cancel Job"
              onPress={() => {
                Alert.alert(
                  'Cancel Job',
                  'Are you sure you want to cancel this job? This action cannot be undone.',
                  [
                    { text: 'No', style: 'cancel' },
                    {
                      text: 'Yes, Cancel',
                      style: 'destructive',
                      onPress: handleCancelJob
                    },
                  ]
                );
              }}
              variant="secondary"
              style={StyleSheet.flatten([styles.actionButton, styles.cancelButton])}
              disabled={job.status === 'COMPLETED' || job.status === 'CANCELLED'}
            />
          </View>
        </Card>
      </ScrollView>
    </Screen>
  );
};

export default JobDetailsScreen;

