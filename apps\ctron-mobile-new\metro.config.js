// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path');

const { getDefaultConfig } = require('expo/metro-config');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { withNativeWind } = require('nativewind/metro');
// eslint-disable-next-line @typescript-eslint/no-var-requires

const config = getDefaultConfig(__dirname, {
  isCSSEnabled: true,
  transpilePackages: [
    'react-native-css-interop',
    'nativewind',
    'react-native-reanimated',
    'react-native-gesture-handler',
    'react-native-screens',
    'react-native-safe-area-context',
    'react-native-vector-icons',
    '@react-native-async-storage/async-storage',
    'react-native-svg',
    'react-native-toast-message',
    'native-base',
    '@react-aria/utils',
    '@react-stately/utils',
    'react-native-svg',
    'socket.io-client',
    'debug'
  ],
});

// Configure SVG transformer
config.transformer.babelTransformerPath = require.resolve('react-native-svg-transformer');
config.resolver.assetExts = config.resolver.assetExts.filter(ext => ext !== 'svg');
config.resolver.sourceExts = [...config.resolver.sourceExts, 'svg'];

// Add React aliasing to ensure single instance and handle NativeBase dependencies
config.resolver.alias = {
  ...config.resolver.alias,
  'react': path.resolve(__dirname, 'node_modules/react'),
  'react-native': path.resolve(__dirname, 'node_modules/react-native'),
  // Use installed react-dom instead of shim for better compatibility
  'react-dom': path.resolve(__dirname, 'node_modules/react-dom'),
  'react-dom/client': path.resolve(__dirname, 'node_modules/react-dom/client'),
  'react-dom/server': path.resolve(__dirname, 'node_modules/react-dom/server'),
  // Node.js polyfills for socket.io-client
  'tty': path.resolve(__dirname, 'node-polyfills/tty.js'),
  'util': path.resolve(__dirname, 'node-polyfills/util.js'),
  'os': path.resolve(__dirname, 'node-polyfills/os.js'),
  'fs': path.resolve(__dirname, 'node-polyfills/fs.js'),
  'path': path.resolve(__dirname, 'node-polyfills/path.js'),
  'crypto': path.resolve(__dirname, 'node-polyfills/crypto.js')
};

// Add platform extensions for better resolution
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Handle node modules that might cause issues
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Add additional source extensions
config.resolver.sourceExts = [...config.resolver.sourceExts, 'svg', 'ts', 'tsx', 'js', 'jsx', 'json'];

// Handle problematic packages that might cause bundling issues
config.resolver.blockList = [
  // Block problematic files that might cause issues
  /.*\/node_modules\/.*\/dist\/.*\.web\.js$/,
  /.*\/node_modules\/.*\/lib\/.*\.web\.js$/,
  // Block Node.js specific debug files
  /.*\/node_modules\/debug\/src\/node\.js$/,
];

// Add custom resolver to handle react-dom imports
const originalResolver = config.resolver.resolverMainFields;
config.resolver.resolverMainFields = ['react-native', 'main', 'browser'];

// Ensure react-dom is properly resolved
config.resolver.nodeModulesPaths = [
  path.resolve(__dirname, 'node_modules'),
  path.resolve(__dirname, '../node_modules'),
];

// Apply NativeWind configuration
module.exports = withNativeWind(config, { input: './global.css' });