// CTRON Home - Modern Loading Screen with NativeBase
// Professional loading screen with CTRON branding

import {
  Box,
  VStack,
  Text,
  Spinner,
  Progress,
} from 'native-base';
import React, { useEffect, useRef } from 'react';

import { Animated } from 'react-native';

interface LoadingScreenProps {
  message?: string;
  progress?: number;
  showProgress?: boolean;
  variant?: 'default' | 'splash' | 'minimal';
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'Loading...',
  progress,
  showProgress = false,
  variant = 'default',
}) => {

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  if (variant === 'minimal') {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" bg="transparent">
        <Spinner size="lg" color="primary.500" />
      </Box>
    );
  }

  if (variant === 'splash') {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" bg="primary.500">
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          }}
        >
          <VStack space={8} alignItems="center">
            {/* CTRON Logo */}
            <Box
              w={24}
              h={24}
              bg="white"
              rounded="2xl"
              justifyContent="center"
              alignItems="center"
              shadow={4}
            >
              <Text fontSize="2xl" fontWeight="bold" color="primary.500">
                C
              </Text>
            </Box>

            <VStack space={4} alignItems="center">
              <Text fontSize="3xl" fontWeight="bold" color="white">
                CTRON Home
              </Text>
              <Text fontSize="md" color="white" opacity={0.8}>
                Professional Home Services
              </Text>
            </VStack>

            <Spinner size="lg" color="white" />
          </VStack>
        </Animated.View>
      </Box>
    );
  }

  // Default variant
  return (
    <Box flex={1} justifyContent="center" alignItems="center" bg="gray.50" px={8}>
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        }}
      >
        <VStack space={6} alignItems="center" w="full">
          {/* Loading Spinner */}
          <Box
            w={20}
            h={20}
            bg="white"
            rounded="full"
            justifyContent="center"
            alignItems="center"
            shadow={3}
          >
            <Spinner size="lg" color="primary.500" />
          </Box>

          {/* Loading Message */}
          <VStack space={2} alignItems="center">
            <Text fontSize="lg" fontWeight="semibold" color="gray.700">
              {message}
            </Text>
            <Text fontSize="sm" color="gray.500" textAlign="center">
              Please wait while we process your request
            </Text>
          </VStack>

          {/* Progress Bar */}
          {showProgress && (
            <VStack space={2} w="full">
              <Progress
                value={progress || 0}
                colorScheme="primary"
                size="md"
                rounded="full"
              />
              {progress !== undefined && (
                <Text fontSize="sm" color="gray.600" textAlign="center">
                  {Math.round(progress)}% complete
                </Text>
              )}
            </VStack>
          )}
        </VStack>
      </Animated.View>
    </Box>
  );
};

export default LoadingScreen;