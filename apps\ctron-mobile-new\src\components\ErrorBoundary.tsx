import { Component, ErrorInfo, ReactNode } from 'react';

import { View, Text, StyleSheet, TouchableOpacity } from '../utils/platformUtils';

import ErrorRecovery from './ErrorRecovery';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }



  static getDerivedStateFromError(error: any): State {
    // Ensure the error object has a string message
    const errorMessage = typeof error?.message === 'string' ? error.message : JSON.stringify(error);
    return { hasError: true, error: new Error(errorMessage) };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to monitoring service in production
    if (__DEV__) {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
    // In production, send to error tracking service like Sentry
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ErrorRecovery
          error={this.state.error}
          onRetry={this.handleRetry}
          context="ErrorBoundary"
          showDetails={__DEV__}
          customMessage="We're sorry for the inconvenience. Please try again."
        />
      );
    }

    return this.props.children;
  }
}

