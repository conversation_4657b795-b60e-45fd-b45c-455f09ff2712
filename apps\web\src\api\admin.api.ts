// CTRON Home - Web Admin API Service
// API calls for admin functionality

import api from '../services/api';

export interface DashboardMetrics {
  jobsToday: number;
  totalRevenue: number;
  onlineTechnicians: number;
  disputes: number;
  totalJobs: number;
  activeJobs: number;
  completedJobs: number;
  totalTechnicians: number;
  activeTechnicians: number;
  monthlyRevenue: number;
  averageJobValue: number;
  customerSatisfaction: number;
}

export interface RecentActivity {
  id: string;
  type: 'job_created' | 'job_completed' | 'payment_completed' | 'technician_approved' | 'user_registered';
  message: string;
  timestamp: string;
  priority: 'low' | 'medium' | 'high';
  userId?: string;
  jobId?: string;
  technicianId?: string;
}

export interface Job {
  id: string;
  issue: string;
  description?: string;
  status: string;
  createdAt: string;
  scheduledAt: string;
  completedAt?: string;
  address?: string;
  homeowner: {
    id: string;
    fullName: string;
    email: string;
    phone?: string;
  };
  technician?: {
    id: string;
    user: {
      fullName: string;
      email: string;
      phone?: string;
    };
    specialization: string;
    rating?: number;
  };
  payment?: {
    id: string;
    amount: number;
    status: string;
    isReleased: boolean;
    isFrozen: boolean;
    freezeReason?: string;
    stripePaymentIntentId?: string;
  };
  chat?: {
    id: string;
    messageCount: number;
    lastMessageAt?: string;
  };
}

export interface Technician {
  id: string;
  userId: string;
  specialization: string;
  experience: number;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  rating?: number;
  completedJobs: number;
  isAvailable: boolean;
  user: {
    id: string;
    fullName: string;
    email: string;
    phone?: string;
    profileImage?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface SystemSettings {
  gracePeriodHours: number;
  stripeTestMode: boolean;
  statusMessage: string;
  maintenanceMode: boolean;
  maxJobsPerTechnician: number;
  commissionRate: number;
  autoAssignJobs: boolean;
  notificationsEnabled: boolean;
}

export const AdminAPI = {
  // Dashboard
  getDashboardMetrics: async (): Promise<DashboardMetrics> => {
    const response = await api.get('/api/admin/dashboard/metrics');
    return response.data.metrics;
  },

  getRecentActivity: async (limit = 10): Promise<{ activities: RecentActivity[] }> => {
    const response = await api.get(`/api/admin/recent-activity?limit=${limit}`);
    return response.data;
  },

  // Jobs Management
  getJobs: async (params: {
    page?: number;
    limit?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
    technicianId?: string;
    homeownerId?: string;
  } = {}): Promise<{
    jobs: Job[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get(`/api/admin/jobs?${queryParams.toString()}`);
    return response.data;
  },

  getJobDetails: async (jobId: string): Promise<{ job: Job }> => {
    const response = await api.get(`/api/admin/jobs/${jobId}`);
    return response.data;
  },

  updateJobStatus: async (jobId: string, status: string, reason?: string): Promise<{ success: boolean }> => {
    const response = await api.put(`/api/admin/jobs/${jobId}/status`, {
      status,
      reason,
    });
    return response.data;
  },

  // Payment Management
  freezePayment: async (jobId: string, reason: string): Promise<{ success: boolean }> => {
    const response = await api.patch(`/api/payments/${jobId}/freeze`, { reason });
    return response.data;
  },

  unfreezePayment: async (jobId: string): Promise<{ success: boolean }> => {
    const response = await api.patch(`/api/payments/${jobId}/unfreeze`);
    return response.data;
  },

  releasePaymentManually: async (jobId: string): Promise<{ success: boolean }> => {
    const response = await api.patch(`/api/payments/${jobId}/release-manually`);
    return response.data;
  },

  // Technician Management
  getTechnicians: async (params: {
    page?: number;
    limit?: number;
    status?: 'PENDING' | 'APPROVED' | 'REJECTED';
  } = {}): Promise<{
    technicians: Technician[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await api.get(`/api/admin/technicians?${queryParams.toString()}`);
    return response.data;
  },

  getPendingTechnicians: async (): Promise<Technician[]> => {
    const response = await api.get('/api/technicians/pending');
    return response.data;
  },

  approveTechnician: async (technicianId: string): Promise<{ success: boolean }> => {
    const response = await api.patch(`/api/technicians/${technicianId}/approve`);
    return response.data;
  },

  rejectTechnician: async (technicianId: string, reason?: string): Promise<{ success: boolean }> => {
    const response = await api.patch(`/api/technicians/${technicianId}/reject`, { reason });
    return response.data;
  },

  updateTechnicianStatus: async (technicianId: string, status: 'APPROVED' | 'REJECTED', reason?: string): Promise<{ success: boolean }> => {
    const response = await api.put(`/api/admin/technicians/${technicianId}/status`, {
      status,
      reason,
    });
    return response.data;
  },

  // System Settings
  getSystemSettings: async (): Promise<SystemSettings> => {
    const response = await api.get('/api/admin/settings');
    return response.data.settings;
  },

  updateSystemSettings: async (settings: Partial<SystemSettings>): Promise<{ success: boolean }> => {
    const response = await api.put('/api/admin/settings', settings);
    return response.data;
  },

  // Analytics
  getRevenueAnalytics: async (period: 'week' | 'month' | 'quarter' | 'year' = 'month'): Promise<{
    totalRevenue: number;
    periodRevenue: number;
    revenueGrowth: number;
    averageJobValue: number;
    topTechnicians: Array<{
      technicianId: string;
      name: string;
      revenue: number;
      jobCount: number;
    }>;
    revenueByDay: Array<{
      date: string;
      revenue: number;
      jobCount: number;
    }>;
  }> => {
    const response = await api.get(`/api/admin/analytics/revenue?period=${period}`);
    return response.data;
  },

  getUserAnalytics: async (): Promise<{
    totalUsers: number;
    newUsersThisMonth: number;
    activeUsers: number;
    userGrowthRate: number;
    usersByRole: {
      homeowners: number;
      technicians: number;
      admins: number;
    };
    registrationsByDay: Array<{
      date: string;
      count: number;
    }>;
  }> => {
    const response = await api.get('/api/admin/analytics/users');
    return response.data;
  },

  // Export
  exportData: async (type: 'jobs' | 'users' | 'technicians' | 'payments', filters?: Record<string, any>): Promise<{ downloadUrl: string }> => {
    const queryParams = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const response = await api.post(`/api/admin/export/${type}?${queryParams.toString()}`);
    return response.data;
  },
};