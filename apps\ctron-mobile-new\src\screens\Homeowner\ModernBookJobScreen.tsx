// CTRON Home - Modern Book Job Screen v2.0
// Multi-step booking process with enhanced UI and technician selection - Updated Styling

import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { BlurView } from 'expo-blur';
import React, { useState, useMemo, useRef, useEffect } from 'react';

import type { ViewStyle } from 'react-native';

import { JobAPI, JobCreatePayload } from '../../api/job.api';
import { TechnicianAPI } from '../../api/technician.api';
import SafeLinearGradient from '../../components/SafeLinearGradient';
import { Header } from '../../components/ui/Header';
import { API_BASE_URL } from '../../config/api.config';
import { useTheme } from '../../context/ThemeContext';
import { <PERSON><PERSON>, <PERSON> } from '../../design-system';
import type { HomeownerStackParamList } from '../../navigation/types';
import { getAuthToken } from '../../utils/auth.utils';
import { 
  Alert, 
  StyleSheet, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  View, 
  ScrollView, 
  FlatList, 
  ActivityIndicator,
  Animated,
  Dimensions,
  StatusBar,
  Platform
} from '../../utils/platformUtils';


interface Technician {
  id: string;
  fullName: string;
  specialization: string;
  rating?: number;
  distance?: number;
  completedJobs?: number;
  hourlyRate?: number;
  isAvailable: boolean;
  profileImage?: string;
  skills?: string[];
  responseTime?: string;
}

interface ServiceType {
  id: string;
  name: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  estimatedDuration: string;
  basePrice: number;
  category: string;
}

type BookJobScreenNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'BookJob'>;

const { width: screenWidth } = Dimensions.get('window');

const serviceTypes: ServiceType[] = [
  {
    id: '1',
    name: 'Plumbing',
    description: 'Pipes, leaks, installations',
    icon: 'water-outline',
    estimatedDuration: '1-3 hours',
    basePrice: 80,
    category: 'Home Maintenance'
  },
  {
    id: '2',
    name: 'Electrical',
    description: 'Wiring, outlets, lighting',
    icon: 'flash-outline',
    estimatedDuration: '1-4 hours',
    basePrice: 90,
    category: 'Home Maintenance'
  },
  {
    id: '3',
    name: 'HVAC',
    description: 'Heating, cooling, ventilation',
    icon: 'thermometer-outline',
    estimatedDuration: '2-6 hours',
    basePrice: 120,
    category: 'Climate Control'
  },
  {
    id: '4',
    name: 'Appliance Repair',
    description: 'Washing machines, fridges, ovens',
    icon: 'construct-outline',
    estimatedDuration: '1-2 hours',
    basePrice: 70,
    category: 'Appliances'
  },
  {
    id: '5',
    name: 'General Handyman',
    description: 'Various home repairs',
    icon: 'hammer-outline',
    estimatedDuration: '1-4 hours',
    basePrice: 60,
    category: 'General'
  },
  {
    id: '6',
    name: 'Cleaning',
    description: 'Deep cleaning, maintenance',
    icon: 'sparkles-outline',
    estimatedDuration: '2-4 hours',
    basePrice: 50,
    category: 'Cleaning'
  }
];

interface UrgencyLevel {
  id: string;
  label: string;
  description: string;
  color: string;
  icon: keyof typeof Ionicons.glyphMap;
}

const urgencyLevels: UrgencyLevel[] = [
  { id: 'low', label: 'Low', description: 'Within a week', color: '#8E8E93', icon: 'time-outline' },
  { id: 'medium', label: 'Medium', description: 'Within 2-3 days', color: '#FF9500', icon: 'alert-circle-outline' },
  { id: 'high', label: 'High', description: 'Within 24 hours', color: '#FF3B30', icon: 'warning-outline' },
  { id: 'urgent', label: 'Emergency', description: 'ASAP', color: '#FF3B30', icon: 'flash-outline' }
];

export default function ModernBookJobScreen() {
  const navigation = useNavigation<BookJobScreenNavigationProp>();
  const { colors, spacing, typography, borderRadius } = useTheme();

  // Form state
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedService, setSelectedService] = useState<ServiceType | null>(null);
  const [issue, setIssue] = useState('');
  const [urgency, setUrgency] = useState('medium');
  const [scheduledAt, setScheduledAt] = useState<Date>(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState<'date' | 'time'>('date');
  const [address, setAddress] = useState('');
  const [selectedTechnician, setSelectedTechnician] = useState<Technician | null>(null);
  const [loading, setLoading] = useState(false);
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [loadingTechnicians, setLoadingTechnicians] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const stepProgress = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  useEffect(() => {
    // Animate step progress
    Animated.timing(stepProgress, {
      toValue: (currentStep - 1) / 3,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [currentStep]);

  useEffect(() => {
    if (currentStep === 3 && selectedService) {
      loadTechnicians();
    }
  }, [currentStep, selectedService]);

  const loadTechnicians = async () => {
    if (!selectedService) return;
    
    setLoadingTechnicians(true);
    try {
      // Fetch available technicians from API
      const response = await fetch(`${API_BASE_URL}/api/technicians/available`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await getAuthToken()}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setTechnicians(data.technicians || []);
      } else {
        throw new Error('Failed to fetch technicians');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load technicians');
    } finally {
      setLoadingTechnicians(false);
    }
  };

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      navigation.goBack();
    }
  };

  const handleSubmit = async () => {
    if (!selectedService || !selectedTechnician) {
      Alert.alert('Error', 'Please complete all required fields');
      return;
    }

    setLoading(true);
    try {
      const jobData: JobCreatePayload = {
        issue,
        scheduledAt: scheduledAt.toISOString(),
        technicianId: selectedTechnician.id,
        serviceType: selectedService.name,
        urgency,
        address
      };

      await JobAPI.createJob(jobData);
      Alert.alert(
        'Success!', 
        'Your job has been booked successfully. The technician will contact you soon.',
        [{ text: 'OK', onPress: () => navigation.navigate('Home') }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to book the job. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1: return selectedService !== null;
      case 2: return issue.trim().length > 0 && address.trim().length > 0;
      case 3: return selectedTechnician !== null;
      case 4: return true;
      default: return false;
    }
  };

  const ServiceCard = ({ service, isSelected, onPress }: {
    service: ServiceType;
    isSelected: boolean;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      style={[styles.serviceCard, isSelected && styles.selectedServiceCard]}
      onPress={onPress}
    >
      <View style={styles.serviceCardGlass}>
        <BlurView intensity={isSelected ? 30 : 15} />
      </View>
      <View style={styles.serviceCardContent}>
        <View style={[styles.serviceIcon, { backgroundColor: isSelected ? colors.primary.main + '20' : colors.neutral.light + '20' }]}>
          <Ionicons 
            name={service.icon} 
            size={24} 
            color={isSelected ? colors.primary.main : colors.text.secondary} 
          />
        </View>
        <Text style={[styles.serviceName, isSelected && styles.selectedServiceName]}>
          {service.name}
        </Text>
        <Text style={styles.serviceDescription}>{service.description}</Text>
        <View style={styles.serviceFooter}>
          <Text style={styles.serviceDuration}>{service.estimatedDuration}</Text>
          <Text style={styles.servicePrice}>from £{service.basePrice}</Text>
        </View>
        {isSelected && (
          <View style={styles.selectedIndicator}>
            <Ionicons name="checkmark-circle" size={20} color={colors.primary.main} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const UrgencyCard = ({ urgencyLevel, isSelected, onPress }: {
    urgencyLevel: UrgencyLevel;
    isSelected: boolean;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      style={[styles.urgencyCard, isSelected && styles.selectedUrgencyCard]}
      onPress={onPress}
    >
      <View style={styles.urgencyCardGlass}>
        <BlurView intensity={15} />
      </View>
      <View style={styles.urgencyCardContent}>
        <View style={[styles.urgencyIcon, { backgroundColor: urgencyLevel.color + '20' }]}>
          <Ionicons name={urgencyLevel.icon} size={20} color={urgencyLevel.color} />
        </View>
        <Text style={[styles.urgencyLabel, isSelected && styles.selectedUrgencyLabel]}>
          {urgencyLevel.label}
        </Text>
        <Text style={styles.urgencyDescription}>{urgencyLevel.description}</Text>
      </View>
    </TouchableOpacity>
  );

  const TechnicianCard = ({ technician, isSelected, onPress }: {
    technician: Technician;
    isSelected: boolean;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      style={[styles.technicianCard, isSelected && styles.selectedTechnicianCard]}
      onPress={onPress}
    >
      <View style={styles.technicianCardGlass}>
        <BlurView intensity={isSelected ? 25 : 15} />
      </View>
      <View style={styles.technicianCardContent}>
        <View style={styles.technicianHeader}>
          <View style={styles.technicianAvatar}>
            <Text style={styles.technicianInitial}>{technician.fullName.charAt(0)}</Text>
          </View>
          <View style={styles.technicianInfo}>
            <Text style={[styles.technicianName, isSelected && styles.selectedTechnicianName]}>
              {technician.fullName}
            </Text>
            <Text style={styles.technicianSpecialization}>{technician.specialization}</Text>
            <View style={styles.technicianMeta}>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={14} color="#FFD700" />
                <Text style={styles.ratingText}>{technician.rating}</Text>
                <Text style={styles.jobsText}>({technician.completedJobs} jobs)</Text>
              </View>
              <Text style={styles.distanceText}>{technician.distance} km away</Text>
            </View>
          </View>
          <View style={styles.technicianPrice}>
            <Text style={styles.priceText}>£{technician.hourlyRate}/hr</Text>
            <Text style={styles.responseTime}>{technician.responseTime}</Text>
          </View>
        </View>
        
        {technician.skills && (
          <View style={styles.skillsContainer}>
            {technician.skills.slice(0, 2).map((skill, index) => (
              <View key={index} style={styles.skillBadge}>
                <Text style={styles.skillText}>{skill}</Text>
              </View>
            ))}
          </View>
        )}
        
        {isSelected && (
          <View style={styles.selectedTechnicianIndicator}>
            <Ionicons name="checkmark-circle" size={20} color={colors.primary.main} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>What service do you need? 🔧</Text>
            <Text style={styles.stepDescription}>Choose the type of service you're looking for</Text>
            
            <View style={styles.servicesGrid}>
              {serviceTypes.map((item, index) => {
                if (index % 2 === 0) {
                  const nextItem = serviceTypes[index + 1];
                  return (
                    <View key={item.id} style={styles.serviceRow}>
                      <ServiceCard
                        service={item}
                        isSelected={selectedService?.id === item.id}
                        onPress={() => setSelectedService(item)}
                      />
                      {nextItem && (
                        <ServiceCard
                          service={nextItem}
                          isSelected={selectedService?.id === nextItem.id}
                          onPress={() => setSelectedService(nextItem)}
                        />
                      )}
                    </View>
                  );
                }
                return null;
              })}
            </View>
          </View>
        );

      case 2:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Describe your issue</Text>
            <Text style={styles.stepDescription}>Provide details to help us match you with the right technician</Text>
            
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Issue Description *</Text>
                <View style={styles.textAreaContainer}>
                  <TextInput
                    style={styles.textArea}
                    placeholder="Describe the problem in detail..."
                    placeholderTextColor={colors.text.secondary}
                    value={issue}
                    onChangeText={setIssue}
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                  />
                </View>
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Address *</Text>
                <View style={styles.inputWrapper}>
                  <Ionicons name="location-outline" size={20} color={colors.text.secondary} />
                  <TextInput
                    style={styles.input}
                    placeholder="Enter your address"
                    placeholderTextColor={colors.text.secondary}
                    value={address}
                    onChangeText={setAddress}
                  />
                </View>
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Urgency Level</Text>
                <View style={styles.urgencyGrid}>
                  {urgencyLevels.map((level) => (
                    <UrgencyCard
                      key={level.id}
                      urgencyLevel={level}
                      isSelected={urgency === level.id}
                      onPress={() => setUrgency(level.id)}
                    />
                  ))}
                </View>
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Preferred Date & Time</Text>
                <View style={styles.dateTimeContainer}>
                  <TouchableOpacity
                    style={[styles.datePickerButton, { flex: 1, marginRight: spacing.sm }]}
                    onPress={() => {
                      setDatePickerMode('date');
                      setShowDatePicker(true);
                    }}
                  >
                    <Ionicons name="calendar-outline" size={20} color={colors.text.secondary} />
                    <Text style={styles.datePickerText}>
                      {scheduledAt.toLocaleDateString()}
                    </Text>
                    <Ionicons name="chevron-down" size={20} color={colors.text.secondary} />
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.datePickerButton, { flex: 1 }]}
                    onPress={() => {
                      setDatePickerMode('time');
                      setShowTimePicker(true);
                    }}
                  >
                    <Ionicons name="time-outline" size={20} color={colors.text.secondary} />
                    <Text style={styles.datePickerText}>
                      {scheduledAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Text>
                    <Ionicons name="chevron-down" size={20} color={colors.text.secondary} />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        );

      case 3:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Choose your technician</Text>
            <Text style={styles.stepDescription}>Select from available technicians in your area</Text>
            
            {loadingTechnicians ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary.main} />
                <Text style={styles.loadingText}>Finding technicians...</Text>
              </View>
            ) : (
              <View style={styles.techniciansContainer}>
                {technicians.map((item) => (
                  <TechnicianCard
                    key={item.id}
                    technician={item}
                    isSelected={selectedTechnician?.id === item.id}
                    onPress={() => setSelectedTechnician(item)}
                  />
                ))}
              </View>
            )}
          </View>
        );

      case 4:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Confirm your booking</Text>
            <Text style={styles.stepDescription}>Review your details before confirming</Text>
            
            <View style={styles.summaryContainer}>
              <View style={styles.summaryCard}>
                <BlurView intensity={20} />
              </View>
              <View style={styles.summaryCardContent}>
                <View style={styles.summarySection}>
                  <Text style={styles.summaryLabel}>Service</Text>
                  <Text style={styles.summaryValue}>{selectedService?.name}</Text>
                </View>
                
                <View style={styles.summarySection}>
                  <Text style={styles.summaryLabel}>Technician</Text>
                  <View style={styles.summaryTechnician}>
                    <View style={styles.summaryTechnicianAvatar}>
                      <Text style={styles.summaryTechnicianInitial}>
                        {selectedTechnician?.fullName.charAt(0)}
                      </Text>
                    </View>
                    <View>
                      <Text style={styles.summaryValue}>{selectedTechnician?.fullName}</Text>
                      <Text style={styles.summarySubValue}>£{selectedTechnician?.hourlyRate}/hr</Text>
                    </View>
                  </View>
                </View>
                
                <View style={styles.summarySection}>
                  <Text style={styles.summaryLabel}>Date & Time</Text>
                  <Text style={styles.summaryValue}>
                    {scheduledAt.toLocaleDateString()} at {scheduledAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </Text>
                </View>
                
                <View style={styles.summarySection}>
                  <Text style={styles.summaryLabel}>Address</Text>
                  <Text style={styles.summaryValue}>{address}</Text>
                </View>
                
                <View style={styles.summarySection}>
                  <Text style={styles.summaryLabel}>Issue</Text>
                  <Text style={styles.summaryValue}>{issue}</Text>
                </View>
                
                <View style={styles.summaryDivider} />
                
                <View style={styles.summaryTotal}>
                  <Text style={styles.summaryTotalLabel}>Estimated Cost</Text>
                  <Text style={styles.summaryTotalValue}>£{selectedService?.basePrice}</Text>
                </View>
              </View>
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    gradientBackground: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: 250,
    },
    header: {
      paddingTop: StatusBar.currentHeight || 44,
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.md,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    headerSpacer: {
      width: 40,
    },
    progressContainer: {
      marginBottom: spacing.xl,
    },
    progressBar: {
      height: 4,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 2,
      marginBottom: spacing.sm,
    },
    progressFill: {
      height: '100%',
      backgroundColor: colors.primary.main,
      borderRadius: 2,
    },
    progressText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      textAlign: 'center',
    },
    content: {
      flex: 1,
      paddingHorizontal: spacing.lg,
    },
    stepContent: {
      flex: 1,
    },
    stepTitle: {
      fontSize: 28,
      fontWeight: '800',
      color: colors.text.primary,
      marginBottom: spacing.md,
      textAlign: 'center',
      textShadowColor: 'rgba(0, 0, 0, 0.1)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    stepDescription: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginBottom: spacing.xl,
      lineHeight: 24,
      textAlign: 'center',
      opacity: 0.8,
    },
    servicesGrid: {
      paddingBottom: spacing.xl,
      paddingTop: spacing.lg,
      paddingHorizontal: spacing.sm,
    },
    serviceRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.lg,
      gap: spacing.md,
    },
    serviceCard: {
      flex: 1,
      maxWidth: (screenWidth - spacing.lg * 2 - spacing.md) / 2,
      height: 180,
      borderRadius: borderRadius.lg,
      elevation: 6, // Increased elevation
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 }, // Increased shadow
      shadowOpacity: 0.15,
      shadowRadius: 8,
      backgroundColor: 'rgba(255, 255, 255, 0.03)', // Add subtle background
    },
    selectedServiceCard: {
      transform: [{ scale: 1.08 }],
      borderWidth: 2,
      borderColor: colors.primary.main,
      shadowColor: colors.primary.main,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.3,
      shadowRadius: 16,
      elevation: 8,
    },
    serviceCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.12)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.18)',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.1,
      shadowRadius: 12,
      elevation: 4,
    },
    serviceCardContent: {
      flex: 1,
      borderRadius: borderRadius.lg,
      padding: spacing.md,
      position: 'relative',
      justifyContent: 'space-between',
    },
    serviceIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    serviceName: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    selectedServiceName: {
      color: colors.primary.main,
    },
    serviceDescription: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.md,
      lineHeight: 18,
      height: 54, // Fixed height for 3 lines of text
      overflow: 'hidden',
    },
    serviceFooter: {
      marginTop: 'auto',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    serviceDuration: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
    },
    servicePrice: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.semibold,
      color: colors.primary.main,
    },
    selectedIndicator: {
      position: 'absolute',
      top: spacing.sm,
      right: spacing.sm,
    },
    formContainer: {
      flex: 1,
    },
    inputContainer: {
      marginBottom: spacing.xl,
    },
    inputLabel: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.12)',
      borderRadius: borderRadius.md,
      paddingHorizontal: spacing.md,
      height: 52,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.18)',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.05,
      shadowRadius: 6,
      elevation: 2,
    },
    input: {
      flex: 1,
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      marginLeft: spacing.sm,
    },
    textAreaContainer: {
      backgroundColor: 'rgba(255, 255, 255, 0.12)',
      borderRadius: borderRadius.md,
      padding: spacing.lg,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.18)',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.05,
      shadowRadius: 6,
      elevation: 2,
    },
    textArea: {
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      minHeight: 80,
    },
    urgencyGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: spacing.sm,
    },
    urgencyCard: {
      width: (screenWidth - spacing.lg * 2 - spacing.sm) / 2,
      height: 80,
    },
    selectedUrgencyCard: {
      transform: [{ scale: 1.02 }],
    },
    urgencyCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.md,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    urgencyCardContent: {
      flex: 1,
      borderRadius: borderRadius.md,
      padding: spacing.md,
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
    },
    urgencyIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.sm,
    },
    urgencyLabel: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
    },
    selectedUrgencyLabel: {
      color: colors.primary.main,
    },
    urgencyDescription: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
    },
    datePickerButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderRadius: borderRadius.md,
      paddingHorizontal: spacing.md,
      height: 48,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    datePickerText: {
      flex: 1,
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      marginLeft: spacing.sm,
    },
    dateTimeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    techniciansContainer: {
      paddingBottom: spacing.xl,
    },
    technicianCard: {
      marginBottom: spacing.md,
    },
    selectedTechnicianCard: {
      transform: [{ scale: 1.03 }],
      borderWidth: 2,
      borderColor: colors.primary.main,
      shadowColor: colors.primary.main,
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.25,
      shadowRadius: 12,
      elevation: 6,
    },
    technicianCardGlass: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.12)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.18)',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.08,
      shadowRadius: 10,
      elevation: 3,
    },
    technicianCardContent: {
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
      position: 'relative',
    },
    technicianHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: spacing.md,
    },
    technicianAvatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: colors.primary.main,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    technicianInitial: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    technicianInfo: {
      flex: 1,
    },
    technicianName: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    selectedTechnicianName: {
      color: colors.primary.main,
    },
    technicianSpecialization: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.sm,
    },
    technicianMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    ratingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    ratingText: {
      fontSize: typography.fontSize.sm,
      color: colors.text.primary,
      marginLeft: spacing.xs,
    },
    jobsText: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
      marginLeft: spacing.xs,
    },
    distanceText: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
    },
    technicianPrice: {
      alignItems: 'flex-end',
    },
    priceText: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.bold,
      color: colors.primary.main,
    },
    responseTime: {
      fontSize: typography.fontSize.xs,
      color: colors.text.secondary,
    },
    skillsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: spacing.xs,
    },
    skillBadge: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      backgroundColor: 'rgba(0, 122, 255, 0.1)',
      borderRadius: borderRadius.sm,
    },
    skillText: {
      fontSize: typography.fontSize.xs,
      color: colors.primary.main,
      fontWeight: typography.fontWeight.medium,
    },
    selectedTechnicianIndicator: {
      position: 'absolute',
      top: spacing.md,
      right: spacing.md,
    },
    summaryContainer: {
      flex: 1,
    },
    summaryCard: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.08)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.12)',
    },
    summaryCardContent: {
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
      position: 'relative',
    },
    summarySection: {
      marginBottom: spacing.lg,
    },
    summaryLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    summaryValue: {
      fontSize: typography.fontSize.md,
      color: colors.text.primary,
      fontWeight: typography.fontWeight.medium,
    },
    summarySubValue: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
    },
    summaryTechnician: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    summaryTechnicianAvatar: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.primary.main,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.sm,
    },
    summaryTechnicianInitial: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.inverse,
    },
    summaryDivider: {
      height: 1,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      marginVertical: spacing.lg,
    },
    summaryTotal: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    summaryTotalLabel: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
    },
    summaryTotalValue: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.primary.main,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: typography.fontSize.md,
      color: colors.text.secondary,
      marginTop: spacing.md,
    },
    footer: {
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.lg,
      paddingTop: spacing.md,
    },
    footerButtons: {
      flexDirection: 'row',
      gap: spacing.md,
    },
    backButtonFooter: {
      flex: 1,
    },
    nextButton: {
      flex: 2,
    },
  }), [colors, spacing, typography, borderRadius, screenWidth]);

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      <View style={styles.gradientBackground}>
        <SafeLinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={20} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Book Service</Text>
          <View style={styles.headerSpacer} />
        </View>
        
        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View 
              style={[
                styles.progressFill,
                {
                  width: stepProgress.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  })
                }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>Step {currentStep} of 4</Text>
        </View>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderStepContent()}
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.footerButtons}>
          {currentStep > 1 && (
            <Button
              title="Back"
              onPress={handleBack}
              variant="outline"
              size="lg"
              style={styles.backButtonFooter}
            />
          )}
          <Button
            title={currentStep === 4 ? 'Confirm Booking' : 'Next'}
            onPress={currentStep === 4 ? handleSubmit : handleNext}
            variant="primary"
            size="lg"
            disabled={!canProceed()}
            loading={loading}
            style={styles.nextButton}
          />
        </View>
      </View>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={scheduledAt}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={(event, selectedDate) => {
            setShowDatePicker(false);
            if (event.type === 'set' && selectedDate) {
              const newDate = new Date(scheduledAt);
              newDate.setFullYear(selectedDate.getFullYear());
              newDate.setMonth(selectedDate.getMonth());
              newDate.setDate(selectedDate.getDate());
              setScheduledAt(newDate);
            }
          }}
        />
      )}

      {/* Time Picker */}
      {showTimePicker && (
        <DateTimePicker
          value={scheduledAt}
          mode="time"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={(event, selectedTime) => {
            setShowTimePicker(false);
            if (event.type === 'set' && selectedTime) {
              const newDate = new Date(scheduledAt);
              newDate.setHours(selectedTime.getHours());
              newDate.setMinutes(selectedTime.getMinutes());
              setScheduledAt(newDate);
            }
          }}
        />
      )}
    </Animated.View>
  );
}