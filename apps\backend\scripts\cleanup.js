#!/usr/bin/env node

// CTRON Home Backend - Cleanup Script
// Removes build artifacts, logs, and temporary files

const fs = require('fs');
const path = require('path');

const cleanupPaths = [
  'dist',
  'build',
  '*.log',
  'logs',
  'coverage',
  '.nyc_output',
  'node_modules/.cache',
  '*.tsbuildinfo',
];

const cleanupDirectory = (dirPath) => {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`✅ Removed: ${dirPath}`);
  }
};

const cleanupFiles = (pattern) => {
  const files = fs.readdirSync('.').filter(file => file.match(pattern));
  files.forEach(file => {
    fs.unlinkSync(file);
    console.log(`✅ Removed: ${file}`);
  });
};

console.log('🧹 Starting backend cleanup...');

cleanupPaths.forEach(cleanupPath => {
  if (cleanupPath.includes('*')) {
    const pattern = new RegExp(cleanupPath.replace('*', '.*'));
    cleanupFiles(pattern);
  } else {
    cleanupDirectory(cleanupPath);
  }
});

console.log('✅ Backend cleanup completed!');