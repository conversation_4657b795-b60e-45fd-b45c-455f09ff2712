/**
 * Dynamic Import Helper
 * 
 * This utility provides a standardized way to dynamically import modules
 * based on the current platform (web, iOS, Android).
 * 
 * It helps prevent runtime errors when importing native-only modules in web environments
 * and vice versa by providing platform-specific implementations.
 */

import { isIOS, isAndroid, isNative } from './platformUtils';

/**
 * Synchronously imports a module based on the current platform.
 * This function is deprecated and should be replaced with static imports.
 * 
 * @deprecated Use static imports with Platform.select instead
 * @param baseModulePath - The base path to the module without extension
 * @param options - Configuration options for module resolution
 * @returns The imported module or an empty object as fallback
 */
export function dynamicImport<T>(
  baseModulePath: string,
  options: {
    webFallbackPath?: string;
    nativeFallbackPath?: string;
    defaultFallbackPath?: string;
  } = {}
): T {
  if (__DEV__) {
    console.warn('dynamicImport is deprecated. Use static imports with Platform.select instead.');
  }
  
  // For now, return an empty object to prevent crashes
  // This function should be replaced with static imports
  return {} as T;
}

/**
 * Asynchronously imports a module based on the current platform.
 * Useful for code-splitting and reducing initial bundle size.
 * 
 * @param baseModulePath - The base path to the module without extension
 * @param options - Configuration options for module resolution
 * @returns A promise that resolves to the imported module
 * 
 * @example
 * // Usage with React.lazy
 * const MyLazyComponent = React.lazy(() => dynamicImportAsync('./MyComponent'));
 */
export async function dynamicImportAsync<T>(
  baseModulePath: string,
  options: {
    webFallbackPath?: string;
    nativeFallbackPath?: string;
    defaultFallbackPath?: string;
  } = {}
): Promise<T> {
  try {
    // Try platform-specific imports first
    if (isIOS) {
      try {
        // Try .ios extension first
        return await import(`${baseModulePath}.ios`);
      } catch (iosError) {
        // Fall through to native fallback
      }
    } else if (isAndroid) {
      try {
        // Try .android extension first
        return await import(`${baseModulePath}.android`);
      } catch (androidError) {
        // Fall through to native fallback
      }
    }

    // Try native fallback for iOS/Android if platform-specific module doesn't exist
    if (isNative && options.nativeFallbackPath) {
      try {
        return await import(options.nativeFallbackPath);
      } catch (nativeFallbackError) {
        // Fall through to default import
      }
    }

    // Try the specified default fallback path if provided
    if (options.defaultFallbackPath) {
      return await import(options.defaultFallbackPath);
    }

    // Default: try importing the base module without extension
    return await import(baseModulePath);
  } catch (error) {
    console.error(`Failed to dynamically import module asynchronously: ${baseModulePath}`, error);
    
    // Return an empty object as a last resort to prevent crashes
    return {} as T;
  }
}

/**
 * Creates a platform-specific component or module.
 * 
 * @param components - Object containing platform-specific implementations
 * @returns The appropriate component/module for the current platform
 * 
 * @example
 * const Button = createPlatformSpecific({
 *   web: WebButton,
 *   ios: IOSButton,
 *   android: AndroidButton,
 *   default: DefaultButton
 * });
 */
export function createPlatformSpecific<T>(
  components: {
    ios?: T;
    android?: T;
    native?: T;
    default: T;
  }
): T {
  if (isIOS && components.ios) {
    return components.ios;
  } else if (isAndroid && components.android) {
    return components.android;
  } else if (isNative && components.native) {
    return components.native;
  }
  
  return components.default;
}