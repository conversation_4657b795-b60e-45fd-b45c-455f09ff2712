// src/api/job.api.ts
import axios from 'axios';

import { API_BASE_URL } from '../config/api.config';
import type { Job } from '../types/job';
import { getAuthToken } from '../utils/auth.utils';

export type JobCreatePayload = {
  issue: string;
  serviceType: string;
  scheduledAt: string;
  technicianId?: string;
  urgency?: string;
  address?: string;
};

// Helper function to make authenticated API calls
const makeAuthenticatedRequest = async (method: string, endpoint: string, data?: unknown) => {
  const token = await getAuthToken();

  if (!token) {
    throw new Error('Authentication token not found. Please log in again.');
  }

  const config: AxiosRequestConfig = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  };

  if (data) {
    config.data = data;
  }

  if (__DEV__) {
    console.log(`🔗 API Request: ${method} ${endpoint}`);
    console.log('📦 Request data:', data);
    console.log('🔑 Token present:', !!token);
  }

  try {
    const response = await axios(config);

    if (__DEV__) {
      console.log(`✅ API Response: ${method} ${endpoint} - ${response.status}`);
    }

    return response;
  } catch (error: unknown) {
    if (__DEV__) {
      console.error(`❌ API Error: ${method} ${endpoint}`);
      console.error('Status:', (error as { response?: { status?: number; data?: unknown } }).response?.status);
      console.error('Data:', (error as { response?: { status?: number; data?: unknown } }).response?.data);
      console.error('Message:', (error as { message?: string }).message);
    }

    // Enhanced error handling for common HTTP status codes
    const errorResponse = (error as { response?: { status?: number } }).response;
    if (errorResponse?.status === 401) {
      throw new Error('Authentication failed. Please log in again.');
    } else if (errorResponse?.status === 403) {
      throw new Error('You do not have permission to perform this action.');
    } else if (errorResponse?.status === 404) {
      throw new Error('The requested resource was not found.');
    } else if (errorResponse?.status && errorResponse.status >= 500) {
      throw new Error('Server error. Please try again later.');
    }

    throw error;
  }
};

export const JobAPI = {
  createJob: async (data: JobCreatePayload): Promise<Job> => {
    const res = await makeAuthenticatedRequest('POST', '/api/jobs', data);
    return res.data.job;
  },

  getUserJobs: async (): Promise<Job[]> => {
    const res = await makeAuthenticatedRequest('GET', '/api/jobs/my-jobs');
    return res.data.jobs;
  },

  getMyJobs: async (): Promise<Job[]> => {
    const res = await makeAuthenticatedRequest('GET', '/api/jobs/my-jobs');
    return res.data.jobs;
  },

  getTechnicianJobs: async (): Promise<Job[]> => {
    const res = await makeAuthenticatedRequest('GET', '/api/jobs/assigned');
    return res.data.jobs;
  },

  getJobDetails: async (id: string): Promise<Job> => {
    const res = await makeAuthenticatedRequest('GET', `/api/jobs/${id}`);
    return res.data.job;
  },

  updateStatus: async (
    id: string,
    status: 'IN_PROGRESS' | 'COMPLETED',
    payload?: { photoUrl?: string }
  ): Promise<Job> => {
    const res = await makeAuthenticatedRequest('PUT', `/api/jobs/${id}/status`, {
      status,
      ...(payload || {}),
    });
    return res.data.job;
  },

  cancelJob: async (id: string, reason?: string): Promise<Job> => {
    const res = await makeAuthenticatedRequest('PUT', `/api/jobs/${id}/status`, {
      status: 'CANCELLED',
      cancellationReason: reason,
    });
    return res.data.job;
  },

  getJobById: async (id: string): Promise<Job> => {
    const res = await makeAuthenticatedRequest('GET', `/api/jobs/${id}`);
    return res.data.job;
  },

  assignTechnician: async (jobId: string, technicianId: string): Promise<Job> => {
    const res = await makeAuthenticatedRequest('PUT', `/api/jobs/${jobId}/assign`, {
      technicianId,
    });
    return res.data.job;
  },

  getAvailableJobs: async (): Promise<Job[]> => {
    const res = await makeAuthenticatedRequest('GET', '/api/jobs/available');
    return res.data.jobs;
  },

  acceptJob: async (jobId: string): Promise<Job> => {
    const res = await makeAuthenticatedRequest('POST', `/api/jobs/${jobId}/accept`);
    return res.data.job;
  },

  completeJob: async (jobId: string, data: { proofImages?: string[]; notes?: string }): Promise<Job> => {
    const res = await makeAuthenticatedRequest('PUT', `/api/jobs/${jobId}/complete`, data);
    return res.data.job;
  },

  updateJob: async (jobId: string, data: {
    issue?: string;
    description?: string;
    scheduledAt?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
  }): Promise<Job> => {
    const res = await makeAuthenticatedRequest('PUT', `/api/jobs/${jobId}`, data);
    return res.data.job;
  },
};
