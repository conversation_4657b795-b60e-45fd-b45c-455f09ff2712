// CTRON Home Design System - Input Component
// Standardized input field with consistent styling

import React, { useState } from 'react';

import { View, TextInput, Text, ViewStyle, TextStyle, TouchableOpacity } from '../../utils/platformUtils';
import { tokens } from '../tokens';

export interface InputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'outlined' | 'filled' | 'underlined';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  style?: ViewStyle;
  inputStyle?: TextStyle;
}

export const Input: React.FC<InputProps> = ({
  value,
  onChangeText,
  placeholder,
  label,
  error,
  helperText,
  variant = 'outlined',
  size = 'md',
  disabled = false,
  multiline = false,
  numberOfLines = 1,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  leftIcon,
  rightIcon,
  onRightIconPress,
  style,
  inputStyle,
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const getVariantStyles = (): { container: ViewStyle; input: TextStyle } => {
    const baseContainer: ViewStyle = {
      flexDirection: 'row',
      alignItems: multiline ? 'flex-start' : 'center',
      borderRadius: tokens.borderRadius.base,
    };

    const baseInput: TextStyle = {
      flex: 1,
      fontFamily: tokens.typography.fontFamily.primary,
      color: tokens.colors.neutral[900],
    };


    const borderColor = error
      ? tokens.colors.error[500]
      : isFocused
        ? tokens.colors.primary[500]
        : tokens.colors.neutral[300];

    switch (variant) {
      case 'outlined':
        return {
          container: {
            ...baseContainer,
            borderWidth: 1,
            borderColor,
            backgroundColor: disabled ? tokens.colors.neutral[50] : tokens.colors.neutral[0],
          },
          input: baseInput,
        };

      case 'filled':
        return {
          container: {
            ...baseContainer,
            backgroundColor: disabled ? tokens.colors.neutral[100] : tokens.colors.neutral[50],
            borderBottomWidth: 2,
            borderBottomColor: borderColor,
          },
          input: baseInput,
        };

      case 'underlined':
        return {
          container: {
            ...baseContainer,
            backgroundColor: 'transparent',
            borderBottomWidth: 1,
            borderBottomColor: borderColor,
          },
          input: baseInput,
        };

      default:
        return {
          container: baseContainer,
          input: baseInput,
        };
    }
  };

  const getSizeStyles = (): { container: ViewStyle; input: TextStyle } => {
    switch (size) {
      case 'sm':
        return {
          container: {
            paddingHorizontal: tokens.spacing[3],
            paddingVertical: tokens.spacing[2],
            minHeight: 32,
          },
          input: {
            fontSize: tokens.typography.fontSize.sm,
          },
        };

      case 'md':
        return {
          container: {
            paddingHorizontal: tokens.spacing[4],
            paddingVertical: tokens.spacing[3],
            minHeight: 40,
          },
          input: {
            fontSize: tokens.typography.fontSize.base,
          },
        };

      case 'lg':
        return {
          container: {
            paddingHorizontal: tokens.spacing[5],
            paddingVertical: tokens.spacing[4],
            minHeight: 48,
          },
          input: {
            fontSize: tokens.typography.fontSize.lg,
          },
        };

      default:
        return {
          container: {},
          input: {},
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();

  const containerStyle: ViewStyle = {
    ...variantStyles.container,
    ...sizeStyles.container,
    ...(disabled && { opacity: 0.6 }),
  };

  const textInputStyle: TextStyle = {
    ...variantStyles.input,
    ...sizeStyles.input,
    ...(multiline && {
      textAlignVertical: 'top',
      paddingTop: tokens.spacing[2],
    }),
    ...inputStyle,
  };

  const labelStyle: TextStyle = {
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.medium,
    color: tokens.colors.neutral[700],
    marginBottom: tokens.spacing[1],
  };

  const errorStyle: TextStyle = {
    fontSize: tokens.typography.fontSize.xs,
    color: tokens.colors.error[500],
    marginTop: tokens.spacing[1],
  };

  const helperStyle: TextStyle = {
    fontSize: tokens.typography.fontSize.xs,
    color: tokens.colors.neutral[600],
    marginTop: tokens.spacing[1],
  };

  return (
    <View style={style}>
      {label && <Text style={labelStyle}>{label}</Text>}

      <View style={containerStyle}>
        {leftIcon && (
          <View style={{ marginRight: tokens.spacing[2] }}>
            {leftIcon}
          </View>
        )}

        <TextInput
          style={textInputStyle}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={tokens.colors.neutral[400]}
          editable={!disabled}
          multiline={multiline}
          numberOfLines={numberOfLines}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />

        {rightIcon && (
          <TouchableOpacity
            style={{ marginLeft: tokens.spacing[2] }}
            onPress={onRightIconPress}
            disabled={!onRightIconPress}
          >
            {rightIcon}
          </TouchableOpacity>
        )}
      </View>

      {error && <Text style={errorStyle}>{error}</Text>}
      {helperText && !error && <Text style={helperStyle}>{helperText}</Text>}
    </View>
  );
};