// CTRON Home - Modern My Jobs Screen with NativeBase
// Enhanced job management with professional UI components

import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Box,
  VStack,
  HStack,
  Text,
  ScrollView,
  Button,
  Input,
  Badge,
  Avatar,
  Icon,
  Pressable,
  Select,
  useTheme,
  useToast,
  RefreshControl,
  Skeleton,
  Divider,
} from 'native-base';
import React, { useState, useEffect, useCallback } from 'react';

import { JobAPI } from '../../api/job.api';
import { JobStatusCard } from '../../components/modern';
import { useAuth } from '../../context/AuthContext';
import type { HomeownerStackParamList } from '../../navigation/types';
import type { Job } from '../../types/job';

type MyJobsScreenNavigationProp = NativeStackNavigationProp<HomeownerStackParamList, 'MyJobs'>;

interface JobWithDetails extends Job {
  technician?: {
    id: string;
    user: {
      fullName: string;
    };
    rating?: number;
    specialization?: string;
  };
}

const ModernMyJobsScreenNB: React.FC = () => {
  const navigation = useNavigation<MyJobsScreenNavigationProp>();
  const { user } = useAuth();
  const toast = useToast();
  const theme = useTheme();

  const [jobs, setJobs] = useState<JobWithDetails[]>([]);
  const [filteredJobs, setFilteredJobs] = useState<JobWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'active' | 'completed' | 'pending'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'status' | 'priority'>('date');

  const loadJobs = useCallback(async () => {
    try {
      setLoading(true);
      const response = await JobAPI.getMyJobs();
      const jobsData = (response || []).map((job: any) => ({
        ...job,
        description: job.description || job.issue || 'No description available',
        title: job.title || job.issue || 'Untitled Job'
      }) as JobWithDetails);
      setJobs(jobsData);
      setFilteredJobs(jobsData);
    } catch (error) {
      toast.show({
        title: 'Error',
        description: 'Failed to load your jobs',
        status: 'error',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadJobs();
  }, [loadJobs]);

  useEffect(() => {
    filterAndSortJobs();
  }, [jobs, searchQuery, selectedFilter, sortBy]);

  const filterAndSortJobs = () => {
    let filtered = jobs;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(job => 
        job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (job.serviceType && job.serviceType.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply status filter
    if (selectedFilter !== 'all') {
      if (selectedFilter === 'active') {
        filtered = filtered.filter(job => ['PENDING', 'ACCEPTED', 'IN_PROGRESS'].includes(job.status));
      } else if (selectedFilter === 'completed') {
        filtered = filtered.filter(job => ['COMPLETED', 'CANCELLED'].includes(job.status));
      } else if (selectedFilter === 'pending') {
        filtered = filtered.filter(job => job.status === 'PENDING');
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        case 'status':
          return a.status.localeCompare(b.status);
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          return (priorityOrder[b.priority as keyof typeof priorityOrder] || 2) - 
                 (priorityOrder[a.priority as keyof typeof priorityOrder] || 2);
        default:
          return 0;
      }
    });

    setFilteredJobs(filtered);
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadJobs();
    setRefreshing(false);
  }, [loadJobs]);

  const getJobCounts = () => {
    const active = jobs.filter(job => ['PENDING', 'ACCEPTED', 'IN_PROGRESS'].includes(job.status)).length;
    const completed = jobs.filter(job => job.status === 'COMPLETED').length;
    const pending = jobs.filter(job => job.status === 'PENDING').length;
    
    return { active, completed, pending, total: jobs.length };
  };

  const jobCounts = getJobCounts();

  const FilterChip = ({ 
    label, 
    value, 
    count, 
    isActive, 
    onPress 
  }: {
    label: string;
    value: string;
    count: number;
    isActive: boolean;
    onPress: () => void;
  }) => (
    <Pressable onPress={onPress}>
      <Badge
        colorScheme={isActive ? 'primary' : 'gray'}
        variant={isActive ? 'solid' : 'outline'}
        size="lg"
        rounded="full"
        px={4}
        py={2}
        _pressed={{
          bg: isActive ? 'primary.600' : 'gray.100',
        }}
      >
        <Text
          fontSize="sm"
          fontWeight="medium"
          color={isActive ? 'white' : 'gray.600'}
        >
          {label} ({count})
        </Text>
      </Badge>
    </Pressable>
  );

  const EmptyState = () => (
    <Box flex={1} justifyContent="center" alignItems="center" py={12}>
      <Icon
        as={MaterialIcons}
        name="work-outline"
        size="4xl"
        color="gray.300"
        mb={4}
      />
      <Text fontSize="xl" fontWeight="semibold" color="gray.600" mb={2}>
        {searchQuery 
          ? 'No jobs found'
          : selectedFilter === 'all'
          ? 'No jobs yet'
          : `No ${selectedFilter} jobs`
        }
      </Text>
      <Text fontSize="md" color="gray.500" textAlign="center" mb={6}>
        {searchQuery 
          ? `No jobs match "${searchQuery}"`
          : selectedFilter === 'all'
          ? 'Book your first service to get started'
          : `You don't have any ${selectedFilter} jobs`
        }
      </Text>
      {!searchQuery && selectedFilter === 'all' && (
        <Button
          colorScheme="primary"
          size="lg"
          onPress={() => navigation.navigate('BookJob')}
          leftIcon={<Icon as={MaterialIcons} name="add" size="sm" />}
        >
          Book Your First Service
        </Button>
      )}
    </Box>
  );

  if (loading) {
    return (
      <Box flex={1} bg="gray.50" safeArea>
        <VStack space={4} p={4}>
          <Skeleton h="16" rounded="xl" />
          <HStack space={3}>
            <Skeleton flex={1} h="10" rounded="full" />
            <Skeleton flex={1} h="10" rounded="full" />
            <Skeleton flex={1} h="10" rounded="full" />
          </HStack>
          <Skeleton h="32" rounded="xl" />
          <Skeleton h="32" rounded="xl" />
          <Skeleton h="32" rounded="xl" />
        </VStack>
      </Box>
    );
  }

  return (
    <Box flex={1} bg="gray.50" safeArea>
      {/* Header */}
      <Box bg="white" shadow={1}>
        <VStack space={4} p={4}>
          <HStack justifyContent="space-between" alignItems="center">
            <VStack>
              <Text fontSize="2xl" fontWeight="bold" color="gray.800">
                My Jobs
              </Text>
              <Text fontSize="sm" color="gray.600">
                {jobCounts.total} total jobs
              </Text>
            </VStack>
            <Button
              variant="ghost"
              onPress={() => navigation.navigate('BookJob')}
              leftIcon={<Icon as={MaterialIcons} name="add" size="sm" />}
            >
              New Job
            </Button>
          </HStack>

          {/* Search */}
          <Input
            placeholder="Search jobs..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            leftElement={
              <Icon
                as={MaterialIcons}
                name="search"
                size="sm"
                color="gray.400"
                ml={3}
              />
            }
            rightElement={
              searchQuery.length > 0 ? (
                <Pressable onPress={() => setSearchQuery('')} mr={3}>
                  <Icon
                    as={MaterialIcons}
                    name="close"
                    size="sm"
                    color="gray.400"
                  />
                </Pressable>
              ) : undefined
            }
            bg="gray.50"
            borderColor="gray.200"
            _focus={{
              bg: 'white',
              borderColor: 'primary.500',
            }}
          />

          {/* Filter Chips */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <HStack space={3} px={1}>
              <FilterChip
                label="All"
                value="all"
                count={jobCounts.total}
                isActive={selectedFilter === 'all'}
                onPress={() => setSelectedFilter('all')}
              />
              <FilterChip
                label="Active"
                value="active"
                count={jobCounts.active}
                isActive={selectedFilter === 'active'}
                onPress={() => setSelectedFilter('active')}
              />
              <FilterChip
                label="Completed"
                value="completed"
                count={jobCounts.completed}
                isActive={selectedFilter === 'completed'}
                onPress={() => setSelectedFilter('completed')}
              />
              <FilterChip
                label="Pending"
                value="pending"
                count={jobCounts.pending}
                isActive={selectedFilter === 'pending'}
                onPress={() => setSelectedFilter('pending')}
              />
            </HStack>
          </ScrollView>

          {/* Sort Options */}
          <HStack justifyContent="space-between" alignItems="center">
            <Text fontSize="sm" color="gray.600">
              {filteredJobs.length} jobs found
            </Text>
            <Select
              selectedValue={sortBy}
              onValueChange={setSortBy}
              placeholder="Sort by"
              minWidth="120"
              size="sm"
              variant="filled"
            >
              <Select.Item label="Date" value="date" />
              <Select.Item label="Status" value="status" />
              <Select.Item label="Priority" value="priority" />
            </Select>
          </HStack>
        </VStack>
      </Box>

      {/* Jobs List */}
      <ScrollView
        flex={1}
        px={4}
        py={4}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {filteredJobs.length === 0 ? (
          <EmptyState />
        ) : (
          <VStack space={3}>
            {filteredJobs.map((job) => (
              <JobStatusCard
                key={job.id}
                job={{
                  id: job.id,
                  title: job.title,
                  description: job.description,
                  status: job.status.toLowerCase() as any,
                  priority: (job.priority || 'medium') as any,
                  scheduledDate: job.scheduledAt || job.createdAt,
                  estimatedDuration: '2-3 hours',
                  address: job.address || 'Address not specified',
                  price: 85, // This should come from the job data
                  technician: job.technician ? {
                    id: job.technician.id,
                    name: job.technician.user.fullName,
                    phone: '+44 7700 900123', // This should come from the technician data
                    rating: job.technician.rating || 4.5,
                  } : undefined,
                  progress: job.status === 'IN_PROGRESS' ? 65 : undefined,
                }}
                onPress={() => navigation.navigate('JobDetails', { jobId: job.id })}
                onContactPress={() => {
                  if (job.technician) {
                    // Handle contact technician
                    toast.show({
                      title: 'Contact',
                      description: `Contacting ${job.technician.user.fullName}`,
                      status: 'info',
                    });
                  }
                }}
                onTrackPress={() => navigation.navigate('JobDetails', { jobId: job.id })}
                variant="default"
              />
            ))}
            
            {/* Bottom spacing */}
            <Box h={4} />
          </VStack>
        )}
      </ScrollView>
    </Box>
  );
};

export default ModernMyJobsScreenNB;