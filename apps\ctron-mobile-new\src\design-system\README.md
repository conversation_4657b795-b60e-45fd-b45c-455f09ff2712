# CTRON Home Design System

A comprehensive design system for the CTRON Home mobile application, providing consistent UI components, design tokens, and utilities.

## Overview

The CTRON Home Design System ensures visual consistency, improves development efficiency, and maintains a cohesive user experience across the entire application.

## Features

- 🎨 **Consistent Design Tokens** - Colors, typography, spacing, and more
- 🧩 **Reusable Components** - Pre-built, tested UI components
- 📱 **Mobile-First** - Optimized for React Native
- 🔧 **TypeScript Support** - Full type safety and IntelliSense
- 🎯 **Accessibility** - Built with accessibility best practices
- 🚀 **Performance** - Optimized for mobile performance

## Installation

```typescript
import { Button, Card, Typography, tokens } from '../design-system';
```

## Design Tokens

### Colors

```typescript
import { tokens } from '../design-system';

// Primary colors
tokens.colors.primary[500] // Main brand color
tokens.colors.primary[50]  // Light variant
tokens.colors.primary[900] // Dark variant

// Semantic colors
tokens.colors.success[500] // Success state
tokens.colors.warning[500] // Warning state
tokens.colors.error[500]   // Error state
tokens.colors.info[500]    // Info state
```

### Typography

```typescript
// Font sizes
tokens.typography.fontSize.xs    // 12px
tokens.typography.fontSize.sm    // 14px
tokens.typography.fontSize.base  // 16px
tokens.typography.fontSize.lg    // 18px
tokens.typography.fontSize.xl    // 20px

// Font weights
tokens.typography.fontWeight.normal    // 400
tokens.typography.fontWeight.medium    // 500
tokens.typography.fontWeight.semibold  // 600
tokens.typography.fontWeight.bold      // 700
```

### Spacing

```typescript
// Spacing scale (4px base unit)
tokens.spacing[1]  // 4px
tokens.spacing[2]  // 8px
tokens.spacing[3]  // 12px
tokens.spacing[4]  // 16px
tokens.spacing[6]  // 24px
tokens.spacing[8]  // 32px
```

## Components

### Button

Standardized button component with multiple variants and sizes.

```typescript
import { Button } from '../design-system';

<Button
  title="Primary Action"
  onPress={() => console.log('Pressed')}
  variant="primary"
  size="md"
/>

<Button
  title="Secondary Action"
  onPress={() => console.log('Pressed')}
  variant="outline"
  size="lg"
  loading={isLoading}
/>
```

**Props:**
- `title: string` - Button text
- `onPress: () => void` - Press handler
- `variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'`
- `size?: 'sm' | 'md' | 'lg'`
- `disabled?: boolean`
- `loading?: boolean`
- `fullWidth?: boolean`
- `icon?: React.ReactNode`

### Card

Flexible container component for grouping related content.

```typescript
import { Card, HeroCard, ContentCard } from '../design-system';

<Card variant="elevated" padding={4}>
  <Text>Card content</Text>
</Card>

<HeroCard>
  <Text>Hero content with large shadow</Text>
</HeroCard>

<ContentCard>
  <Text>Standard content card</Text>
</ContentCard>
```

**Props:**
- `children: React.ReactNode`
- `variant?: 'elevated' | 'outlined' | 'filled'`
- `padding?: keyof typeof tokens.spacing`
- `borderRadius?: keyof typeof tokens.borderRadius`
- `shadow?: keyof typeof tokens.shadows`

### Typography

Consistent text styling with semantic variants.

```typescript
import { Typography, Heading, Body, Caption } from '../design-system';

<Typography variant="h1">Main Heading</Typography>
<Typography variant="body1" color={tokens.colors.neutral[600]}>
  Body text content
</Typography>

<Heading level={2}>Section Heading</Heading>
<Body size={1}>Regular body text</Body>
<Caption>Small caption text</Caption>
```

**Props:**
- `children: React.ReactNode`
- `variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'overline'`
- `color?: string`
- `align?: 'left' | 'center' | 'right' | 'justify'`
- `weight?: keyof typeof tokens.typography.fontWeight`

### Input

Form input component with validation and helper text support.

```typescript
import { Input } from '../design-system';

<Input
  label="Email Address"
  value={email}
  onChangeText={setEmail}
  placeholder="Enter your email"
  keyboardType="email-address"
  error={emailError}
  helperText="We'll never share your email"
/>
```

**Props:**
- `value: string`
- `onChangeText: (text: string) => void`
- `label?: string`
- `placeholder?: string`
- `error?: string`
- `helperText?: string`
- `variant?: 'outlined' | 'filled' | 'underlined'`
- `size?: 'sm' | 'md' | 'lg'`
- `leftIcon?: React.ReactNode`
- `rightIcon?: React.ReactNode`

### BlurCard

Standardized BlurView wrapper that fixes React Native styling issues.

```typescript
import { BlurCard, HeroBlurCard, ContentBlurCard } from '../design-system';

<BlurCard intensity={20} variant="hero">
  <Text>Content over blurred background</Text>
</BlurCard>

<HeroBlurCard>
  <Text>Hero section with blur effect</Text>
</HeroBlurCard>
```

**Props:**
- `children: React.ReactNode`
- `intensity?: number`
- `variant?: 'hero' | 'content' | 'subtle'`
- `borderRadius?: keyof typeof tokens.borderRadius`
- `shadow?: keyof typeof tokens.shadows`

### StatusBadge

Status indicator for job states and general status display.

```typescript
import { StatusBadge } from '../design-system';

<StatusBadge status="COMPLETED" />
<StatusBadge status="IN_PROGRESS" size="lg" />
<StatusBadge status="error" text="Failed" variant="outlined" />
```

**Props:**
- `status: 'PENDING' | 'ACCEPTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'success' | 'warning' | 'error' | 'info'`
- `text?: string`
- `size?: 'sm' | 'md' | 'lg'`
- `variant?: 'filled' | 'outlined' | 'subtle'`

### Avatar

User and company avatar component with fallback initials.

```typescript
import { Avatar, UserAvatar, CompanyAvatar } from '../design-system';

<Avatar
  source={{ uri: 'https://example.com/avatar.jpg' }}
  name="John Doe"
  size="lg"
/>

<UserAvatar name="Jane Smith" size="md" />
<CompanyAvatar name="CTRON" variant="rounded" />
```

**Props:**
- `source?: { uri: string } | number`
- `name?: string`
- `size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'`
- `variant?: 'circular' | 'rounded' | 'square'`

### IconButton

Icon-only button for actions and navigation.

```typescript
import { IconButton, PrimaryIconButton, GhostIconButton } from '../design-system';
import { Icon } from 'react-native-vector-icons/MaterialIcons';

<IconButton
  icon={<Icon name="favorite" size={24} />}
  onPress={() => console.log('Liked')}
  variant="filled"
  color="primary"
/>

<PrimaryIconButton
  icon={<Icon name="add" size={20} />}
  onPress={handleAdd}
/>
```

**Props:**
- `icon: React.ReactNode`
- `onPress: () => void`
- `variant?: 'filled' | 'outlined' | 'ghost'`
- `size?: 'sm' | 'md' | 'lg'`
- `color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'neutral'`

## Migration Guide

### Replacing BlurView

Replace direct BlurView usage with BlurCard:

```typescript
// Before
<BlurView intensity={20} style={styles.container}>
  <Text>Content</Text>
</BlurView>

// After
<BlurCard intensity={20} style={styles.container}>
  <Text>Content</Text>
</BlurCard>
```

### Using Design Tokens

Replace hardcoded values with design tokens:

```typescript
// Before
const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
});

// After
import { tokens } from '../design-system';

const styles = StyleSheet.create({
  container: {
    padding: tokens.spacing[4],
    backgroundColor: tokens.colors.primary[500],
    borderRadius: tokens.borderRadius.base,
  },
});
```

## Best Practices

1. **Use Design Tokens**: Always use design tokens instead of hardcoded values
2. **Component Composition**: Combine components to create complex UIs
3. **Consistent Spacing**: Use the spacing scale for margins and padding
4. **Semantic Colors**: Use semantic color names (success, error, warning) over specific colors
5. **Accessibility**: Ensure proper contrast ratios and accessibility labels
6. **Performance**: Use preset component variants when possible

## Contributing

When adding new components:

1. Follow the existing component structure
2. Include TypeScript interfaces
3. Use design tokens for styling
4. Add preset variants for common use cases
5. Update the main index.ts export
6. Document the component in this README

## File Structure

```
src/design-system/
├── index.ts                 # Main exports
├── tokens.ts               # Design tokens
├── README.md               # This file
└── components/
    ├── Button.tsx
    ├── Card.tsx
    ├── Typography.tsx
    ├── Input.tsx
    ├── BlurCard.tsx
    ├── StatusBadge.tsx
    ├── Avatar.tsx
    └── IconButton.tsx
```