import React, { useState, useEffect } from 'react';

import { useErrorHandler } from '../hooks/useErrorHandler';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ActivityIndicator } from '../utils/platformUtils';

interface ErrorRecoveryProps {
  error?: Error | null;
  onRetry?: () => void;
  onReset?: () => void;
  context?: string;
  showDetails?: boolean;
  customMessage?: string;
  children?: React.ReactNode;
}

/**
 * Enhanced error recovery component with automatic retry and fallback options
 */
export const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({
  error,
  onRetry,
  onReset,
  context = 'ErrorRecovery',
  showDetails = false,
  customMessage,
  children,
}) => {
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  const { handleError } = useErrorHandler({
    context,
    onRetry: onRetry,
    enableRetry: !!onRetry,
  });

  useEffect(() => {
    if (error) {
      handleError(error);
    }
  }, [error, handleError]);

  const handleRetry = async () => {
    if (retryCount >= maxRetries) {
      Alert.alert(
        'Maximum Retries Reached',
        'The operation has failed multiple times. Please try again later or contact support.',
        [
          { text: 'Reset', onPress: onReset },
          { text: 'OK', style: 'cancel' }
        ]
      );
      return;
    }

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    try {
      await onRetry?.();
    } catch (retryError) {
      handleError(retryError);
    } finally {
      setIsRetrying(false);
    }
  };

  const handleShowDetails = () => {
    if (error) {
      Alert.alert(
        'Error Details',
        `Message: ${String(error.message)}\n\nStack: ${error.stack ? String(error.stack).substring(0, 500) : 'No stack available'}...`,
        [{ text: 'OK' }]
      );
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
      backgroundColor: '#ffffff',
    },
    errorCard: {
      backgroundColor: '#f8f9fa',
      borderRadius: 12,
      padding: 24,
      marginBottom: 24,
      borderWidth: 1,
      borderColor: '#e9ecef',
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      maxWidth: 400,
      width: '100%',
    },
    errorIcon: {
      fontSize: 48,
      textAlign: 'center',
      marginBottom: 16,
    },
    errorTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: '#212529',
      textAlign: 'center',
      marginBottom: 8,
    },
    errorMessage: {
      fontSize: 16,
      color: '#6c757d',
      textAlign: 'center',
      marginBottom: 24,
      lineHeight: 24,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 12,
      flexWrap: 'wrap',
    },
    retryButton: {
      backgroundColor: '#007bff',
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
      minWidth: 100,
      alignItems: 'center',
    },
    retryButtonDisabled: {
      backgroundColor: '#6c757d',
    },
    resetButton: {
      backgroundColor: '#ffffff',
      borderWidth: 1,
      borderColor: '#dee2e6',
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
      minWidth: 100,
      alignItems: 'center',
    },
    detailsButton: {
      backgroundColor: '#ffffff',
      borderWidth: 1,
      borderColor: '#e9ecef',
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 4,
      marginTop: 16,
    },
    buttonText: {
      fontSize: 14,
      fontWeight: '500',
      color: '#ffffff',
    },
    resetButtonText: {
      fontSize: 14,
      fontWeight: '500',
      color: '#212529',
    },
    detailsButtonText: {
      fontSize: 12,
      color: '#6c757d',
    },
    retryInfo: {
      fontSize: 12,
      color: '#6c757d',
      textAlign: 'center',
      marginTop: 8,
    },
  });

  if (!error) {
    return <>{children}</>;
  }

  const errorMessage = customMessage || String(error.message) || 'An unexpected error occurred';

  return (
    <View style={styles.container}>
      <View style={styles.errorCard}>
        <Text style={styles.errorIcon}>⚠️</Text>
        <Text style={styles.errorTitle}>Something went wrong</Text>
        <Text style={styles.errorMessage}>{errorMessage}</Text>

        <View style={styles.buttonContainer}>
          {onRetry && (
            <TouchableOpacity
              style={[
                styles.retryButton,
                (isRetrying || retryCount >= maxRetries) && styles.retryButtonDisabled
              ]}
              onPress={handleRetry}
              disabled={isRetrying || retryCount >= maxRetries}
            >
              {isRetrying ? (
                <ActivityIndicator size="small" color="#ffffff" />
              ) : (
                <Text style={styles.buttonText}>
                  {retryCount >= maxRetries ? 'Max Retries' : 'Try Again'}
                </Text>
              )}
            </TouchableOpacity>
          )}

          {onReset && (
            <TouchableOpacity style={styles.resetButton} onPress={onReset}>
              <Text style={styles.resetButtonText}>Reset</Text>
            </TouchableOpacity>
          )}
        </View>

        {retryCount > 0 && (
          <Text style={styles.retryInfo}>
            Retry attempt: {retryCount}/{maxRetries}
          </Text>
        )}

        {showDetails && __DEV__ && (
          <TouchableOpacity style={styles.detailsButton} onPress={handleShowDetails}>
            <Text style={styles.detailsButtonText}>Show Details (Dev)</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default ErrorRecovery;

