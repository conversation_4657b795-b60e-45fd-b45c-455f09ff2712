/**
 * Platform Utilities
 * 
 * This file provides a consistent way to access the Platform API
 * and other React Native components to avoid TypeScript errors with direct imports.
 */

// Import components from react-native

// Import the actual components/modules
// eslint-disable-next-line @typescript-eslint/no-var-requires
const ReactNative = require('react-native');

// Extract components from ReactNative
const Platform = ReactNative.Platform;
const Alert = ReactNative.Alert;
const Text = ReactNative.Text;
const StyleSheet = ReactNative.StyleSheet;
const View = ReactNative.View;
const TouchableOpacity = ReactNative.TouchableOpacity;
const Linking = ReactNative.Linking;
const FlatList = ReactNative.FlatList;
const ScrollView = ReactNative.ScrollView;
const TextInput = ReactNative.TextInput;
const ActivityIndicator = ReactNative.ActivityIndicator;
const Image = ReactNative.Image;
const Dimensions = ReactNative.Dimensions;
const KeyboardAvoidingView = ReactNative.KeyboardAvoidingView;
const RefreshControl = ReactNative.RefreshControl;
const Animated = ReactNative.Animated;
const Easing = ReactNative.Easing;
const StatusBar = ReactNative.StatusBar;
const SafeAreaView = ReactNative.SafeAreaView;
const Appearance = ReactNative.Appearance;

// Re-export components and types
export { Platform, Alert, Text, StyleSheet, View, TouchableOpacity, FlatList, ScrollView, TextInput, ActivityIndicator, Image, Dimensions, KeyboardAvoidingView, RefreshControl, Animated, Easing, StatusBar, SafeAreaView, Appearance, Linking };

// Re-export TypeScript types
export type {
  ViewStyle,
  TextStyle,
  ImageStyle,
  TextInputProps,
  ViewProps,
  TextProps,
  ScrollViewProps,
  FlatListProps
} from 'react-native';

// Export common platform detection utilities
export const isWeb = false; // Web support disabled
export const isIOS = Platform.OS === 'ios';
export const isAndroid = Platform.OS === 'android';
export const isNative = isIOS || isAndroid;

// Helper function to run platform-specific code
export function runOnPlatform<T>(
  config: {
    ios?: () => T;
    android?: () => T;
    native?: () => T;
    default?: () => T;
  }
): T | undefined {
  if (isIOS && config.ios) {
    return config.ios();
  } else if (isAndroid && config.android) {
    return config.android();
  } else if (isNative && config.native) {
    return config.native();
  } else if (config.default) {
    return config.default();
  }
  return undefined;
}

/**
 * Get the appropriate file extension for the current platform
 * @returns The platform-specific file extension (.ios.js, .android.js)
 */
export function getPlatformExtension(): string {
  if (isIOS) return '.ios';
  if (isAndroid) return '.android';
  return '';
}

/**
 * Safely require a module with platform-specific fallbacks
 * @deprecated Use the more robust dynamicImport utility from dynamicImport.ts instead
 */
// Deprecated: Use Platform.select with static imports instead
// export function requirePlatformSpecific(basePath: string): unknown {
//   console.warn('requirePlatformSpecific is deprecated. Use Platform.select with static imports instead.');
//   return {};
// }