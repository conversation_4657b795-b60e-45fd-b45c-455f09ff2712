// backend/src/types/services.ts
import { JobStatus, Role, NotificationType, NotificationStatus } from '@prisma/client';
import { 
  JobWithRelations, 
  TechnicianProfile, 
  UserProfile, 
  PaymentIntentResponse,
  CreateNotificationRequest,
  LocationCoordinates,
  JobFilters,
  TechnicianFilters,
  PaginationParams,
  PaginatedResponse
} from './api';

// ===== USER SERVICE TYPES =====
export interface IUserService {
  getById(id: string): Promise<UserProfile | null>;
  updateProfile(id: string, data: UpdateUserProfileData): Promise<UserProfile>;
  listUsers(filters?: UserFilters, pagination?: PaginationParams): Promise<PaginatedResponse<UserProfile>>;
  deleteUser(id: string): Promise<void>;
  getUserStats(id: string): Promise<UserStats>;
}

export interface UpdateUserProfileData {
  fullName?: string;
  phone?: string;
  email?: string;
}

export interface UserFilters {
  role?: Role[];
  searchTerm?: string;
  isActive?: boolean;
  createdFrom?: Date;
  createdTo?: Date;
}

export interface UserStats {
  totalJobs: number;
  completedJobs: number;
  totalSpent?: number; // For homeowners
  totalEarned?: number; // For technicians
  averageRating?: number;
  joinedDate: Date;
}

// ===== JOB SERVICE TYPES =====
export interface IJobService {
  createJob(data: CreateJobData): Promise<JobWithRelations>;
  getJobById(id: string): Promise<JobWithRelations | null>;
  getUserJobs(userId: string, filters?: JobFilters, pagination?: PaginationParams): Promise<PaginatedResponse<JobWithRelations>>;
  getTechnicianJobs(technicianId: string, filters?: JobFilters, pagination?: PaginationParams): Promise<PaginatedResponse<JobWithRelations>>;
  updateJob(id: string, data: UpdateJobData): Promise<JobWithRelations>;
  updateJobStatus(id: string, status: JobStatus, proofImageKey?: string): Promise<JobWithRelations>;
  cancelJob(id: string, reason?: string): Promise<JobWithRelations>;
  assignTechnician(jobId: string, technicianId: string): Promise<JobWithRelations>;
  getAvailableJobs(technicianId: string, location?: LocationCoordinates): Promise<JobWithRelations[]>;
  getJobStats(filters?: JobStatsFilters): Promise<JobStats>;
}

export interface CreateJobData {
  userId: string;
  issue: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
  latitude?: number;
  longitude?: number;
  photoUrl?: string;
  scheduledAt: Date;
  technicianId?: string;
}

export interface UpdateJobData {
  issue?: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
  scheduledAt?: Date;
  photoUrl?: string;
}

export interface JobStatsFilters {
  dateFrom?: Date;
  dateTo?: Date;
  technicianId?: string;
  userId?: string;
  status?: JobStatus[];
}

export interface JobStats {
  total: number;
  byStatus: Record<JobStatus, number>;
  byPriority: Record<'low' | 'medium' | 'high', number>;
  averageCompletionTime: number; // in hours
  totalRevenue: number;
  averageJobValue: number;
}

// ===== TECHNICIAN SERVICE TYPES =====
export interface ITechnicianService {
  createTechnician(data: CreateTechnicianData): Promise<TechnicianProfile>;
  getTechnicianById(id: string): Promise<TechnicianProfile | null>;
  getTechnicianByUserId(userId: string): Promise<TechnicianProfile | null>;
  updateTechnician(id: string, data: UpdateTechnicianData): Promise<TechnicianProfile>;
  updateAvailability(id: string, isAvailable: boolean): Promise<TechnicianProfile>;
  updateLocation(id: string, location: LocationCoordinates): Promise<TechnicianProfile>;
  searchTechnicians(filters: TechnicianFilters, pagination?: PaginationParams): Promise<PaginatedResponse<TechnicianProfile>>;
  getTechnicianStats(id: string): Promise<TechnicianStats>;
  updateKycStatus(id: string, status: KycStatus, qualificationFile?: string): Promise<TechnicianProfile>;
}

export interface CreateTechnicianData {
  userId: string;
  specialization: string;
  qualificationFile?: string;
  latitude?: number;
  longitude?: number;
}

export interface UpdateTechnicianData {
  specialization?: string;
  qualificationFile?: string;
  latitude?: number;
  longitude?: number;
}

export type KycStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

export interface TechnicianStats {
  totalJobs: number;
  completedJobs: number;
  cancelledJobs: number;
  averageRating: number;
  totalReviews: number;
  totalEarnings: number;
  averageJobValue: number;
  responseTime: number; // average in minutes
  completionRate: number; // percentage
}

// ===== PAYMENT SERVICE TYPES =====
export interface IPaymentService {
  createPaymentIntent(jobId: string, amount: number, currency?: string): Promise<PaymentIntentResponse>;
  capturePayment(paymentIntentId: string): Promise<void>;
  refundPayment(paymentIntentId: string, amount?: number, reason?: string): Promise<RefundResponse>;
  getPaymentByJobId(jobId: string): Promise<PaymentWithJob | null>;
  releasePayment(jobId: string): Promise<void>;
  freezePayment(jobId: string, reason: string): Promise<void>;
  unfreezePayment(jobId: string): Promise<void>;
  getPaymentStats(filters?: PaymentStatsFilters): Promise<PaymentStats>;
}

export interface PaymentWithJob {
  id: string;
  jobId: string;
  userId: string;
  amount: number;
  currency: string;
  stripePaymentIntentId: string;
  isReleased: boolean;
  releasedAt: Date | null;
  isFrozen: boolean;
  freezeReason: string | null;
  createdAt: Date;
  job: JobWithRelations;
}

export interface RefundResponse {
  id: string;
  amount: number;
  status: string;
  reason?: string;
}

export interface PaymentStatsFilters {
  dateFrom?: Date;
  dateTo?: Date;
  technicianId?: string;
  userId?: string;
  isReleased?: boolean;
  isFrozen?: boolean;
}

export interface PaymentStats {
  totalAmount: number;
  releasedAmount: number;
  frozenAmount: number;
  pendingAmount: number;
  totalTransactions: number;
  averageTransactionValue: number;
  refundedAmount: number;
  totalRefunds: number;
}

// ===== NOTIFICATION SERVICE TYPES =====
export interface INotificationService {
  createNotification(data: CreateNotificationRequest): Promise<NotificationResponse>;
  sendPushNotification(userId: string, title: string, body: string, data?: Record<string, any>): Promise<void>;
  sendEmailNotification(userId: string, subject: string, content: string): Promise<void>;
  markAsRead(notificationId: string): Promise<void>;
  markAllAsRead(userId: string): Promise<void>;
  getUserNotifications(userId: string, pagination?: PaginationParams): Promise<PaginatedResponse<NotificationResponse>>;
  deleteNotification(id: string): Promise<void>;
  getNotificationStats(userId: string): Promise<NotificationStats>;
}

export interface NotificationResponse {
  id: string;
  userId: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  type: NotificationType;
  status: NotificationStatus;
  isRead: boolean;
  readAt: Date | null;
  sentAt: Date | null;
  createdAt: Date;
}

export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<NotificationType, number>;
  byStatus: Record<NotificationStatus, number>;
}

// ===== CHAT SERVICE TYPES =====
export interface IChatService {
  createChat(jobId: string): Promise<ChatResponse>;
  getChatByJobId(jobId: string): Promise<ChatResponse | null>;
  sendMessage(chatId: string, senderId: string, content: string, attachments?: string): Promise<MessageResponse>;
  getMessages(chatId: string, pagination?: PaginationParams): Promise<PaginatedResponse<MessageResponse>>;
  markMessagesAsRead(chatId: string, userId: string): Promise<void>;
  closeChat(chatId: string): Promise<ChatResponse>;
  getUserChats(userId: string): Promise<ChatResponse[]>;
}

export interface ChatResponse {
  id: string;
  jobId: string;
  status: 'ACTIVE' | 'CLOSED' | 'ARCHIVED';
  createdAt: Date;
  updatedAt: Date;
  participants: Array<{
    userId: string;
    role: Role;
    joinedAt: Date;
    user: UserProfile;
  }>;
  messages?: MessageResponse[];
  unreadCount?: number;
}

export interface MessageResponse {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  attachments?: string;
  read: boolean;
  createdAt: Date;
  sender: UserProfile;
}

// ===== FILE UPLOAD SERVICE TYPES =====
export interface IFileUploadService {
  uploadFile(file: any, folder?: string): Promise<FileUploadResult>;
  uploadMultipleFiles(files: any[], folder?: string): Promise<FileUploadResult[]>;
  deleteFile(key: string): Promise<void>;
  getSignedUrl(key: string, expiresIn?: number): Promise<string>;
  validateFile(file: any): Promise<FileValidationResult>;
}

export interface FileUploadResult {
  url: string;
  key: string;
  filename: string;
  size: number;
  mimetype: string;
  folder?: string;
}

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ===== AI SERVICE TYPES =====
export interface IAIService {
  generateJobDescription(issue: string, context?: string): Promise<string>;
  analyzeTechnicianPerformance(technicianId: string): Promise<PerformanceAnalysis>;
  suggestTechnicians(jobData: CreateJobData): Promise<TechnicianSuggestion[]>;
  generateReviewSummary(reviews: ReviewResponse[]): Promise<ReviewSummary>;
  detectIssueCategory(description: string): Promise<IssueCategory>;
}

export interface PerformanceAnalysis {
  overallScore: number;
  strengths: string[];
  improvements: string[];
  recommendations: string[];
  trends: {
    rating: number[];
    completionTime: number[];
    responseTime: number[];
  };
}

export interface TechnicianSuggestion {
  technicianId: string;
  score: number;
  reasons: string[];
  estimatedTime: number;
  distance?: number;
}

export interface ReviewResponse {
  id: string;
  rating: number;
  comment?: string;
  createdAt: Date;
  jobId: string;
  userId: string;
  technicianId?: string;
}

export interface ReviewSummary {
  averageRating: number;
  totalReviews: number;
  sentiment: 'positive' | 'neutral' | 'negative';
  commonThemes: string[];
  keyStrengths: string[];
  areasForImprovement: string[];
}

export interface IssueCategory {
  category: string;
  confidence: number;
  subcategories: string[];
  estimatedDifficulty: 'low' | 'medium' | 'high';
  estimatedDuration: number; // in hours
}