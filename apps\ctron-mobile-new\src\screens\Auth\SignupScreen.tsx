// CTRON Signup Screen - Clean, Professional Design
// Based on reference UI with CTRON branding

import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useRef } from 'react';

import AuthAPI from '../../api/auth.api';
import { BackgroundElements } from '../../components/auth/BackgroundElements';
import { EnhancedButton } from '../../components/auth/EnhancedButton';
import { EnhancedInput } from '../../components/auth/EnhancedInput';
import Logo from '../../components/ui/Logo';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import type { AuthStackParamList } from '../../navigation/AuthStack';
import { View, Text, TouchableOpacity, KeyboardAvoidingView, StatusBar, ScrollView, Platform, SafeAreaView, Dimensions, Animated, Easing, StyleSheet } from '../../utils/platformUtils';

type Role = 'HOMEOWNER' | 'TECHNICIAN';



export default function SignupScreen() {
  const { login } = useAuth();
  const navigation = useNavigation<StackNavigationProp<AuthStackParamList, 'Signup'>>();
  const { theme, isDark } = useTheme();

  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState<Role>('HOMEOWNER');
  const [errors, setErrors] = useState<{
    fullName?: string;
    email?: string;
    password?: string;
    general?: string;
  }>({});
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const logoScaleAnim = useRef(new Animated.Value(0.8)).current;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary,
    },
    keyboardContainer: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: theme.spacing.lg,
    },
    header: {
      alignItems: 'center',
      paddingVertical: theme.spacing.xl,
    },

    brandName: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.text.primary,
      marginBottom: theme.spacing.xs,
    },
    welcomeTitle: {
      fontSize: 16,
      color: theme.colors.text.secondary,
      textAlign: 'center',
    },
    tagline: {
      fontSize: 16,
      color: theme.colors.text.secondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    roleSection: {
      marginBottom: theme.spacing.lg,
    },
    roleContainer: {
      flexDirection: 'row',
      gap: theme.spacing.md,
    },
    roleButton: {
      flex: 1,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.md,
      borderWidth: 2,
      borderColor: theme.colors.border.medium,
      alignItems: 'center',
      backgroundColor: 'transparent',
    },
    roleButtonActive: {
      backgroundColor: theme.colors.primary.main,
      borderColor: theme.colors.primary.main,
    },
    roleIcon: {
      fontSize: 20,
      marginBottom: theme.spacing.xs,
    },
    roleText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text.secondary,
    },
    roleTextActive: {
      color: theme.colors.primary.contrast,
    },
    errorContainer: {
      marginTop: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.error.light,
      borderRadius: theme.borderRadius.sm,
    },
    errorText: {
      color: theme.colors.error.main,
      fontSize: 14,
      textAlign: 'center',
    },
    footer: {
      alignItems: 'center',
      marginTop: theme.spacing.lg,
      paddingBottom: theme.spacing.lg,
    },
    footerText: {
      color: theme.colors.text.secondary,
      fontSize: 14,
    },
    linkButton: {
      marginTop: theme.spacing.xs,
    },
    linkText: {
      color: theme.colors.primary.main,
      fontSize: 14,
      fontWeight: '600',
    },
  });

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.spring(logoScaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);
  
  // Validation functions
  const validateFullName = (value: string) => {
    if (!value.trim()) return 'Full name is required';
    if (value.trim().length < 2) return 'Full name must be at least 2 characters';
    return '';
  };
  

  
  const validateEmail = (value: string) => {
    if (!value.trim()) return 'Email is required';
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return 'Please enter a valid email address';
    }
    return '';
  };
  
  const validatePassword = (value: string) => {
    if (!value) return 'Password is required';
    if (value.length < 6) return 'Password must be at least 6 characters';
    return '';
  };
  
  // Real-time validation handlers
  const handleFullNameChange = (value: string) => {
    setFullName(value);
    if (errors.fullName) {
      const error = validateFullName(value);
      setErrors(prev => ({ ...prev, fullName: error }));
    }
  };
  

  
  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (errors.email) {
      const error = validateEmail(value);
      setErrors(prev => ({ ...prev, email: error }));
    }
  };
  
  const handlePasswordChange = (value: string) => {
    setPassword(value);
    if (errors.password) {
      const error = validatePassword(value);
      setErrors(prev => ({ ...prev, password: error }));
    }
  };

  const handleSignup = async () => {
    // Validate all fields
    const newErrors = {
      fullName: validateFullName(fullName),
      email: validateEmail(email),
      password: validatePassword(password),
    };
    
    setErrors(newErrors);
    
    // Check if there are any validation errors
    const hasErrors = Object.values(newErrors).some(error => error !== '');
    if (hasErrors) {
      return;
    }

    setLoading(true);

    try {
      const res = await AuthAPI.signup({ 
        fullName: fullName.trim(), 
        email: email.trim().toLowerCase(), 
        password, 
        role 
      });

      const { accessToken } = res;
      if (!accessToken) throw new Error('No token returned');

      await login(accessToken);
    } catch (err: unknown) {
      const error = err as { response?: { data?: { message?: string } }; message?: string };
      const errorMessage = error.response?.data?.message || error.message || 'Signup failed';
      setErrors(prev => ({ ...prev, general: errorMessage }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <BackgroundElements variant="signup" />
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor={theme.colors.background.primary} />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardContainer}
      >
        <ScrollView
          contentContainerStyle={[
            styles.scrollContainer,
            {
              justifyContent: 'center',
              minHeight: Dimensions.get('window').height - 100
            }
          ]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header Section */}
          <Animated.View 
            style={[
              styles.header,
              {
                opacity: fadeAnim,
                transform: [
                  { translateY: slideAnim },
                  { scale: logoScaleAnim }
                ]
              }
            ]}
          >
            {/* CTRON Logo */}
            <Logo 
              size={80} 
              color={theme.colors.primary.contrast} 
              backgroundColor={theme.colors.primary.main} 
            />
            <Text style={[styles.brandName, { marginBottom: theme.spacing.sm }]}>CTRON Home</Text>
            <Text style={[styles.tagline, { marginBottom: theme.spacing.xl }]}>Your trusted home services partner</Text>
          </Animated.View>

          {/* Form Section */}
          <Animated.View 
            style={[
              { paddingHorizontal: theme.spacing.sm },
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}
          >
            {/* Role Selection */}
            <View style={styles.roleSection}>
              <View style={styles.roleContainer}>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    role === 'HOMEOWNER' && styles.roleButtonActive,
                  ]}
                  onPress={() => setRole('HOMEOWNER')}
                  accessibilityRole="button"
                  accessibilityState={{ selected: role === 'HOMEOWNER' }}
                >
                  <Text style={styles.roleIcon}>🏠</Text>
                  <Text style={[
                    styles.roleText,
                    role === 'HOMEOWNER' && styles.roleTextActive,
                  ]}>Homeowner</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    role === 'TECHNICIAN' && styles.roleButtonActive,
                  ]}
                  onPress={() => setRole('TECHNICIAN')}
                  accessibilityRole="button"
                  accessibilityState={{ selected: role === 'TECHNICIAN' }}
                >
                  <Text style={styles.roleIcon}>🔧</Text>
                  <Text style={[
                    styles.roleText,
                    role === 'TECHNICIAN' && styles.roleTextActive,
                  ]}>Technician</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Full Name Input */}
            <EnhancedInput
              label="Full Name"
              value={fullName}
              onChangeText={handleFullNameChange}
              error={errors.fullName}
              autoCapitalize="words"
              placeholder="Enter your full name"
              accessibilityLabel="Full name input"
              accessibilityHint="Enter your full name"
            />



            {/* Email Input */}
            <EnhancedInput
              label="Email"
              value={email}
              onChangeText={handleEmailChange}
              error={errors.email}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholder="Enter your email"
              accessibilityLabel="Email input"
              accessibilityHint="Enter your email address"
            />

            {/* Password Input */}
            <EnhancedInput
              label="Password"
              value={password}
              onChangeText={handlePasswordChange}
              error={errors.password}
              secureTextEntry={true}
              showPasswordToggle={true}
              autoCapitalize="none"
              placeholder="Enter your password"
              accessibilityLabel="Password input"
              accessibilityHint="Enter your password"
            />

            {/* Sign Up Button */}
            <EnhancedButton
              title="Create Account"
              onPress={handleSignup}
              loading={loading}
              loadingText="Creating Account..."
              variant="primary"
              size="large"
              accessibilityLabel="Create account"
              style={{ marginTop: theme.spacing.lg }}
            />



            {/* Error Display */}
            {errors.general && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{errors.general}</Text>
              </View>
            )}
          </Animated.View>

          {/* Footer */}
          <Animated.View 
            style={[
              styles.footer,
              { marginTop: theme.spacing.lg, paddingBottom: theme.spacing.lg },
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}
          >
            <Text style={styles.footerText}>Already have an account?</Text>
            <TouchableOpacity
              style={styles.linkButton}
              onPress={() => navigation.navigate('Login')}
              accessibilityRole="button"
              accessibilityLabel="Go to login screen"
            >
              <Text style={styles.linkText}>Log In</Text>
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

