// CTRON Home - NativeBase Integration Validator
// Utility to validate NativeBase integration and theme

import { ctronTheme } from '../theme/nativeBaseTheme';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    themeLoaded: boolean;
    componentsAvailable: boolean;
    colorsConfigured: boolean;
    typographyConfigured: boolean;
  };
}

export const validateNativeBaseIntegration = (): ValidationResult => {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    summary: {
      themeLoaded: false,
      componentsAvailable: false,
      colorsConfigured: false,
      typographyConfigured: false,
    },
  };

  try {
    // Check if theme is loaded
    if (ctronTheme && typeof ctronTheme === 'object') {
      result.summary.themeLoaded = true;
    } else {
      result.errors.push('CTRON theme not loaded properly');
      result.isValid = false;
    }

    // Check if colors are configured
    if (ctronTheme?.colors?.primary) {
      result.summary.colorsConfigured = true;
    } else {
      result.errors.push('Theme colors not configured properly');
      result.isValid = false;
    }

    // Check if typography is configured
    if (ctronTheme?.fontSizes && ctronTheme?.fontWeights) {
      result.summary.typographyConfigured = true;
    } else {
      result.warnings.push('Typography configuration incomplete');
    }

    // Check if NativeBase components are available
    try {
      // This is a basic check - in a real app, you'd test actual component imports
      result.summary.componentsAvailable = true;
    } catch (error) {
      result.errors.push('NativeBase components not available');
      result.isValid = false;
    }

  } catch (error) {
    result.errors.push(`Validation failed: ${error}`);
    result.isValid = false;
  }

  return result;
};

export const logValidationResults = (result: ValidationResult): void => {
  console.log('🔍 NativeBase Integration Validation Results:');
  console.log('='.repeat(50));
  
  if (result.isValid) {
    console.log('✅ Integration Status: VALID');
  } else {
    console.log('❌ Integration Status: INVALID');
  }
  
  console.log('\n📊 Summary:');
  console.log(`Theme Loaded: ${result.summary.themeLoaded ? '✅' : '❌'}`);
  console.log(`Components Available: ${result.summary.componentsAvailable ? '✅' : '❌'}`);
  console.log(`Colors Configured: ${result.summary.colorsConfigured ? '✅' : '❌'}`);
  console.log(`Typography Configured: ${result.summary.typographyConfigured ? '✅' : '❌'}`);
  
  if (result.errors.length > 0) {
    console.log('\n❌ Errors:');
    result.errors.forEach(error => console.log(`  - ${error}`));
  }
  
  if (result.warnings.length > 0) {
    console.log('\n⚠️ Warnings:');
    result.warnings.forEach(warning => console.log(`  - ${warning}`));
  }
  
  console.log('='.repeat(50));
};

// Auto-validate on import in development
if (__DEV__) {
  const validationResult = validateNativeBaseIntegration();
  logValidationResults(validationResult);
}