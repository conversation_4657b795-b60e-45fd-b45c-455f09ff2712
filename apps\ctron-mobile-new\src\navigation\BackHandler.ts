/**
 * BackHandler Polyfill for React Native Web
 * 
 * This module provides a cross-platform BackHandler implementation that works on web.
 * The native BackHandler is not available in React Native web, so we provide a polyfill.
 */


import { navigationRef } from './navigationRef';

// Type definition for the subscription object
type BackHandlerSubscription = {
  remove: () => void;
};

// Type definition for the handler function
type BackHandlerEventHandler = () => boolean | null | undefined;

/**
 * BackHandler polyfill for web platform
 * Provides the same API as React Native's BackHandler but with no-op implementations for web
 */
const _RNBackHandler = {
  addEventListener: (_eventName: string, _handler: BackHandlerEventHandler): BackHandlerSubscription => ({
    remove: () => { /* no-op */ }
  }),
  removeEventListener: (_eventName: string, _handler: BackHandlerEventHandler): void => {
    /* no-op */
  },
  exitApp: (): void => {
    /* no-op */
  },
};

/**
 * Cross-platform BackHandler implementation
 * This provides a consistent API across all platforms
 */
export const BackHandler = {
  /**
   * Attaches a back handler to handle hardware back button presses
   * @returns A subscription object with a remove method to detach the handler
   */
  attach: (): BackHandlerSubscription => {
    // For web, we return a no-op subscription
    // For native, we would use the actual BackHandler.addEventListener
    const subscription: BackHandlerSubscription = {
      remove: () => { /* no-op */ }
    };
    return subscription;
  },

  /**
   * Navigates back using the navigation ref if available
   * This provides a consistent way to handle back navigation across platforms
   */
  goBack: (): void => {
    if (navigationRef.current?.canGoBack()) {
      navigationRef.current.goBack();
    }
  }
};
