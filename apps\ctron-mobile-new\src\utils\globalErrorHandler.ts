// Global Error Handler - Based on research findings
import { debugLogger } from './debugLogger';
import { Alert } from './platformUtils';

// Global error handler for unhandled promise rejections
export const setupGlobalErrorHandling = () => {
  // Handle unhandled promise rejections for React Native
  const originalHandler = (global as any).ErrorUtils?.getGlobalHandler();
  
  (global as any).ErrorUtils?.setGlobalHandler((error: Error, isFatal: boolean) => {
    debugLogger.error('🚨 Global Error:', error);
    
    if (__DEV__) {
      Alert.alert(
        'Global Error',
        'An unexpected error occurred',
          [{ text: 'OK' }]
        );
      }
      
      // Call original handler if it exists
      if (originalHandler) {
        originalHandler(error, isFatal);
      }
    });
};

// Export a function to handle promise rejections manually
export const handlePromiseRejection = (error: any, context?: string) => {
  debugLogger.error('🚨 Promise Rejection' + (context ? ` in ${context}` : '') + ':', error);
  
  if (__DEV__) {
    Alert.alert(
      'Promise Rejection',
      `An error occurred${context ? ` in ${context}` : ''}`,
      [{ text: 'OK' }]
    );
  }
};
