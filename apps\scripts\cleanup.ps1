# CTRON Home - Codebase Cleanup Script (PowerShell)
# This script performs routine cleanup tasks across the entire codebase

param(
    [switch]$Deep
)

Write-Host "🧹 Starting CTRON Home codebase cleanup..." -ForegroundColor Green

# Remove build artifacts
Write-Host "📦 Cleaning build artifacts..." -ForegroundColor Yellow
Remove-Item -Recurse -Force -ErrorAction SilentlyContinue backend/dist
Remove-Item -Recurse -Force -ErrorAction SilentlyContinue web/dist
Remove-Item -Recurse -Force -ErrorAction SilentlyContinue ctron-mobile-new/dist
Remove-Item -Recurse -Force -ErrorAction SilentlyContinue ctron-mobile-new/.expo
Remove-Item -Recurse -Force -ErrorAction SilentlyContinue ctron-mobile-new/.metro-cache

# Clean node_modules if requested
if ($Deep) {
    Write-Host "🗑️  Deep clean: removing node_modules..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force -ErrorAction SilentlyContinue backend/node_modules
    Remove-Item -Recurse -Force -ErrorAction SilentlyContinue web/node_modules
    Remove-Item -Recurse -Force -ErrorAction SilentlyContinue ctron-mobile-new/node_modules
    
    Write-Host "📥 Reinstalling dependencies..." -ForegroundColor Yellow
    Set-Location backend
    npm install
    Set-Location ../web
    npm install
    Set-Location ../ctron-mobile-new
    yarn install
    Set-Location ..
}

# Clean temporary files
Write-Host "🧽 Cleaning temporary files..." -ForegroundColor Yellow
Get-ChildItem -Recurse -Name "*.log" | Remove-Item -Force -ErrorAction SilentlyContinue
Get-ChildItem -Recurse -Name ".DS_Store" | Remove-Item -Force -ErrorAction SilentlyContinue
Get-ChildItem -Recurse -Name "Thumbs.db" | Remove-Item -Force -ErrorAction SilentlyContinue
Get-ChildItem -Recurse -Name "*.tmp" | Remove-Item -Force -ErrorAction SilentlyContinue

# Clean TypeScript build info
Get-ChildItem -Recurse -Name "*.tsbuildinfo" | Remove-Item -Force -ErrorAction SilentlyContinue

Write-Host "✅ Cleanup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Cleanup Summary:" -ForegroundColor Cyan
Write-Host "   - Build artifacts removed"
Write-Host "   - Temporary files cleaned"
Write-Host "   - TypeScript build cache cleared"

if ($Deep) {
    Write-Host "   - Dependencies reinstalled"
}

Write-Host ""
Write-Host "💡 Usage:" -ForegroundColor Cyan
Write-Host "   .\scripts\cleanup.ps1        # Standard cleanup"
Write-Host "   .\scripts\cleanup.ps1 -Deep  # Deep clean with dependency reinstall"