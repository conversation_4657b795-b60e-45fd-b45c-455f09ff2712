// FS polyfill for React Native
// This provides a minimal fs implementation for Node.js modules

const fs = {
  // Sync methods (throw errors since not available in RN)
  readFileSync: () => {
    throw new Error('fs.readFileSync is not available in React Native');
  },
  
  writeFileSync: () => {
    throw new Error('fs.writeFileSync is not available in React Native');
  },
  
  existsSync: () => false,
  
  statSync: () => {
    throw new Error('fs.statSync is not available in React Native');
  },
  
  // Async methods (provide no-op implementations)
  readFile: (path, options, callback) => {
    if (typeof options === 'function') {
      callback = options;
    }
    if (callback) {
      setTimeout(() => callback(new Error('fs.readFile is not available in React Native')), 0);
    }
  },
  
  writeFile: (path, data, options, callback) => {
    if (typeof options === 'function') {
      callback = options;
    }
    if (callback) {
      setTimeout(() => callback(new Error('fs.writeFile is not available in React Native')), 0);
    }
  },
  
  access: (path, mode, callback) => {
    if (typeof mode === 'function') {
      callback = mode;
    }
    if (callback) {
      setTimeout(() => callback(new Error('fs.access is not available in React Native')), 0);
    }
  },
  
  stat: (path, callback) => {
    if (callback) {
      setTimeout(() => callback(new Error('fs.stat is not available in React Native')), 0);
    }
  },
  
  // Constants
  constants: {
    F_OK: 0,
    R_OK: 4,
    W_OK: 2,
    X_OK: 1
  }
};

module.exports = fs;