#!/usr/bin/env node

/**
 * CTRON Mobile - NativeBase Issue Fixer
 * 
 * This script automatically fixes common NativeBase integration issues
 * Run with: node scripts/fix-nativebase-issues.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 CTRON NativeBase Issue Fixer');
console.log('================================');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ Error: Please run this script from the project root directory');
  process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

// Check if NativeBase is installed
if (!packageJson.dependencies['native-base']) {
  console.error('❌ Error: NativeBase is not installed');
  process.exit(1);
}

console.log('✅ NativeBase found in dependencies');

// Fix 1: Ensure react-dom is installed
console.log('\n🔍 Checking react-dom dependency...');
if (!packageJson.dependencies['react-dom']) {
  console.log('📦 Adding react-dom dependency...');
  try {
    execSync('yarn add react-dom@18.3.1', { stdio: 'inherit' });
    console.log('✅ react-dom added successfully');
  } catch (error) {
    console.error('❌ Failed to add react-dom:', error.message);
  }
} else {
  console.log('✅ react-dom already installed');
}

// Fix 2: Check metro config
console.log('\n🔍 Checking metro configuration...');
const metroConfigPath = 'metro.config.js';
if (fs.existsSync(metroConfigPath)) {
  const metroConfig = fs.readFileSync(metroConfigPath, 'utf8');
  
  // Check if react-dom shim is configured
  if (metroConfig.includes('react-dom-shim.js')) {
    console.log('✅ Metro config includes react-dom shim');
  } else {
    console.log('⚠️  Metro config might need react-dom shim configuration');
  }
  
  // Check if NativeBase packages are in transpilePackages
  if (metroConfig.includes('native-base') && metroConfig.includes('@react-aria/utils')) {
    console.log('✅ Metro config includes NativeBase transpile packages');
  } else {
    console.log('⚠️  Metro config might need NativeBase transpile packages');
  }
} else {
  console.log('❌ Metro config not found');
}

// Fix 3: Check if react-dom shim exists
console.log('\n🔍 Checking react-dom shim...');
const shimPath = 'react-dom-shim.js';
if (fs.existsSync(shimPath)) {
  console.log('✅ react-dom shim exists');
} else {
  console.log('❌ react-dom shim not found - this might cause bundling issues');
}

// Fix 4: Check NativeBase theme setup
console.log('\n🔍 Checking NativeBase theme setup...');
const themePath = 'src/theme/nativeBaseTheme.ts';
if (fs.existsSync(themePath)) {
  console.log('✅ NativeBase theme file exists');
} else {
  console.log('❌ NativeBase theme file not found');
}

// Fix 5: Check App.tsx for NativeBaseProvider
console.log('\n🔍 Checking App.tsx configuration...');
const appPaths = ['App.tsx', 'src/App.tsx', 'App.js', 'src/App.js'];
let appPath = null;

for (const path of appPaths) {
  if (fs.existsSync(path)) {
    appPath = path;
    break;
  }
}

if (appPath) {
  const appContent = fs.readFileSync(appPath, 'utf8');
  
  if (appContent.includes('NativeBaseProvider')) {
    console.log('✅ App.tsx includes NativeBaseProvider');
  } else {
    console.log('❌ App.tsx missing NativeBaseProvider - components won\'t render correctly');
  }
  
  if (appContent.includes('ctronTheme') || appContent.includes('theme=')) {
    console.log('✅ App.tsx includes theme configuration');
  } else {
    console.log('⚠️  App.tsx might be missing theme configuration');
  }
} else {
  console.log('❌ App.tsx not found');
}

// Fix 6: Clear caches
console.log('\n🧹 Clearing caches...');
try {
  // Clear Metro cache
  if (fs.existsSync('node_modules/.cache')) {
    execSync('rm -rf node_modules/.cache', { stdio: 'inherit' });
    console.log('✅ Metro cache cleared');
  }
  
  // Clear Expo cache
  try {
    execSync('npx expo start --clear --no-dev', { stdio: 'pipe', timeout: 5000 });
  } catch (error) {
    // This is expected to timeout, we just want to clear the cache
  }
  console.log('✅ Expo cache cleared');
  
} catch (error) {
  console.log('⚠️  Cache clearing had some issues, but continuing...');
}

// Fix 7: Validate NativeBase setup
console.log('\n🔍 Validating NativeBase setup...');
try {
  const validatorPath = 'src/utils/nativeBaseValidator.ts';
  if (fs.existsSync(validatorPath)) {
    // Try to run the validator
    execSync('node -e "const { validateNativeBaseSetup } = require(\'./src/utils/nativeBaseValidator\'); validateNativeBaseSetup();"', { stdio: 'inherit' });
  } else {
    console.log('⚠️  NativeBase validator not found');
  }
} catch (error) {
  console.log('⚠️  NativeBase validator had issues:', error.message);
}

// Summary and recommendations
console.log('\n📋 Summary and Recommendations');
console.log('==============================');

console.log('\n✅ Quick fixes applied:');
console.log('  - Checked react-dom dependency');
console.log('  - Verified metro configuration');
console.log('  - Cleared caches');
console.log('  - Validated setup');

console.log('\n🚀 Next steps:');
console.log('  1. Run: yarn start');
console.log('  2. If issues persist, run: yarn start:reset');
console.log('  3. Check the troubleshooting guide: NATIVEBASE_TROUBLESHOOTING_GUIDE.md');

console.log('\n🔧 If you still have issues:');
console.log('  - Check console for specific error messages');
console.log('  - Try: yarn fix:deps (reinstall dependencies)');
console.log('  - Try: yarn fix:metro (clear metro cache)');
console.log('  - Review: NATIVEBASE_TROUBLESHOOTING_GUIDE.md');

console.log('\n🎉 NativeBase issue fixer completed!');
console.log('   Run "yarn start" to test your app.');