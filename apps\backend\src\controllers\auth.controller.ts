// backend/src/controllers/auth.controller.ts
import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { prisma } from '../config/db';
import { env } from '../config/env';
import { AuthenticatedRequest } from '../middleware/auth.middleware';
import { logger } from '../utils/logger';
import { User } from '@prisma/client';
import { AuthResponse, LoginRequest, SignupRequest, RefreshTokenRequest } from '../types/api';

// Token blacklist for logout functionality
const tokenBlacklist = new Set<string>();

// Helper function to generate tokens
const generateTokens = (user: User): { accessToken: string; refreshToken: string } => {
  const payload = {
    userId: user.id,
    fullName: user.fullName,
    email: user.email,
    role: user.role,
  };

  const accessToken = jwt.sign(
    payload,
    env.JWT_SECRET,
    { expiresIn: env.JWT_EXPIRES_IN } as jwt.SignOptions
  );

  const refreshToken = jwt.sign(
    { userId: user.id, tokenId: crypto.randomUUID() },
    env.JWT_REFRESH_SECRET,
    { expiresIn: env.JWT_REFRESH_EXPIRES_IN } as jwt.SignOptions
  );

  return { accessToken, refreshToken };
};

// Helper function to store refresh token
const storeRefreshToken = async (userId: string, refreshToken: string) => {
  const hashedToken = crypto.createHash('sha256').update(refreshToken).digest('hex');

  // Remove old refresh tokens for this user (keep only latest 5)
  await prisma.refreshToken.deleteMany({
    where: {
      userId,
      createdAt: {
        lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Older than 30 days
      },
    },
  });

  // Store new refresh token
  await prisma.refreshToken.create({
    data: {
      userId,
      token: hashedToken,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    },
  });
};

export const AuthController = {
  async signup(req: Request, res: Response): Promise<void> {
    try {
      const { fullName, email, phone, password, role } = req.body;

      // Check if user already exists
      const existing = await prisma.user.findUnique({ where: { email } });
      if (existing) {
        res.status(400).json({
          success: false,
          message: 'Email already in use'
        });
        return;
      }

      // Hash password with higher cost for better security
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create user
      const user = await prisma.user.create({
        data: { fullName, email, phone, password: hashedPassword, role },
      });

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens(user);

      // Store refresh token
      await storeRefreshToken(user.id, refreshToken);

      logger.info('User registered successfully', {
        userId: user.id,
        email: user.email,
        role: user.role
      });

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        accessToken,
        refreshToken,
        user: {
          id: user.id,
          fullName: user.fullName,
          email: user.email,
          phone: user.phone,
          role: user.role,
        },
      });
    } catch (error) {
      logger.error('Signup error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during registration'
      });
    }
  },

  async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password } = req.body;

      // Find user with rate limiting check
      const user = await prisma.user.findUnique({ where: { email } });
      if (!user) {
        // Use same response time to prevent user enumeration
        await new Promise(resolve => setTimeout(resolve, 100));
        res.status(400).json({
          success: false,
          message: 'Invalid credentials'
        });
        return;
      }

      // Verify password
      const valid = await bcrypt.compare(password, user.password);
      if (!valid) {
        logger.warn('Failed login attempt', { email, ip: req.ip });
        res.status(400).json({
          success: false,
          message: 'Invalid credentials'
        });
        return;
      }

      // Generate tokens
      const { accessToken, refreshToken } = generateTokens(user);

      // Store refresh token
      await storeRefreshToken(user.id, refreshToken);

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      logger.info('User logged in successfully', {
        userId: user.id,
        email: user.email,
        ip: req.ip
      });

      res.status(200).json({
        success: true,
        message: 'Login successful',
        accessToken,
        refreshToken,
        user: {
          id: user.id,
          fullName: user.fullName,
          email: user.email,
          phone: user.phone,
          role: user.role,
        },
      });
    } catch (error) {
      logger.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during login'
      });
    }
  },

  async getProfile(req: Request, res: Response): Promise<void> {
    try {
      const userId = (req as AuthenticatedRequest).user?.userId;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthenticated'
        });
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          fullName: true,
          phone: true,
          role: true,
          createdAt: true,
          lastLoginAt: true,
        },
      });

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        user
      });
    } catch (error) {
      logger.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      const userId = (req as AuthenticatedRequest).user?.userId;
      const { fullName, phone, email } = req.body;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthenticated'
        });
        return;
      }

      // Check if email is being changed and if it's already in use
      if (email) {
        const existingUser = await prisma.user.findFirst({
          where: {
            email,
            id: { not: userId }
          }
        });

        if (existingUser) {
          res.status(400).json({
            success: false,
            message: 'Email already in use'
          });
          return;
        }
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          ...(fullName && { fullName }),
          ...(phone && { phone }),
          ...(email && { email }),
        },
        select: {
          id: true,
          email: true,
          fullName: true,
          phone: true,
          role: true,
        },
      });

      logger.info('User profile updated', { userId, updatedFields: Object.keys(req.body) });

      res.status(200).json({
        success: true,
        message: 'Profile updated successfully',
        user: updatedUser
      });
    } catch (error) {
      logger.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        res.status(400).json({
          success: false,
          message: 'Refresh token is required'
        });
        return;
      }

      // Verify refresh token
      let decoded: { userId: string; tokenId: string; iat: number; exp: number };
      try {
        decoded = jwt.verify(refreshToken, env.JWT_REFRESH_SECRET) as { userId: string; tokenId: string; iat: number; exp: number };
      } catch (error) {
        res.status(401).json({
          success: false,
          message: 'Invalid refresh token'
        });
        return;
      }

      // Check if refresh token exists in database
      const hashedToken = crypto.createHash('sha256').update(refreshToken).digest('hex');
      const storedToken = await prisma.refreshToken.findFirst({
        where: {
          userId: decoded.userId,
          token: hashedToken,
          expiresAt: { gt: new Date() },
        },
      });

      if (!storedToken) {
        res.status(401).json({
          success: false,
          message: 'Refresh token not found or expired'
        });
        return;
      }

      // Get user
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
      });

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // Generate new tokens
      const { accessToken, refreshToken: newRefreshToken } = generateTokens(user);

      // Remove old refresh token and store new one
      await prisma.refreshToken.delete({ where: { id: storedToken.id } });
      await storeRefreshToken(user.id, newRefreshToken);

      logger.info('Token refreshed successfully', { userId: user.id });

      res.status(200).json({
        success: true,
        accessToken,
        refreshToken: newRefreshToken,
      });
    } catch (error) {
      logger.error('Refresh token error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  async logout(req: Request, res: Response): Promise<void> {
    try {
      const userId = (req as AuthenticatedRequest).user?.userId;
      const authHeader = req.headers.authorization;
      const token = authHeader?.split(' ')[1];

      if (!userId || !token) {
        res.status(401).json({
          success: false,
          message: 'Unauthenticated'
        });
        return;
      }

      // Add token to blacklist
      tokenBlacklist.add(token);

      // Remove all refresh tokens for this user
      await prisma.refreshToken.deleteMany({
        where: { userId },
      });

      logger.info('User logged out successfully', { userId });

      res.status(200).json({
        success: true,
        message: 'Logged out successfully'
      });
    } catch (error) {
      logger.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },

  // Helper method to check if token is blacklisted
  isTokenBlacklisted(token: string): boolean {
    return tokenBlacklist.has(token);
  },

  // Forgot password handler
  forgotPassword: async (req: Request, res: Response): Promise<void> => {
    try {
      const { email } = req.body;

      if (!email) {
        res.status(400).json({
          success: false,
          message: 'Email is required'
        });
        return;
      }

      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { email },
      });

      // Always return success even if user doesn't exist (security best practice)
      if (!user) {
        logger.info(`Password reset requested for non-existent email: ${email}`);
        res.status(200).json({
          success: true,
          message: 'If your email is registered, you will receive a password reset link'
        });
        return;
      }

      // Generate password reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      
      // In a real app, we would store this token in the database
      // and send an email with a reset link
      
      // For now, just log it
      logger.info(`Password reset token for ${email}: ${resetToken}`);

      res.status(200).json({
        success: true,
        message: 'If your email is registered, you will receive a password reset link'
      });
    } catch (error) {
      logger.error('Forgot password error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  },
};
