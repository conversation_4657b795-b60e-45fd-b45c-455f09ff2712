// 📁 File: apps/web/src/hooks/useSocket.ts

import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { config } from '../config/env';

const SOCKET_URL = config.SOCKET_URL; // Use environment configuration

let socket: Socket | null = null;

const useSocket = (onTechnicianUpdate: (data: any) => void) => {
  const isConnected = useRef(false);

  useEffect(() => {
    if (!isConnected.current) {
      const token = localStorage.getItem('token');

      socket = io(SOCKET_URL, {
        auth: { token },
        transports: ['websocket'],
      });

      socket.on('connect', () => {
        isConnected.current = true;
      });

      socket.on('disconnect', () => {
        isConnected.current = false;
      });

      socket.on('technician_update', onTechnicianUpdate);
    }

    return () => {
      socket?.off('technician_update', onTechnicianUpdate);
    };
  }, [onTechnicianUpdate]);
};

export default useSocket;
