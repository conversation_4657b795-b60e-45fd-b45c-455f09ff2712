// CTRON Home - Modern Form Components with NativeBase
// Professional form components for consistent user experience

import { MaterialIcons } from '@expo/vector-icons';
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  TextArea,
  Select,
  FormControl,

  Icon,
  Pressable,
  useTheme,
} from 'native-base';
import React, { useState } from 'react';

// Enhanced Input Component
interface ModernInputProps {
  label: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  isRequired?: boolean;
  isInvalid?: boolean;
  errorMessage?: string;
  helperText?: string;
  leftIcon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  type?: 'text' | 'password' | 'email' | 'phone';
  multiline?: boolean;
  numberOfLines?: number;
}

export const ModernInput: React.FC<ModernInputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  isRequired = false,
  isInvalid = false,
  errorMessage,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  type = 'text',
  multiline = false,
  numberOfLines = 1,
}) => {
  const [showPassword, setShowPassword] = useState(false);


  const getKeyboardType = () => {
    switch (type) {
      case 'email':
        return 'email-address';
      case 'phone':
        return 'phone-pad';
      default:
        return 'default';
    }
  };

  const InputComponent = multiline ? TextArea : Input;

  return (
    <FormControl isRequired={isRequired} isInvalid={isInvalid}>
      <FormControl.Label>
        <Text fontSize="md" fontWeight="medium" color="gray.700">
          {label}
        </Text>
      </FormControl.Label>

      <InputComponent
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        keyboardType={getKeyboardType()}
        secureTextEntry={type === 'password' && !showPassword}
        numberOfLines={multiline ? numberOfLines : undefined}
        leftElement={
          leftIcon ? (
            <Icon
              as={MaterialIcons}
              name={leftIcon}
              size="sm"
              color="gray.400"
              ml={3}
            />
          ) : undefined
        }
        rightElement={
          type === 'password' ? (
            <Pressable onPress={() => setShowPassword(!showPassword)} mr={3}>
              <Icon
                as={MaterialIcons}
                name={showPassword ? 'visibility-off' : 'visibility'}
                size="sm"
                color="gray.400"
              />
            </Pressable>
          ) : rightIcon ? (
            <Pressable onPress={onRightIconPress} mr={3}>
              <Icon
                as={MaterialIcons}
                name={rightIcon}
                size="sm"
                color="gray.400"
              />
            </Pressable>
          ) : undefined
        }
        bg="white"
        borderColor="gray.200"
        borderWidth={2}
        rounded="lg"
        fontSize="md"
        _focus={{
          borderColor: 'primary.500',
          bg: 'white',
        }}
        _invalid={{
          borderColor: 'error.500',
        }}
      />

      {errorMessage && (
        <FormControl.ErrorMessage>
          {errorMessage}
        </FormControl.ErrorMessage>
      )}

      {helperText && !errorMessage && (
        <FormControl.HelperText>
          {helperText}
        </FormControl.HelperText>
      )}
    </FormControl>
  );
};

// Enhanced Select Component
interface ModernSelectProps {
  label: string;
  placeholder?: string;
  selectedValue: string;
  onValueChange: (value: string) => void;
  options: { label: string; value: string }[];
  isRequired?: boolean;
  isInvalid?: boolean;
  errorMessage?: string;
  helperText?: string;
}

export const ModernSelect: React.FC<ModernSelectProps> = ({
  label,
  placeholder = 'Select an option',
  selectedValue,
  onValueChange,
  options,
  isRequired = false,
  isInvalid = false,
  errorMessage,
  helperText,
}) => {
  return (
    <FormControl isRequired={isRequired} isInvalid={isInvalid}>
      <FormControl.Label>
        <Text fontSize="md" fontWeight="medium" color="gray.700">
          {label}
        </Text>
      </FormControl.Label>

      <Select
        selectedValue={selectedValue}
        onValueChange={onValueChange}
        placeholder={placeholder}
        bg="white"
        borderColor="gray.200"
        borderWidth={2}
        rounded="lg"
        fontSize="md"
        _selectedItem={{
          bg: 'primary.50',
          endIcon: <Icon as={MaterialIcons} name="check" size="sm" />,
        }}
        _focus={{
          borderColor: 'primary.500',
        }}
        _invalid={{
          borderColor: 'error.500',
        }}
      >
        {options.map((option) => (
          <Select.Item
            key={option.value}
            label={option.label}
            value={option.value}
          />
        ))}
      </Select>

      {errorMessage && (
        <FormControl.ErrorMessage>
          {errorMessage}
        </FormControl.ErrorMessage>
      )}

      {helperText && !errorMessage && (
        <FormControl.HelperText>
          {helperText}
        </FormControl.HelperText>
      )}
    </FormControl>
  );
};

// Form Section Component
interface FormSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
}) => {
  return (
    <Box bg="white" rounded="xl" shadow={2} p={4}>
      <VStack space={4}>
        <VStack space={1}>
          <Text fontSize="lg" fontWeight="semibold" color="gray.800">
            {title}
          </Text>
          {description && (
            <Text fontSize="sm" color="gray.600">
              {description}
            </Text>
          )}
        </VStack>
        {children}
      </VStack>
    </Box>
  );
};

// Rating Component
interface RatingProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  size?: 'sm' | 'md' | 'lg';
  isReadOnly?: boolean;
  showValue?: boolean;
}

export const Rating: React.FC<RatingProps> = ({
  rating,
  onRatingChange,
  size = 'md',
  isReadOnly = false,
  showValue = false,
}) => {
  const getIconSize = () => {
    switch (size) {
      case 'sm': return 'sm';
      case 'lg': return 'lg';
      default: return 'md';
    }
  };

  return (
    <HStack space={1} alignItems="center">
      {[1, 2, 3, 4, 5].map((star) => (
        <Pressable
          key={star}
          onPress={() => !isReadOnly && onRatingChange?.(star)}
          disabled={isReadOnly}
        >
          <Icon
            as={MaterialIcons}
            name={star <= rating ? 'star' : 'star-border'}
            color={star <= rating ? 'yellow.400' : 'gray.300'}
            size={getIconSize()}
          />
        </Pressable>
      ))}
      {showValue && (
        <Text fontSize="sm" color="gray.600" ml={2}>
          {rating}/5
        </Text>
      )}
    </HStack>
  );
};

// Search Bar Component
interface SearchBarProps {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  onClear?: () => void;
  onSubmit?: () => void;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search...',
  value,
  onChangeText,
  onClear,
  onSubmit,
}) => {
  return (
    <Input
      placeholder={placeholder}
      value={value}
      onChangeText={onChangeText}
      onSubmitEditing={onSubmit}
      leftElement={
        <Icon
          as={MaterialIcons}
          name="search"
          size="sm"
          color="gray.400"
          ml={3}
        />
      }
      rightElement={
        value.length > 0 ? (
          <Pressable onPress={onClear} mr={3}>
            <Icon
              as={MaterialIcons}
              name="close"
              size="sm"
              color="gray.400"
            />
          </Pressable>
        ) : undefined
      }
      bg="gray.50"
      borderColor="gray.200"
      borderWidth={1}
      rounded="full"
      fontSize="md"
      _focus={{
        bg: 'white',
        borderColor: 'primary.500',
      }}
    />
  );
};

// Filter Chip Component
interface FilterChipProps {
  label: string;
  isActive: boolean;
  onPress: () => void;
  count?: number;
}

export const FilterChip: React.FC<FilterChipProps> = ({
  label,
  isActive,
  onPress,
  count,
}) => {
  return (
    <Pressable onPress={onPress}>
      <Box
        bg={isActive ? 'primary.500' : 'white'}
        borderWidth={1}
        borderColor={isActive ? 'primary.500' : 'gray.300'}
        rounded="full"
        px={4}
        py={2}
        shadow={isActive ? 2 : 1}
        _pressed={{
          bg: isActive ? 'primary.600' : 'gray.50',
        }}
      >
        <Text
          fontSize="sm"
          fontWeight="medium"
          color={isActive ? 'white' : 'gray.700'}
        >
          {label}
          {count !== undefined && ` (${count})`}
        </Text>
      </Box>
    </Pressable>
  );
};

export default {
  ModernInput,
  ModernSelect,
  FormSection,
  Rating,
  SearchBar,
  FilterChip,
};