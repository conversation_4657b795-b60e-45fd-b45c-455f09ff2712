/**
 * CTRON Home Mobile App - Main Application Component
 * 
 * This is the entry point for the CTRON Home mobile application.
 * It sets up the application providers, navigation, and platform-specific configurations.
 */
import { StatusBar } from 'expo-status-bar';
import React, { useEffect } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NativeBaseProvider } from 'native-base';

import { ErrorBoundary } from './src/components/ErrorBoundary';
import { AuthProvider } from './src/context/AuthContext';
import { JobProvider } from './src/context/JobContext';
import { ThemeProvider } from './src/context/ThemeContext';
import RootNavigator from './src/navigation/RootNavigator';
import { ctronTheme } from './src/theme/nativeBaseTheme';
import { validateAndDisplayEnvironment } from './src/utils/environmentValidator';
import { notificationService } from './src/utils/notifications';
import { Platform } from './src/utils/platformUtils';
import { isWeb } from './src/utils/platformUtils';
// Import NativeBase validator in development
if (__DEV__) {
  require('./src/utils/nativeBaseValidator');
}

// Define a type for the Stripe provider component
type StripeProviderComponent = React.ComponentType<{
  publishableKey: string;
  children: React.ReactNode;
  // Add other potential props that might be needed by either implementation
  merchantIdentifier?: string;
  urlScheme?: string;
  setUrlSchemeOnAndroid?: boolean;
}>;

// Platform-specific imports with proper TypeScript handling
let StripeComponent: StripeProviderComponent;

// Use dynamic imports to handle platform-specific code
if (isWeb) {
  // Web-specific imports
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const { StripeWebProvider } = require('./src/components/StripeWebProvider');
  StripeComponent = StripeWebProvider;
  
  // Web environment setup - removed non-existent modules that were causing 'undefined' errors
} else {
  // Native-specific imports
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const { StripeProvider } = require('@stripe/stripe-react-native');
  StripeComponent = StripeProvider;
  // Setup TurboModule fix for native only
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const { setupTurboModuleFix } = require('./src/utils/turboModuleFix');
  setupTurboModuleFix();
}

/**
 * Development warning suppression
 * 
 * This section suppresses known warnings that are not relevant to the application's functionality.
 * These warnings are typically related to third-party libraries or React Native internals.
 */
if (__DEV__) {
  // Define types for console functions
  type ConsoleFunction = (...args: unknown[]) => void;
  
  // Ignore specific warnings that are known issues or not critical for development
  const originalWarn: ConsoleFunction = console.warn;
  console.warn = (...args: unknown[]) => {
    const message = args.join(' ');
    
    // List of warning patterns to ignore
    const ignoredWarnings: string[] = [
      'Non-serializable values were found in the navigation state',
      'Require cycle:',
      'Constants.manifest.extra has been deprecated',
      'ViewPropTypes will be removed from React Native',
      'new NativeEventEmitter() was called with a non-null argument without the native module',
      'EventEmitter.removeListener(',
      'Warning: componentWillMount has been renamed',
      'Warning: componentWillReceiveProps has been renamed',
      'Warning: componentWillUpdate has been renamed',
    ];

    // Add native-specific warnings to ignore
    if (Platform.OS !== 'web') {
      ignoredWarnings.push(
        'TurboModule Registry.getEnforcing',
        'SourceCode',
        'LogBox',
        'NativePerformance'
      );
    } else {
      // Web-specific warnings to ignore
      ignoredWarnings.push(
        'TurboModuleRegistry.get',
        'NativeAnimatedModule',
        'NativeAnimatedTurboModule'
      );
    }

    // Only log warnings that are not in the ignored list
    if (!ignoredWarnings.some(warning => message.includes(warning))) {
      originalWarn(...args);
    }
  };

  // Suppress TurboModule errors for debugging modules on native
  if (Platform.OS !== 'web') {
    const originalError: ConsoleFunction = console.error;
    console.error = (...args: unknown[]) => {
      const message = args.join(' ');
      
      // List of error patterns to ignore
      const ignoredErrors: string[] = [
        'TurboModule Registry.getEnforcing',
        'SourceCode',
        'LogBox',
        'NativePerformance',
      ];

      // Only log errors that are not in the ignored list
      if (!ignoredErrors.some(error => message.includes(error))) {
        originalError(...args);
      }
    };
  }
}

// Validate environment variables on app startup
validateAndDisplayEnvironment();

/**
 * Main App component that serves as the entry point for the application
 * It sets up all the necessary providers and initializes platform-specific services
 */
const App: React.FC = () => {
  console.info(`🚀 App: Component rendering for ${Platform.OS}...`);

  useEffect(() => {
    // Initialize notification service for native platforms only
    if (Platform.OS !== 'web') {
      notificationService.initialize();
    }
    
    // Return cleanup function
    return () => {
      // Perform any necessary cleanup when the app is unmounted
    };
  }, []);

  // Get the appropriate Stripe key based on platform
  const stripeKey = process.env.EXPO_PUBLIC_STRIPE_PUBLIC_KEY || '';
  
  // Log a warning if the Stripe key is missing
  if (!stripeKey) {
    console.warn('EXPO_PUBLIC_STRIPE_PUBLIC_KEY is not set. Stripe functionality will be unavailable.');
  } else if (__DEV__) {
    // In development, log that we found the key (but don't show the actual key)
    console.info('Stripe key found, first few characters:', stripeKey.substring(0, 4) + '...');
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <NativeBaseProvider theme={ctronTheme}>
          <ThemeProvider>
            <StripeComponent publishableKey={stripeKey}>
              <AuthProvider>
                <JobProvider>
                  <RootNavigator />
                  {Platform.OS !== 'web' && <StatusBar style="auto" />}
                </JobProvider>
              </AuthProvider>
            </StripeComponent>
          </ThemeProvider>
        </NativeBaseProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
};

// Styles removed as they were unused

export default App;