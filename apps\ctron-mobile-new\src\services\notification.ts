/**
 * Default Notification Service
 * 
 * This is the fallback implementation used when no platform-specific
 * implementation is available.
 */

/**
 * Shows a notification to the user
 * @param message The message to display
 * @returns A string indicating the notification was shown
 */
export function showNotification(_message: string): string {
  // Default notification shown
  return 'Notification shown using default implementation';
}

/**
 * Schedules a notification to be shown later
 * @param message The message to display
 * @param delayMs The delay in milliseconds
 * @returns A promise that resolves when the notification is scheduled
 */
export async function scheduleNotification(_message: string, _delayMs: number): Promise<string> {
  // Default notification scheduled
  return 'Notification scheduled using default implementation';
}

/**
 * Cancels all pending notifications
 * @returns A promise that resolves when notifications are canceled
 */
export async function cancelAllNotifications(): Promise<boolean> {
  // Canceling all notifications
  return true;
}

export default {
  showNotification,
  scheduleNotification,
  cancelAllNotifications
};