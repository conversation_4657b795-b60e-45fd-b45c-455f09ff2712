// CTRON Home - Technician Home Screen
// Main dashboard for technicians with design system implementation

import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useMemo } from 'react';

import type { TechnicianStackParamList } from '../../types/navigation';


import { Button, Card, Header, JobCard, StatusBadge, Screen, LoadingState, EmptyState } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { useJobs } from '../../context/JobContext';
import { useTheme } from '../../context/ThemeContext';
// Theme values are obtained from useTheme() hook
import {
  View,
  Text,
  StyleSheet,
  FlatList
} from '../../utils/platformUtils';
// ... existing code ...

function TechnicianHomeScreen() {
  const { colors, spacing, typography } = useTheme();
  const navigation = useNavigation<StackNavigationProp<TechnicianStackParamList>>();
  const { user } = useAuth();
  const { assignedJobs = [], myJobs = [], refreshJobs, loading } = useJobs();
  const availableJobs = assignedJobs; // Use assignedJobs as available jobs for technicians
  const [isAvailable, setIsAvailable] = useState(true);

  const firstName = user?.fullName?.split(' ')[0] || 'Technician';

  // Real earnings data from API
  const [earningsData, setEarningsData] = useState({
    todayEarnings: 0,
    weeklyEarnings: 0,
    completedToday: 0,
  });

  const activeJobs = myJobs.filter(
    job => job.status === 'ACCEPTED' || job.status === 'IN_PROGRESS'
  );

  // Fetch earnings data on component mount
  useEffect(() => {
    const fetchEarningsData = async () => {
      try {
        // Replace with actual API call when earnings endpoint is available
        // const earnings = await TechnicianAPI.getEarnings();
        // setEarningsData(earnings);

        // For now, calculate from completed jobs
        const completedJobs = myJobs.filter(job => job.status === 'COMPLETED');
        const today = new Date().toDateString();
        const completedToday = completedJobs.filter(
          job => new Date(job.updatedAt).toDateString() === today
        ).length;

        setEarningsData({
          todayEarnings: 0, // Will be calculated when payment data is available
          weeklyEarnings: 0, // Will be calculated when payment data is available
          completedToday,
        });
      } catch (error) {
        // Failed to fetch earnings data
      }
    };

    fetchEarningsData();
  }, [myJobs]);

  const mapJobStatus = (status: string) => {
    const statusMap: { [key: string]: 'pending' | 'assigned' | 'active' | 'completed' | 'cancelled' } = {
      'PENDING': 'pending',
      'ASSIGNED': 'assigned',
      'IN_PROGRESS': 'active',
      'COMPLETED': 'completed',
      'CANCELLED': 'cancelled',
    };
    return statusMap[status] || 'pending';
  };

  const toggleAvailability = () => {
    setIsAvailable(!isAvailable);
    // Here you would typically update the backend
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      {/* Welcome Section */}
      <View style={styles.welcomeSection}>
        <Text style={styles.welcomeText}>Good morning,</Text>
        <Text style={styles.nameText}>{firstName} 👋</Text>
      </View>

      {/* Availability Toggle */}
      <Card style={styles.availabilityCard}>
        <View style={styles.availabilityHeader}>
          <Text style={styles.availabilityTitle}>Availability Status</Text>
          <StatusBadge
            status={isAvailable ? 'available' : 'offline'}
            size="sm"
          />
        </View>
        <Button
          title={isAvailable ? 'Go Offline' : 'Go Online'}
          onPress={toggleAvailability}
          variant={isAvailable ? 'secondary' : 'primary'}
          size="md"
          fullWidth
          style={styles.availabilityButton}
        />
      </Card>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Button
          title="📋 Browse Jobs"
          onPress={() => navigation.navigate('AvailableJobs')}
          variant="primary"
          size="md"
          style={styles.quickActionButton}
        />
        <Button
          title="💰 View Earnings"
          onPress={() => navigation.navigate('Earnings')}
          variant="ghost"
          size="md"
          style={styles.quickActionButton}
        />
      </View>

      {/* Earnings Summary */}
      <View style={styles.summaryContainer}>
        <Card style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Today</Text>
          <Text style={styles.summaryValue}>£{earningsData.todayEarnings.toFixed(2)}</Text>
        </Card>

        <Card style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>This Week</Text>
          <Text style={styles.summaryValue}>£{earningsData.weeklyEarnings.toFixed(2)}</Text>
        </Card>

        <Card style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Completed</Text>
          <Text style={styles.summaryValue}>{earningsData.completedToday}</Text>
        </Card>
      </View>

      {/* Active Jobs Section */}
      {activeJobs.length > 0 && (
        <>
          <Text style={styles.sectionTitle}>Active Jobs</Text>
          {activeJobs.slice(0, 2).map((job) => {
            const jobData = {
              id: job.id,
              title: `Job #${job.id.slice(0, 8)}`,
              description: job.issue || 'Service request',
              status: mapJobStatus(job.status),
              scheduledAt: job.scheduledAt,
              location: 'Customer location',
              price: undefined,
            };

            return (
              <JobCard
                key={job.id}
                job={jobData}
                onPress={() => (navigation as { navigate: (screen: string, params: unknown) => void }).navigate('JobDetails', { jobId: job.id })}
                showTechnician={false}
                showPrice={false}
              />
            );
          })}
        </>
      )}

      {/* Available Jobs Section */}
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Available Jobs</Text>
        <Button
          title="View All"
          onPress={() => (navigation as { navigate: (screen: string) => void }).navigate('AvailableJobs')}
          variant="ghost"
          size="sm"
        />
      </View>
    </View>
  );

  const renderJobItem = ({ item }: { item: any }) => {
    const jobData = {
      id: item.id,
      title: `Job #${item.id.slice(0, 8)}`,
      description: item.issue || 'Service request',
      status: 'pending' as const,
      scheduledAt: item.scheduledAt,
      location: item.address || 'Customer location',
      price: undefined,
    };

    return (
      <JobCard
        job={jobData}
        onPress={() => navigation.navigate('JobDetails', { jobId: item.id })}
        showTechnician={false}
        showPrice={false}
      />
    );
  };

  const renderEmptyComponent = () => (
    <EmptyState
      title="No available jobs"
      message={isAvailable
        ? 'Check back later for new job opportunities'
        : 'Go online to see available jobs in your area'
      }
      icon="🔧"
      actionTitle={!isAvailable ? "Go Online" : undefined}
      onAction={!isAvailable ? toggleAvailability : undefined}
    />
  );

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.systemBackground,
    },

    contentContainer: {
      padding: spacing.md,
      paddingBottom: spacing.xxxl,
    },

    headerContainer: {
      marginBottom: spacing.xl,
    },

    welcomeSection: {
      marginBottom: spacing.xl,
    },
    welcomeText: {
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    nameText: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    availabilityCard: {
      marginBottom: spacing.md,
      padding: spacing.md,
    },
    availabilityHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    availabilityTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    availabilityButton: {
      minWidth: 100,
    },
    quickActionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.md,
    },
    quickActionButton: {
      flex: 1,
      marginHorizontal: spacing.xs,
    },
    summaryContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.md,
    },
    summaryCard: {
      flex: 1,
      marginHorizontal: spacing.xs,
      padding: spacing.md,
      alignItems: 'center',
    },
    summaryLabel: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: spacing.xs,
    },
    summaryValue: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    sectionTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    emptyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.bold,
      color: colors.text.primary,
      marginBottom: spacing.sm,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: typography.fontSize.base,
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: 24,
      marginBottom: spacing.lg,
    },
    emptyButton: {
      minWidth: 120,
    },
    menuIcon: {
      fontSize: 20,
      color: colors.text.primary,
    },
  }), [colors, spacing, typography]);

  if (loading) {
    return (
      <Screen>
        <Header
          title="CTRON Technician"
          rightAction={{
            icon: <Text style={styles.menuIcon}>☰</Text>,
            onPress: () => {/* Open drawer or menu */ },
            accessibilityLabel: "Menu",
          }}
        />
        <LoadingState message="Loading your dashboard..." />
      </Screen>
    );
  }

  return (
    <Screen>
      <Header
        title="CTRON Technician"
        rightAction={{
          icon: <Text style={styles.menuIcon}>☰</Text>,
          onPress: () => {/* Open drawer or menu */ },
          accessibilityLabel: "Menu",
        }}
      />
      <FlatList
        data={availableJobs.slice(0, 5)} // Show only first 5 available jobs
        keyExtractor={(item: any) => item.id}
        renderItem={renderJobItem}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyComponent}
        refreshControl={null}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      />
    </Screen>
  );
}


export default TechnicianHomeScreen;
