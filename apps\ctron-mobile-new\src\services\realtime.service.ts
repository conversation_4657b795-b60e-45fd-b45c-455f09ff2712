// CTRON Home - Real-time Updates Service
// Comprehensive real-time job and notification management using Socket.IO

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';

import { SocketService } from './socket.service';

/**
 * Interface for real-time job update
 */
interface JobUpdate {
  jobId: string;
  status: 'assigned' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  technicianId: string;
  customerId: string;
  timestamp: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  message?: string;
  estimatedArrival?: string;
}

/**
 * Interface for real-time notification
 */
interface MessageData {
  messageId: string;
  chatId: string;
  senderName: string;
  message: string;
}

interface EmergencyData {
  alertId: string;
  type: 'fire' | 'medical' | 'security' | 'other';
  location: string;
  description: string;
}

interface NotificationData {
  messageId?: string;
  chatId?: string;
  jobId?: string;
  alertId?: string;
  [key: string]: unknown;
}

interface RealtimeNotification {
  id: string;
  type: 'job_assigned' | 'job_updated' | 'message_received' | 'emergency' | 'schedule_reminder';
  title: string;
  message: string;
  data?: NotificationData;
  timestamp: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

/**
 * Interface for location update
 */
interface LocationUpdate {
  technicianId: string;
  jobId?: string;
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: string;
  speed?: number;
  heading?: number;
}

/**
 * Type for event listeners
 */
type EventListener<T = unknown> = (data: T) => void;

/**
 * Real-time service for managing Socket.IO events and notifications
 */
class RealtimeService {
  private listeners: Map<string, EventListener<any>[]> = new Map();
  private isInitialized: boolean = false;
  private notificationQueue: RealtimeNotification[] = [];

  /**
   * Initialize the real-time service
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Configure notifications
    await this.configureNotifications();

    // Set up Socket.IO event listeners
    this.setupSocketListeners();

    this.isInitialized = true;
    // Real-time service initialized
  }

  /**
   * Configure notification settings
   */
  private async configureNotifications(): Promise<void> {
    try {
      await Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
          shouldShowBanner: true,
          shouldShowList: true,
        }),
      });

      // Request notification permissions
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
        // Notification permissions not granted
      }
    } catch (error) {
      // Failed to configure notifications
    }
  }

  /**
   * Set up Socket.IO event listeners
   */
  private setupSocketListeners(): void {
    // Job updates - commented out until socket events are properly defined
    // SocketService.on('job:updated', this.handleJobUpdate.bind(this));
    // SocketService.on('job:assigned', this.handleJobAssigned.bind(this));
    // SocketService.on('job:cancelled', this.handleJobCancelled.bind(this));

    // Location updates - commented out until socket events are properly defined
    // SocketService.on('location:request', this.handleLocationRequest.bind(this));
    // SocketService.on('location:update', this.handleLocationUpdate.bind(this));

    // Messages and notifications - commented out until socket events are properly defined
    // SocketService.on('message:received', this.handleMessageReceived.bind(this));
    // SocketService.on('notification:push', this.handlePushNotification.bind(this));

    // Emergency alerts - commented out until socket events are properly defined
    // SocketService.on('emergency:alert', this.handleEmergencyAlert.bind(this));

    // Connection events
    SocketService.on('connect', this.handleConnect.bind(this));
    SocketService.on('disconnect', this.handleDisconnect.bind(this));
  }

  /**
   * Handle job update events
   */
  private async handleJobUpdate(data: JobUpdate): Promise<void> {
    try {
      // Job update received

      // Store update locally
      await this.storeJobUpdate(data);

      // Emit to listeners
      this.emit('jobUpdate', data);

      // Show notification if app is in background
      if (data.status === 'assigned' || data.status === 'cancelled') {
        await this.showNotification({
          id: `job_${data.jobId}_${Date.now()}`,
          type: 'job_updated' as const,
          title: `Job ${data.status}`,
          message: `Job #${data.jobId} has been ${data.status}`,
          data: { jobId: data.jobId },
          timestamp: new Date().toISOString(),
          priority: 'normal' as const,
        });
      }
    } catch (error) {
      // Failed to handle job update
    }
  }

  /**
   * Handle job assigned events
   */
  private async handleJobAssigned(data: JobUpdate): Promise<void> {
    try {
      // New job assigned

      // Store assignment locally
      await this.storeJobUpdate(data);

      // Emit to listeners
      this.emit('jobAssigned', data);

      // Show high-priority notification
      await this.showNotification({
        id: `job_assigned_${data.jobId}`,
        type: 'job_assigned' as const,
        title: 'New Job Assigned!',
        message: `You have been assigned a new job. Tap to view details.`,
        data: { jobId: data.jobId },
        timestamp: new Date().toISOString(),
        priority: 'high' as const,
      });
    } catch (error) {
      // Failed to handle job assignment
    }
  }

  /**
   * Handle job cancelled events
   */
  private async handleJobCancelled(data: JobUpdate): Promise<void> {
    try {
      // Job cancelled

      // Store cancellation locally
      await this.storeJobUpdate(data);

      // Emit to listeners
      this.emit('jobCancelled', data);

      // Show notification
      await this.showNotification({
        id: `job_cancelled_${data.jobId}`,
        type: 'job_updated',
        title: 'Job Cancelled',
        message: `Job #${data.jobId} has been cancelled by the customer.`,
        data: { jobId: data.jobId },
        timestamp: new Date().toISOString(),
        priority: 'normal',
      });
    } catch (error) {
      // Failed to handle job cancellation
    }
  }

  /**
   * Handle location request events
   */
  private handleLocationRequest(data: { jobId: string; requestId: string }): void {
    // Location request received
    this.emit('locationRequest', data);
  }

  /**
   * Handle location update events
   */
  private handleLocationUpdate(data: LocationUpdate): void {
    // Location update received
    this.emit('locationUpdate', data);
  }

  /**
   * Handle message received events
   */
  private async handleMessageReceived(data: unknown): Promise<void> {
    try {
      // Type guard to ensure data has the expected structure
      if (!this.isMessageData(data)) {
        console.warn('Invalid message data received:', data);
        return;
      }

      // Emit to listeners
      this.emit('messageReceived', data);

      // Show notification
      await this.showNotification({
        id: `message_${data.messageId}`,
        type: 'message_received' as const,
        title: `Message from ${data.senderName}`,
        message: data.message,
        data: { messageId: data.messageId, chatId: data.chatId },
        timestamp: new Date().toISOString(),
        priority: 'normal' as const,
      });
    } catch (error) {
      // Failed to handle message received
    }
  }

  /**
   * Handle push notification events
   */
  private async handlePushNotification(data: RealtimeNotification): Promise<void> {
    try {
      // Push notification received

      // Show notification
      await this.showNotification(data);

      // Emit to listeners
      this.emit('pushNotification', data);
    } catch (error) {
      // Failed to handle push notification
    }
  }

  /**
   * Handle emergency alert events
   */
  /**
   * Type guard for MessageData
   */
  private isMessageData(data: unknown): data is MessageData {
    return (
      typeof data === 'object' &&
      data !== null &&
      'messageId' in data &&
      'chatId' in data &&
      'senderName' in data &&
      'message' in data &&
      typeof (data as MessageData).messageId === 'string' &&
      typeof (data as MessageData).chatId === 'string' &&
      typeof (data as MessageData).senderName === 'string' &&
      typeof (data as MessageData).message === 'string'
    );
  }

  /**
   * Type guard for EmergencyData
   */
  private isEmergencyData(data: unknown): data is EmergencyData {
    return (
      typeof data === 'object' &&
      data !== null &&
      'alertId' in data &&
      'type' in data &&
      'location' in data &&
      'description' in data &&
      typeof (data as EmergencyData).alertId === 'string' &&
      typeof (data as EmergencyData).type === 'string' &&
      typeof (data as EmergencyData).location === 'string' &&
      typeof (data as EmergencyData).description === 'string'
    );
  }

  /**
   * Handle emergency alert events
   */
  private async handleEmergencyAlert(data: unknown): Promise<void> {
    try {
      // Type guard to ensure data has the expected structure
      if (!this.isEmergencyData(data)) {
        console.warn('Invalid emergency data received:', data);
        return;
      }

      // Emergency alert received
      // Emit to listeners
      this.emit('emergencyAlert', data);

      // Show urgent notification
      await this.showNotification({
        id: `emergency_${Date.now()}`,
        type: 'emergency' as const,
        title: 'Emergency Alert',
        message: data.description || 'Emergency situation detected',
        data: { alertId: data.alertId, type: data.type, location: data.location },
        timestamp: new Date().toISOString(),
        priority: 'urgent' as const,
      });
    } catch (error) {
      // Failed to handle emergency alert
    }
  }

  /**
   * Handle connection events
   */
  private handleConnect(): void {
    // Real-time service connected
    this.emit('connected', {});
  }

  /**
   * Handle disconnection events
   */
  private handleDisconnect(): void {
    // Real-time service disconnected
    this.emit('disconnected', {});
  }

  /**
   * Show local notification
   */
  private async showNotification(notification: RealtimeNotification): Promise<void> {
    try {
      // Check if notifications are enabled
      const settings = await AsyncStorage.getItem('technician_settings');
      if (settings) {
        const parsedSettings = JSON.parse(settings);
        if (!parsedSettings.notifications?.pushEnabled) {
          return;
        }
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.message,
          data: notification.data,
          priority: notification.priority === 'urgent' ? 'high' : 'default',
        },
        trigger: null, // Show immediately
      });
    } catch (error) {
      // Failed to show notification
    }
  }

  /**
   * Store job update locally
   */
  private async storeJobUpdate(update: JobUpdate): Promise<void> {
    try {
      const key = `job_update_${update.jobId}`;
      const existingUpdates = await AsyncStorage.getItem(key);
      const updates = existingUpdates ? JSON.parse(existingUpdates) : [];

      updates.push(update);

      // Keep only last 10 updates per job
      if (updates.length > 10) {
        updates.splice(0, updates.length - 10);
      }

      await AsyncStorage.setItem(key, JSON.stringify(updates));
    } catch (error) {
      // Failed to store job update
    }
  }

  /**
   * Send location update
   */
  public sendLocationUpdate(_location: LocationUpdate): void {
    try {
      // SocketService.emit('technician:locationUpdate', location); // Commented out until event is defined
      // Location update sent
    } catch (error) {
      // Failed to send location update
    }
  }

  /**
   * Send job status update
   */
  public sendJobStatusUpdate(jobId: string, status: string, data?: Record<string, unknown>): void {
    try {
      const update = {
        jobId,
        status,
        timestamp: new Date().toISOString(),
        ...data,
      };

      // SocketService.emit('technician:jobStatusUpdate', update); // Commented out until event is defined
      // Job status update sent
    } catch (error) {
      // Failed to send job status update
    }
  }

  /**
   * Add event listener
   */
  public on<T = unknown>(event: string, listener: EventListener<T>): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener as EventListener<any>);
  }

  /**
   * Remove event listener
   */
  public off(event: string, listener: EventListener<any>): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  private emit<T = unknown>(event: string, data: T): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          // Error in event listener
        }
      });
    }
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    this.listeners.clear();
    this.isInitialized = false;
    // Real-time service cleaned up
  }
}

// Export singleton instance
export const realtimeService = new RealtimeService();
export default realtimeService;
