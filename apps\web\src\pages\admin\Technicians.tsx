// 📁 File: apps/web/src/pages/admin/Technicians.tsx

import { useEffect, useState, useCallback } from 'react';
import { LoadingState } from '../../components/ui/LoadingState';
import { EmptyState } from '../../components/ui/EmptyState';
import { PageContainer } from '../../components/ui/PageContainer';
import { AdminAPI, Technician } from '../../api/admin.api';
import useSocket from '../../hooks/useSocket';
import { toast } from 'react-toastify';

// Technician interface is now imported from AdminAPI

const Technicians = () => {
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch initial list of pending technicians
  const fetchTechnicians = useCallback(async () => {
    try {
      const pendingTechnicians = await AdminAPI.getPendingTechnicians();
      setTechnicians(pendingTechnicians);
    } catch (err: any) {
      console.error('Failed to fetch technicians:', err);
      toast.error('Failed to load technicians');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTechnicians();
  }, [fetchTechnicians]);

  // Handle technician update from socket
  const handleTechnicianUpdate = useCallback((updatedTech: Technician) => {
    setTechnicians((prev) => {
      const exists = prev.find((t) => t.id === updatedTech.id);
      if (!exists && updatedTech.status === 'PENDING') {
        return [...prev, updatedTech];
      }
      return prev.map((t) => (t.id === updatedTech.id ? updatedTech : t));
    });
  }, []);

  // Set up socket listener
  useSocket(handleTechnicianUpdate);

  // Approve technician via API
  const approveTechnician = async (id: string) => {
    try {
      await AdminAPI.approveTechnician(id);
      setTechnicians((prev) => prev.filter((t) => t.id !== id));
      toast.success('Technician approved successfully');
    } catch (err: any) {
      console.error('Approval failed:', err);
      toast.error('Failed to approve technician');
    }
  };

  return (
    <PageContainer>
      <div className="space-y-6 md:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Technician Management</h1>
          <p className="text-gray-600 mt-1">Review and approve pending technician applications</p>
        </div>
        <div className="flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium self-start sm:self-auto">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          {technicians.length} Pending
        </div>
      </div>

        {/* Content */}
        <div className="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl shadow-lg overflow-hidden">
          {loading ? (
            <LoadingState message="Loading technicians..." />
          ) : technicians.length === 0 ? (
            <EmptyState
              title="No pending technicians"
              message="All technician applications have been processed."
              actionTitle="Refresh"
              onAction={fetchTechnicians}
            />
          ) : (
            <div className="divide-y divide-gray-200/50">
              {technicians.map((tech, index) => (
                <div
                  key={tech.id}
                  className="p-4 md:p-6 hover:bg-gray-50/50 transition-colors duration-200"
                >
                  <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-semibold text-base md:text-lg flex-shrink-0">
                        {tech.user.fullName.charAt(0)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-base md:text-lg font-semibold text-gray-900 truncate">{tech.user.fullName}</h3>
                        <div className="flex flex-col xs:flex-row xs:items-center gap-1 xs:gap-4 mt-1">
                          <span className="text-xs md:text-sm text-gray-600 flex items-center gap-1 truncate">
                            <svg className="w-3 h-3 md:w-4 md:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <span className="truncate">{tech.user.email}</span>
                          </span>
                          <span className="text-xs md:text-sm text-gray-600 flex items-center gap-1">
                            <svg className="w-3 h-3 md:w-4 md:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            {tech.user.phone || 'Not provided'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between sm:justify-end gap-3 sm:flex-shrink-0">
                      <span className="px-2 py-1 md:px-3 md:py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs md:text-sm font-medium">
                        {tech.status}
                      </span>
                      <button
                        onClick={() => approveTechnician(tech.id)}
                        className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-4 py-2 md:px-6 md:py-2 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 text-sm md:text-base min-h-touch"
                      >
                        <svg className="w-3 h-3 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="hidden xs:inline">Approve</span>
                        <span className="xs:hidden">✓</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </PageContainer>
  );
};

export default Technicians;
