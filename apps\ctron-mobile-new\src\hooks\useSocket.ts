// src/hooks/useSocket.ts

import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';

import { SOCKET_URL } from '../config';
import { useAuth } from '../context/AuthContext';

export const useSocket = () => {
  const { user, token } = useAuth(); // Get user and token from AuthContext
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (token && user && !socketRef.current) {
      socketRef.current = io(SOCKET_URL, {
        auth: {
          token: token,
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });

      socketRef.current.on('connect', () => {
        if (__DEV__) {
          console.log('🟢 Connected to socket server');
        }
      });

      socketRef.current.on('disconnect', () => {
        if (__DEV__) {
          console.log('🔴 Disconnected from socket server');
        }
      });
    }

    return () => {
      socketRef.current?.disconnect();
      socketRef.current = null;
    };
  }, [token, user]);

  return socketRef.current;
};
