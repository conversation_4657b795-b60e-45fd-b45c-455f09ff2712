// CTRON Home Design System - Card Component
// Standardized card container with consistent styling

import React from 'react';
import { View, ViewStyle } from '../../utils/platformUtils';
import { tokens } from '../tokens';

export interface CardProps {
  children: React.ReactNode;
  variant?: 'elevated' | 'outlined' | 'filled';
  padding?: keyof typeof tokens.spacing;
  borderRadius?: keyof typeof tokens.borderRadius;
  shadow?: keyof typeof tokens.shadows;
  backgroundColor?: string;
  style?: ViewStyle;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'elevated',
  padding = 4,
  borderRadius = 'lg',
  shadow = 'md',
  backgroundColor,
  style,
}) => {
  const getVariantStyles = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: tokens.borderRadius[borderRadius],
      padding: tokens.spacing[padding],
    };

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyle,
          backgroundColor: backgroundColor || tokens.colors.neutral[0],
          ...tokens.shadows[shadow],
        };
      
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: backgroundColor || 'transparent',
          borderWidth: 1,
          borderColor: tokens.colors.neutral[200],
        };
      
      case 'filled':
        return {
          ...baseStyle,
          backgroundColor: backgroundColor || tokens.colors.neutral[50],
        };
      
      default:
        return baseStyle;
    }
  };

  const cardStyle: ViewStyle = {
    ...getVariantStyles(),
    ...style,
  };

  return (
    <View style={cardStyle}>
      {children}
    </View>
  );
};

// Preset card variants for common use cases
export const HeroCard: React.FC<Omit<CardProps, 'shadow' | 'borderRadius'>> = (props) => (
  <Card shadow="lg" borderRadius="xl" {...props} />
);

export const ContentCard: React.FC<Omit<CardProps, 'variant'>> = (props) => (
  <Card variant="elevated" {...props} />
);

export const ActionCard: React.FC<Omit<CardProps, 'variant' | 'shadow'>> = (props) => (
  <Card variant="outlined" shadow="sm" {...props} />
);